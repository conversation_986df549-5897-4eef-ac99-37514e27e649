import { NextRequest } from 'next/server'
import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { withSupabaseDashboardMiddleware } from '@/lib/api/supabase-auth-middleware'
import { withErrorHandler, withLogging, withRateLimit } from '@/lib/api/middleware'
import { RequestValidator, ValidationSchema } from '@/lib/api/validation'

// Validation schema for update post type request
const updatePostTypeSchema: ValidationSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Name cannot be empty or only whitespace'
      if (trimmed.length < 2) return 'Name must be at least 2 characters long'
      return true
    }
  },
  slug: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-z0-9-]+$/,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Slug cannot be empty'
      if (trimmed.startsWith('-') || trimmed.endsWith('-')) {
        return 'Slug cannot start or end with hyphen'
      }
      if (trimmed.includes('--')) {
        return 'Slug cannot contain consecutive hyphens'
      }
      return true
    }
  },
  color: {
    required: true,
    type: 'string',
    pattern: /^#[0-9A-Fa-f]{6}$/,
    custom: (value: string) => {
      if (!value?.trim()) return 'Color is required'
      return true
    }
  },
  icon: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 50,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Icon is required'
      return true
    }
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500
  }
}

// Validation schema for post type ID parameter
const postTypeIdSchema: ValidationSchema = {
  id: {
    required: true,
    type: 'string',
    pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
    custom: (value: string) => {
      if (!value?.trim()) return 'Post type ID is required'
      return true
    }
  }
}

async function updatePostTypeHandler(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    // Validate post type ID
    const idValidation = RequestValidator.validate({ id }, postTypeIdSchema)
    if (!idValidation.isValid) {
      return ApiResponseBuilder.validationError(
        'Invalid post type ID format',
        'id',
        { errors: idValidation.errors }
      )
    }

    // Validate request body
    const validation = await RequestValidator.validateBody(request, updatePostTypeSchema)
    
    if (!validation.isValid) {
      console.error('Validation errors:', validation.errors)
      return ApiResponseBuilder.validationError(
        'Validation failed',
        validation.errors[0]?.field,
        { errors: validation.errors }
      )
    }

    const { name, slug, color, icon, description } = validation.data!

    const supabase = createServerSupabaseAdminClient()

    // Check if post type exists
    const { data: existingType } = await supabase
      .from('post_types')
      .select('id, slug')
      .eq('id', id)
      .single()

    if (!existingType) {
      return ApiResponseBuilder.notFound('Post type not found')
    }

    // Check if slug already exists (excluding current post type)
    if (slug !== existingType.slug) {
      const { data: slugExists } = await supabase
        .from('post_types')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .single()

      if (slugExists) {
        return ApiResponseBuilder.badRequest('A post type with this slug already exists')
      }
    }

    // Update post type
    const { data: updatedType, error } = await supabase
      .from('post_types')
      .update({
        name: name.trim(),
        slug: slug.trim(),
        color: color.trim(),
        icon: icon.trim(),
        description: description?.trim() || '',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        posts:posts(count)
      `)
      .single()

    if (error) {
      console.error('Supabase error updating post type:', error)
      return ApiResponseBuilder.databaseError('Failed to update post type', { supabaseError: error })
    }

    // Transform to frontend format
    const transformedType = {
      id: updatedType.id,
      name: updatedType.name,
      slug: updatedType.slug,
      color: updatedType.color,
      icon: updatedType.icon,
      description: updatedType.description || '',
      postCount: updatedType.posts?.[0]?.count || 0,
      createdAt: updatedType.created_at,
      updatedAt: updatedType.updated_at
    }

    return ApiResponseBuilder.success(
      transformedType,
      'Post type updated successfully'
    )
  } catch (error) {
    console.error('Error updating post type:', error)
    return ApiResponseBuilder.internalError('Failed to update post type', { error: error instanceof Error ? error.message : 'Unknown error' })
  }
}

async function deletePostTypeHandler(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    // Validate post type ID
    const idValidation = RequestValidator.validate({ id }, postTypeIdSchema)
    if (!idValidation.isValid) {
      return ApiResponseBuilder.validationError(
        'Invalid post type ID format',
        'id',
        { errors: idValidation.errors }
      )
    }

    const supabase = createServerSupabaseAdminClient()

    // Check if post type exists and has posts
    const { data: postType, error: fetchError } = await supabase
      .from('post_types')
      .select(`
        id,
        name,
        posts:posts(count)
      `)
      .eq('id', id)
      .single()

    if (fetchError || !postType) {
      return ApiResponseBuilder.notFound('Post type not found')
    }

    // Check if post type has associated posts
    const postCount = postType.posts?.[0]?.count || 0
    if (postCount > 0) {
      return ApiResponseBuilder.badRequest(
        `Cannot delete post type "${postType.name}" because it has ${postCount} associated posts. Please reassign or delete the posts first.`
      )
    }

    // Delete post type
    const { error: deleteError } = await supabase
      .from('post_types')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Supabase error deleting post type:', deleteError)
      return ApiResponseBuilder.databaseError('Failed to delete post type', { supabaseError: deleteError })
    }

    return ApiResponseBuilder.success(
      null,
      `Post type "${postType.name}" deleted successfully`
    )
  } catch (error) {
    console.error('Error deleting post type:', error)
    return ApiResponseBuilder.internalError('Failed to delete post type', { error: error instanceof Error ? error.message : 'Unknown error' })
  }
}

// Apply dashboard middleware with authentication
export const PUT = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(20, 15 * 60 * 1000) // 20 requests per 15 minutes for update operations
)(updatePostTypeHandler)

export const DELETE = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(10, 15 * 60 * 1000) // 10 requests per 15 minutes for delete operations
)(deletePostTypeHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}