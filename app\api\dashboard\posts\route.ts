import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { RequestValidator } from '@/lib/api/validation'
import { withSupabaseDashboardMiddleware, getUserFromSupabaseRequest } from '@/lib/api/supabase-auth-middleware'
import { withErrorHandler, withLogging, withRateLimit } from '@/lib/api/middleware'
import { PaginationOptions } from '@/types/api'
import { PostFilters, PostStatus } from '@/types'
import { z } from 'zod'

const postService = new PostApiSimpleService()

// Validation schema for post images
const postImageSchema = z.object({
  url: z.string()
    .min(1, 'Image URL is required'),
  altText: z.string()
    .max(125, 'Alt text must be less than 125 characters')
    .optional(),
  caption: z.string()
    .max(300, 'Caption must be less than 300 characters')
    .optional(),
  name: z.string()
    .min(1, 'Image name is required'),
  size: z.number()
    .min(1, 'Image size must be greater than 0')
})

// Validation schema for create post request
const createPostSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  slug: z.string()
    .min(1, 'Slug is required')
    .max(200, 'Slug must be less than 200 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  content: z.string()
    .min(1, 'Content is required')
    .max(50000, 'Content must be less than 50,000 characters'),
  typeId: z.string()
    .min(1, 'Post type is required'),
  status: z.nativeEnum(PostStatus)
    .default(PostStatus.DRAFT),
  featured: z.boolean()
    .default(false),
  showFullContent: z.boolean()
    .default(false),
  version: z.string()
    .min(1, 'Version is required')
    .max(20, 'Version must be less than 20 characters')
    .default('1.0.0'),
  tags: z.array(z.string())
    .default([]),
  images: z.array(postImageSchema)
    .max(10, 'Maximum 10 images allowed')
    .default([])
})

// Validation schema for query parameters
const querySchema = {
  page: {
    type: 'number' as const,
    min: 1,
  },
  limit: {
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  search: {
    type: 'string' as const,
    maxLength: 255,
  },
  type: {
    type: 'string' as const,
    maxLength: 50,
  },
  status: {
    type: 'string' as const,
    enum: ['draft', 'published', 'archived'],
  },
  author: {
    type: 'string' as const,
    maxLength: 100,
  },
  featured: {
    type: 'boolean' as const,
  },
  sortBy: {
    type: 'string' as const,
    enum: ['created_at', 'updated_at', 'title', 'view_count'],
  },
  sortOrder: {
    type: 'string' as const,
    enum: ['asc', 'desc'],
  },
}

async function getPostsHandler(request: NextRequest) {
  // Debug: Log received query parameters
  const url = new URL(request.url)
  const queryParams = Object.fromEntries(url.searchParams.entries())
  console.log('Received query params:', queryParams)

  // Validate query parameters
  const validation = RequestValidator.validateQuery(request, querySchema)

  if (!validation.isValid) {
    console.error('Validation errors:', validation.errors)
    console.error('Query schema:', querySchema)
    return ApiResponseBuilder.validationError(
      'Invalid query parameters',
      validation.errors[0]?.field,
      { errors: validation.errors }
    )
  }

  // Extract pagination options
  const pagination: PaginationOptions = {
    page: validation.data?.page || 1,
    limit: validation.data?.limit || 10,
    sortBy: validation.data?.sortBy || 'created_at',
    sortOrder: validation.data?.sortOrder || 'desc',
  }

  // Extract filter options
  const filters: PostFilters = {
    search: validation.data?.search,
    type: validation.data?.type,
    status: validation.data?.status,
    authorId: validation.data?.author,
    featured: validation.data?.featured,
  }

  // Get posts from service
  const result = await postService.getAllPosts(pagination, filters)



  // Calculate pagination metadata
  const totalPages = Math.ceil(result.total / pagination.limit!)
  const paginationMeta = {
    page: pagination.page!,
    limit: pagination.limit!,
    total: result.total,
    totalPages,
    hasNext: pagination.page! < totalPages,
    hasPrev: pagination.page! > 1,
  }

  return ApiResponseBuilder.success(
    {
      posts: result.posts,
      pagination: paginationMeta,
      filters
    },
    `Retrieved ${result.posts.length} posts`,
    200
  )
}

async function createPostHandler(request: NextRequest) {
  try {
    console.log('📝 Creating new post...')

    // Parse request body
    const body = await request.json()
    console.log('Request body:', body)

    // Validate request data
    const validatedData = createPostSchema.parse(body)
    console.log('Validated data:', validatedData)

    // Get authenticated user from request
    const user = getUserFromSupabaseRequest(request)
    if (!user) {
      return ApiResponseBuilder.unauthorized('Authentication required')
    }
    console.log('Authenticated user:', user.id)

    // Create post using service
    const postData = {
      title: validatedData.title,
      slug: validatedData.slug,
      content: validatedData.content,
      authorId: user.id,
      typeId: validatedData.typeId,
      status: validatedData.status,
      featured: validatedData.featured || false,
      excerpt: validatedData.showFullContent ? false : true, // Invert showFullContent to excerpt
      version: validatedData.version,
      tags: validatedData.tags || [],
      images: validatedData.images || []
    }

    console.log('Creating post with data:', {
      ...postData,
      images: postData.images.map(img => ({
        name: img.name,
        size: img.size,
        hasAltText: !!img.altText,
        hasCaption: !!img.caption
      }))
    })

    const createdPost = await postService.createPost(postData)
    console.log('Post created successfully:', createdPost.id)

    return ApiResponseBuilder.success(
      createdPost,
      `Post ${validatedData.status === 'PUBLISHED' ? 'published' : 'saved as draft'} successfully`,
      201
    )
  } catch (error) {
    console.error('Error in createPostHandler:', error)

    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(
        'Validation failed',
        error.errors[0]?.path.join('.') || 'unknown',
        { errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        })) }
      )
    }

    if (error instanceof Error) {
      // Handle specific error messages
      if (error.message.includes('Slug is already taken')) {
        return ApiResponseBuilder.validationError(
          'Slug is already taken',
          'slug',
          { errors: [{ field: 'slug', message: error.message }] }
        )
      }

      return ApiResponseBuilder.badRequest(error.message)
    }

    return ApiResponseBuilder.internalError('Failed to create post')
  }
}

// Apply dashboard middleware with authentication
export const GET = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(500, 15 * 60 * 1000) // 500 requests per 15 minutes
)(getPostsHandler)

export const POST = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(100, 15 * 60 * 1000) // 100 requests per 15 minutes for creation
)(createPostHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
