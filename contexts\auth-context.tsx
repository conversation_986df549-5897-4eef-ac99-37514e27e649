"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@/types'
import { setAuthCookie, clearAuth<PERSON>ookie, getAuthCookie } from '@/lib/utils/cookies'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  token: string | null
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!token

  // Load token from localStorage and cookies on mount
  useEffect(() => {
    const savedToken = localStorage.getItem('supabase_token') || getAuthCookie()
    if (savedToken) {
      setToken(savedToken)
      localStorage.setItem('supabase_token', savedToken)
      setAuthCookie(savedToken)
    }
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      setIsLoading(true)
      console.log('Checking authentication...')

      const currentToken = token || localStorage.getItem('supabase_token')
      if (!currentToken) {
        console.log('No token found')
        setUser(null)
        setToken(null)
        return
      }

      const response = await fetch('/api/auth/supabase/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${currentToken}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      console.log('Auth check response:', { status: response.status, ok: response.ok })

      if (response.ok) {
        const data = await response.json()
        console.log('Auth check data:', data)

        if (data.success && data.data?.user) {
          console.log('User authenticated:', data.data.user)
          setUser(data.data.user)
          setToken(currentToken)
        } else {
          console.log('No user in response')
          setUser(null)
          setToken(null)
          localStorage.removeItem('supabase_token')
          clearAuthCookie()
        }
      } else {
        console.log('Auth check failed with status:', response.status)
        setUser(null)
        setToken(null)
        localStorage.removeItem('supabase_token')
        clearAuthCookie()
      }
    } catch (error) {
      console.error('Auth check error:', error)
      setUser(null)
      setToken(null)
      localStorage.removeItem('supabase_token')
      clearAuthCookie()
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/supabase/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (data.success && data.data?.user && data.data?.session?.access_token) {
        const accessToken = data.data.session.access_token
        setUser(data.data.user)
        setToken(accessToken)
        localStorage.setItem('supabase_token', accessToken)
        setAuthCookie(accessToken)
        return { success: true }
      } else {
        return {
          success: false,
          message: data.error?.message || data.message || 'Login failed'
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: 'Network error. Please try again.' }
    }
  }

  const logout = async () => {
    try {
      if (token) {
        await fetch('/api/auth/supabase/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
      setToken(null)
      localStorage.removeItem('supabase_token')
      clearAuthCookie()
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    token,
    login,
    logout,
    checkAuth,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
