"use client"

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { InputField } from '@/components/dashboard/input-field'
import * as LucideIcons from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { useAuth } from '@/contexts/auth-context'
import { useRouter } from 'next/navigation'
import React from 'react'
import { IconPickerContent } from '@/components/ui/icon-picker'
import { Icon, IconName } from '@/components/ui/icon'
import { ColorPicker } from '@/components/ui/color-picker'
import { toast } from '@/hooks/use-toast'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface PostType {
  id?: string
  name: string
  slug: string
  color: string
  icon: string // This will store the Lucide icon name
  description: string
  postCount?: number
}

export default function PostTypesPage() {
  const [postTypes, setPostTypes] = useState<PostType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [editingType, setEditingType] = useState<PostType | null>(null)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState<PostType>({
    name: '',
    slug: '',
    color: '#3B82F6',
    icon: 'FileText',
    description: ''
  })
  const { token, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [iconDialogOpen, setIconDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingTypeId, setDeletingTypeId] = useState<string | null>(null)

  useEffect(() => {
    // Wait for auth to finish loading
    if (isLoading) {
      return
    }

    // Only redirect if we're sure user is not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
      return
    }

    // Only fetch if we have token and auth is ready
    if (isAuthenticated && token) {
      const fetchPostTypes = async () => {
        setLoading(true)
        setError(null)
        try {
          const response = await fetch('/api/dashboard/post-types', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            credentials: 'include',
          })
          if (!response.ok) {
            if (response.status === 401) {
              router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
              return
            }
            throw new Error('Failed to fetch post types')
          }
          const data = await response.json()
          setPostTypes(data.data || [])
        } catch (err: any) {
          setError(err.message || 'Unknown error')
        } finally {
          setLoading(false)
        }
      }
      fetchPostTypes()
    }
  }, [isAuthenticated, token, isLoading, router])

  const filteredTypes = postTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleEdit = (type: PostType) => {
    setEditingType(type)
    setFormData({
      name: type.name,
      slug: type.slug,
      color: type.color,
      icon: type.icon,
      description: type.description
    })
    setFieldErrors({})
    setIsEditing(true)
  }

  const handleDeleteClick = (id: string) => {
    setDeletingTypeId(id)
    setDeleteDialogOpen(true)
  }

  const handleDelete = async () => {
    if (!deletingTypeId || !token) {
      setError('Authentication required')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/dashboard/post-types/${deletingTypeId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
          return
        }
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to delete post type')
      }

      // Remove from local state
      setPostTypes(postTypes.filter(type => type.id !== deletingTypeId))
      
      // Show success toast
      toast({
        title: "Post type deleted",
        description: "The post type has been successfully deleted.",
      })
      
      // Close dialog and reset state
      setDeleteDialogOpen(false)
      setDeletingTypeId(null)
    } catch (err: any) {
      setError(err.message || 'Failed to delete post type')
      toast({
        title: "Error",
        description: err.message || 'Failed to delete post type',
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Real-time field validation
  const validateField = (name: string, value: string): string => {
    const trimmedValue = value?.trim()
    
    switch (name) {
      case 'name':
        if (!trimmedValue) return 'Name is required'
        if (trimmedValue.length < 2) return 'Name must be at least 2 characters long'
        if (trimmedValue.length > 100) return 'Name must not exceed 100 characters'
        break
      
      case 'slug':
        if (!trimmedValue) return 'Slug is required'
        if (!/^[a-z0-9-]+$/.test(trimmedValue)) return 'Slug must contain only lowercase letters, numbers, and hyphens'
        if (trimmedValue.startsWith('-') || trimmedValue.endsWith('-')) return 'Slug cannot start or end with hyphen'
        if (trimmedValue.includes('--')) return 'Slug cannot contain consecutive hyphens'
        if (trimmedValue.length > 100) return 'Slug must not exceed 100 characters'
        break
      
      case 'color':
        if (!trimmedValue) return 'Color is required'
        if (!/^#[0-9A-Fa-f]{6}$/.test(trimmedValue)) return 'Color must be a valid hex color (e.g., #3B82F6)'
        break
      
      case 'icon':
        if (!trimmedValue) return 'Icon is required'
        if (trimmedValue.length > 50) return 'Icon name must not exceed 50 characters'
        break
      
      case 'description':
        if (value && value.length > 500) return 'Description must not exceed 500 characters'
        break
    }
    
    return ''
  }

  // Frontend validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}
    
    // Validate all fields
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof PostType] as string)
      if (error) {
        errors[key] = error
      }
    })
    
    setFieldErrors(errors)
    
    if (Object.keys(errors).length > 0) {
      return false
    }
    
    setError(null)
    return true
  }

  const handleSave = async () => {
    if (!token) {
      setError('Authentication required')
      return
    }

    // Validate form before submitting
    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      const method = editingType ? 'PUT' : 'POST'
      const url = editingType 
        ? `/api/dashboard/post-types/${editingType.id}`
        : '/api/dashboard/post-types'

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
          return
        }
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to save post type')
      }

      const result = await response.json()
      
      if (editingType) {
        // Update existing
        setPostTypes(postTypes.map(type =>
          type.id === editingType.id
            ? { ...type, ...result.data }
            : type
        ))
      } else {
        // Add new
        setPostTypes([...postTypes, result.data])
      }

      handleCancel()
    } catch (err: any) {
      setError(err.message || 'Failed to save post type')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditingType(null)
    setFieldErrors({})
    setFormData({
      name: '',
      slug: '',
      color: '#3B82F6',
      icon: 'FileText',
      description: ''
    })
  }

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
  }

  const handleFieldChange = (name: string, value: string) => {
    // Update form data
    const newFormData = { ...formData, [name]: value }
    
    // Auto-generate slug when name changes
    if (name === 'name') {
      newFormData.slug = generateSlug(value)
    }
    
    setFormData(newFormData)
    
    // Real-time validation
    const error = validateField(name, value)
    setFieldErrors(prev => ({
      ...prev,
      [name]: error
    }))
    
    // Also validate slug if name changed
    if (name === 'name') {
      const slugError = validateField('slug', newFormData.slug)
      setFieldErrors(prev => ({
        ...prev,
        slug: slugError
      }))
    }
  }

  const handleNameChange = (name: string) => {
    handleFieldChange('name', name)
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <DashboardLayout
        title="Post Types Management"
        description="Manage post categories and types"
      >
        <div className="flex items-center justify-center h-[50vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-500 dark:text-gray-400">Checking authentication...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Post Types Management"
      description="Manage post categories and types"
    >
      <div className="space-y-6">
        {/* Error Display - Only show non-validation errors */}
        {error && !error.includes('Please fix the validation errors') && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <LucideIcons.AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        )}

        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <LucideIcons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search post types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
          <Button
            onClick={() => {
              setIsEditing(true);
              setEditingType(null);
              setFieldErrors({});
              setFormData({
                name: '',
                slug: '',
                color: '#3B82F6',
                icon: 'FileText',
                description: ''
              });
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <LucideIcons.Plus className="h-4 w-4 mr-2" />
            New Post Type
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardContent className="p-5">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Types</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{postTypes.length}</p>
                </div>
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                  <LucideIcons.Tag className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardContent className="p-5">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Posts</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {postTypes.reduce((sum, type) => sum + (type.postCount || 0), 0)}
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                  <LucideIcons.Hash className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardContent className="p-5">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Most Used</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {postTypes.sort((a, b) => (b.postCount || 0) - (a.postCount || 0))[0]?.name || 'N/A'}
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                  <LucideIcons.Tag className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <CardContent className="p-5">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Colors Used</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {new Set(postTypes.map(type => type.color)).size}
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                  <LucideIcons.Palette className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Create/Edit Dialog */}
        <Dialog open={isEditing} onOpenChange={(open) => { if (!open) handleCancel(); }}>
          <DialogContent className="max-w-2xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl p-0">
            <div className="px-6 pt-6 pb-4 border-b border-gray-100 dark:border-gray-800 flex items-center justify-between">
              <DialogTitle className="flex items-center text-gray-900 dark:text-white">
                <LucideIcons.Tag className="h-5 w-5 mr-2" />
                {editingType ? 'Edit Post Type' : 'Create New Post Type'}
              </DialogTitle>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2 px-6 pb-6">
              <div className="space-y-4">
                <div>
                  <InputField
                    label="Name"
                    name="name"
                    value={formData.name}
                    onChange={handleNameChange}
                    placeholder="e.g., Tutorial"
                    required
                  />
                  {fieldErrors.name && (
                    <p className="text-red-500 text-sm mt-1">{fieldErrors.name}</p>
                  )}
                </div>
                <div>
                  <InputField
                    label="Slug"
                    name="slug"
                    value={formData.slug}
                    onChange={(value) => handleFieldChange('slug', value)}
                    placeholder="e.g., tutorial"
                    required
                  />
                  {fieldErrors.slug && (
                    <p className="text-red-500 text-sm mt-1">{fieldErrors.slug}</p>
                  )}
                </div>
                <div>
                  <InputField
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={(value) => handleFieldChange('description', value)}
                    placeholder="Brief description of this post type"
                  />
                  {fieldErrors.description && (
                    <p className="text-red-500 text-sm mt-1">{fieldErrors.description}</p>
                  )}
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="icon">Icon</Label>
                  <div className="flex items-center w-full space-x-2 mb-2 mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      className={`flex items-center space-x-2 w-full justify-start ${
                        fieldErrors.icon ? 'border-red-500' : ''
                      }`}
                      onClick={() => setIconDialogOpen(true)}
                    >
                      <Icon name={formData.icon as IconName} className="h-5 w-5 mr-2" />
                      <span>{formData.icon}</span>
                      <span className="ml-2 text-xs text-gray-400">(Pilih Icon)</span>
                    </Button>
                  </div>
                  {fieldErrors.icon && (
                    <p className="text-red-500 text-sm mt-1">{fieldErrors.icon}</p>
                  )}
                  <Dialog open={iconDialogOpen} onOpenChange={setIconDialogOpen}>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Pilih Icon</DialogTitle>
                      </DialogHeader>
                      <IconPickerContent
                        value={formData.icon as IconName}
                        onChange={(icon) => {
                          handleFieldChange('icon', icon)
                          setIconDialogOpen(false)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
                <div className="space-y-2">
                  <ColorPicker
                    label="Color"
                    value={formData.color}
                    onChange={(color) => handleFieldChange('color', color)}
                    showPresets={true}
                    showGradient={false}
                    className="w-full"
                  />
                  {fieldErrors.color && (
                    <p className="text-red-500 text-sm mt-1">{fieldErrors.color}</p>
                  )}
                </div>
                <div>
                  <Label>Preview</Label>
                  <div className="mt-2 space-y-3">
                    {/* Badge Preview */}
                    <Badge
                      style={{
                        background: formData.color.startsWith('linear-gradient') 
                          ? formData.color 
                          : formData.color + (formData.color.length === 7 ? '22' : '20'),
                        color: formData.color.startsWith('linear-gradient') 
                          ? '#ffffff' 
                          : formData.color,
                        borderColor: formData.color.startsWith('linear-gradient') 
                          ? 'transparent' 
                          : formData.color + (formData.color.length === 7 ? '44' : '40')
                      }}
                      className="border flex items-center space-x-1 shadow-sm py-2 px-3"
                    >
                      <Icon name={formData.icon as IconName} className="h-4 w-4" />
                      <span>{formData.name || 'Preview'}</span>
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6 px-6 pb-6">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {editingType ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <LucideIcons.Save className="h-4 w-4 mr-2" />
                    {editingType ? 'Update' : 'Create'}
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Post Types List */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center text-gray-900 dark:text-white">
              <LucideIcons.Tag className="h-5 w-5 mr-2" />
              Post Types ({filteredTypes.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTypes.map((type) => {
                return (
                  <div
                    key={type.id}
                    className="group relative p-6 border border-gray-200/60 dark:border-gray-600/60 rounded-xl hover:shadow-2xl dark:hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50 hover:scale-[1.02] hover:border-gray-300 dark:hover:border-gray-500 backdrop-blur-sm"
                  >
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-transparent via-transparent to-gray-100/20 dark:to-gray-800/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Content */}
                    <div className="relative z-10">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="p-2.5 rounded-lg transition-all duration-300 group-hover:scale-110 relative overflow-hidden"
                            style={{
                              background: type.color.startsWith('linear-gradient') 
                                ? type.color 
                                : type.color + '15',
                              border: type.color.startsWith('linear-gradient') 
                                ? '1px solid rgba(255,255,255,0.2)' 
                                : `1px solid ${type.color}30`
                            }}
                          >
                            <Icon 
                              name={type.icon as IconName} 
                              className="h-6 w-6 transition-colors duration-300 relative z-10" 
                              style={{ 
                                color: type.color.startsWith('linear-gradient') 
                                  ? '#ffffff' 
                                  : type.color 
                              }}
                            />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white text-lg group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors duration-300">{type.name}</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700/50 px-2 py-0.5 rounded-md mt-1">/{type.slug}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 opacity-60 group-hover:opacity-100 transition-opacity duration-300">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(type)}
                            className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                          >
                            <LucideIcons.Edit className="h-4 w-4" />
                          </Button>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  disabled={(type.postCount || 0) > 0}
                                  onClick={() => type.id && handleDeleteClick(type.id)}
                                  className={`transition-all duration-200 ${
                                    (type.postCount || 0) > 0
                                      ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                                      : 'text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20'
                                  }`}
                                >
                                  <LucideIcons.Trash2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              {(type.postCount || 0) > 0 && (
                                <TooltipContent>
                                  <p>Cannot delete post type with {type.postCount} existing posts</p>
                                </TooltipContent>
                              )}
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                        {type.description}
                      </p>
                      
                      <div className="flex items-center justify-between pt-3 border-t border-gray-200/50 dark:border-gray-600/50">
                        <Badge
                          style={{
                            background: type.color.startsWith('linear-gradient') 
                              ? type.color 
                              : type.color + '20',
                            color: type.color.startsWith('linear-gradient') 
                              ? '#ffffff' 
                              : type.color,
                            borderColor: type.color.startsWith('linear-gradient') 
                              ? 'transparent' 
                              : type.color + '40'
                          }}
                          className="border font-medium px-3 py-1 text-xs uppercase tracking-wide shadow-sm"
                        >
                          {type.name}
                        </Badge>
                        <div className="flex items-center space-x-1">
                          <span className="text-lg font-bold text-gray-700 dark:text-gray-300">
                            {type.postCount}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                            posts
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Post Type</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this post type? This action cannot be undone and will permanently remove the post type from your system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setDeleteDialogOpen(false)
              setDeletingTypeId(null)
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <style jsx global>{`
        input.no-clear-x::-ms-clear { display: none; }
        input.no-clear-x::-webkit-clear-button { display: none; }
        input.no-clear-x::-webkit-search-cancel-button { display: none; }
        .dialog-close-btn,
        .dialog-close-btn:hover,
        .dialog-close-btn:focus,
        .dialog-close-btn:active {
          background: transparent !important;
          box-shadow: none !important;
        }
      `}</style>
    </DashboardLayout>
  )
}
