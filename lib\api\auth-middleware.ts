import { NextRequest, NextResponse } from 'next/server'
import { ApiResponseBuilder } from './response-builder'
import { verifyToken } from '@/lib/auth/jwt'
import { ErrorCode, HttpStatus } from '@/types/api'

/**
 * Authentication middleware for dashboard API endpoints
 */
export function withDashboardAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Try to get token from Authorization header first
      let token: string | null = null
      const authHeader = request.headers.get('authorization')

      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7) // Remove 'Bearer ' prefix
      }

      // If no Authorization header, try to get token from cookie
      if (!token) {
        const cookieToken = request.cookies.get('auth-token')
        if (cookieToken) {
          token = cookieToken.value
        }
      }

      if (!token) {
        return ApiResponseBuilder.error(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Authentication required. Please login to access this resource.',
          HttpStatus.UNAUTHORIZED,
          {
            requiresAuth: true,
            redirectTo: '/'
          }
        )
      }

      // Verify the token
      const decoded = await verifyToken(token)

      if (!decoded) {
        return ApiResponseBuilder.error(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Invalid or expired authentication token.',
          HttpStatus.UNAUTHORIZED,
          {
            requiresAuth: true,
            redirectTo: '/'
          }
        )
      }

      // Add user info to request headers for downstream handlers
      const requestWithAuth = new NextRequest(request.url, {
        method: request.method,
        headers: {
          ...Object.fromEntries(request.headers.entries()),
          'x-user-id': decoded.userId || '',
          'x-user-email': decoded.email || '',
          'x-user-role': decoded.role || 'user',
        },
        body: request.body,
      })

      // Call the original handler with authenticated request
      return await handler(requestWithAuth, ...args)
    } catch (error) {
      console.error('Authentication middleware error:', error)

      return ApiResponseBuilder.error(
        ErrorCode.INTERNAL_ERROR,
        'Authentication failed. Please login again.',
        HttpStatus.UNAUTHORIZED,
        {
          requiresAuth: true,
          redirectTo: '/'
        }
      )
    }
  }
}

/**
 * Extract user information from authenticated request
 */
export function getUserFromRequest(request: NextRequest): {
  userId: string
  email: string
  role: string
} {
  return {
    userId: request.headers.get('x-user-id') || '',
    email: request.headers.get('x-user-email') || '',
    role: request.headers.get('x-user-role') || 'user',
  }
}

/**
 * Check if request has valid authentication
 */
export function isAuthenticated(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization')
  return !!(authHeader && authHeader.startsWith('Bearer '))
}

/**
 * Middleware specifically for dashboard API routes
 * Combines authentication with other middleware
 */
export function withDashboardMiddleware<T extends any[]>(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: (request: NextRequest, ...args: T) => Promise<NextResponse>) => {
    // Apply authentication first, then other middlewares
    let wrappedHandler = withDashboardAuth(handler)

    // Apply other middlewares in reverse order
    for (let i = middlewares.length - 1; i >= 0; i--) {
      wrappedHandler = middlewares[i](wrappedHandler)
    }

    return wrappedHandler
  }
}

/**
 * Role-based access control middleware
 */
export function withRoleAuth(allowedRoles: string[] = ['admin', 'user']) {
  return function<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      const user = getUserFromRequest(request)

      if (!allowedRoles.includes(user.role)) {
        return ApiResponseBuilder.error(
          'Insufficient permissions to access this resource.',
          403,
          'FORBIDDEN',
          {
            requiredRoles: allowedRoles,
            userRole: user.role
          }
        )
      }

      return await handler(request, ...args)
    }
  }
}

/**
 * Admin-only access middleware
 */
export function withAdminAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return withRoleAuth(['admin'])(handler)
}
