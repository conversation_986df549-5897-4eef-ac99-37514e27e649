import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { z } from 'zod'

const postService = new PostApiSimpleService()

// Validation schema for slug check
const slugValidationSchema = z.object({
  slug: z.string()
    .min(1, 'Slug is required')
    .max(200, 'Slug must be less than 200 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  excludeId: z.string().nullable().optional()
})

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing slug validation...')

    // Get query parameters
    const url = new URL(request.url)
    const slug = url.searchParams.get('slug')
    const excludeId = url.searchParams.get('excludeId')

    if (!slug) {
      return ApiResponseBuilder.badRequest('Slug parameter is required')
    }

    // Validate slug format
    const validatedData = slugValidationSchema.parse({ slug, excludeId })
    console.log('Validating slug:', validatedData.slug)

    // Check if slug is available
    const isAvailable = await postService.checkSlugAvailability(
      validatedData.slug,
      validatedData.excludeId
    )

    console.log('Slug availability:', isAvailable)

    return ApiResponseBuilder.success(
      {
        slug: validatedData.slug,
        available: isAvailable,
        message: isAvailable ? 'Slug is available' : 'Slug is already taken'
      },
      isAvailable ? 'Slug is available' : 'Slug is already taken',
      200
    )
  } catch (error) {
    console.error('Error in test validateSlugHandler:', error)

    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(
        'Invalid slug format',
        error.errors[0]?.path.join('.') || 'slug',
        { errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        })) }
      )
    }

    if (error instanceof Error) {
      return ApiResponseBuilder.badRequest(error.message)
    }

    return ApiResponseBuilder.internalError('Failed to validate slug')
  }
}
