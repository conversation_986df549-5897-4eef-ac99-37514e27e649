"use client"

import { useState, useEffect, useCallback } from 'react'
import { PostTypeService } from '@/services'
import { PostType } from '@/types'

export interface UsePostTypesReturn {
  postTypes: PostType[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePostTypes(): UsePostTypesReturn {
  const [postTypes, setPostTypes] = useState<PostType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postTypeService = new PostTypeService()

  const fetchPostTypes = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await postTypeService.getAllPostTypes()
      setPostTypes(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post types')
    } finally {
      setLoading(false)
    }
  }, [])

  const refetch = useCallback(() => fetchPostTypes(), [fetchPostTypes])

  useEffect(() => {
    fetchPostTypes()
  }, [fetchPostTypes])

  return {
    postTypes,
    loading,
    error,
    refetch
  }
}

export interface UsePostTypeOptions {
  slug?: string
  id?: string
  autoFetch?: boolean
}

export interface UsePostTypeReturn {
  postType: PostType | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePostType(options: UsePostTypeOptions): UsePostTypeReturn {
  const { slug, id, autoFetch = true } = options
  const [postType, setPostType] = useState<PostType | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postTypeService = new PostTypeService()

  const fetchPostType = useCallback(async () => {
    if (!slug && !id) return

    try {
      setLoading(true)
      setError(null)

      const result = slug 
        ? await postTypeService.getPostTypeBySlug(slug)
        : await postTypeService.getPostTypeById(id!)

      setPostType(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post type')
    } finally {
      setLoading(false)
    }
  }, [slug, id])

  const refetch = useCallback(() => fetchPostType(), [fetchPostType])

  useEffect(() => {
    if (autoFetch && (slug || id)) {
      fetchPostType()
    }
  }, [autoFetch, slug, id, fetchPostType])

  return {
    postType,
    loading,
    error,
    refetch
  }
}

export interface UsePostTypesWithCountsReturn {
  postTypes: Array<PostType & { postCount: number }>
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePostTypesWithCounts(): UsePostTypesWithCountsReturn {
  const [postTypes, setPostTypes] = useState<Array<PostType & { postCount: number }>>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postTypeService = new PostTypeService()

  const fetchPostTypesWithCounts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await postTypeService.getPostTypesWithCounts()
      setPostTypes(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post types with counts')
    } finally {
      setLoading(false)
    }
  }, [])

  const refetch = useCallback(() => fetchPostTypesWithCounts(), [fetchPostTypesWithCounts])

  useEffect(() => {
    fetchPostTypesWithCounts()
  }, [fetchPostTypesWithCounts])

  return {
    postTypes,
    loading,
    error,
    refetch
  }
}
