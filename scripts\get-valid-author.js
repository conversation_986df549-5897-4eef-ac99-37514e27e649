const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function getValidAuthor() {
  console.log('🔍 Looking for valid author ID...\n')

  try {
    // Check existing posts to see what author IDs are being used
    console.log('📋 Checking existing posts for author IDs...')
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('author_id')
      .limit(5)
    
    if (postsError) {
      console.error('❌ Error fetching posts:', postsError.message)
      return
    }

    if (posts && posts.length > 0) {
      const authorIds = [...new Set(posts.map(p => p.author_id))]
      console.log('✅ Found author IDs in posts:', authorIds)
      
      // Use the first author ID found
      const validAuthorId = authorIds[0]
      console.log(`\n🎯 Using author ID: ${validAuthorId}`)
      
      return validAuthorId
    }

    // If no posts exist, check auth.users table
    console.log('📋 No posts found. Checking auth.users table...')
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message)
      return
    }

    if (users && users.users.length > 0) {
      const validAuthorId = users.users[0].id
      console.log(`✅ Found user in auth.users: ${validAuthorId}`)
      return validAuthorId
    }

    console.log('⚠️ No users found in auth.users table')
    return null

  } catch (error) {
    console.error('❌ Error:', error.message)
    return null
  }
}

getValidAuthor().then(authorId => {
  if (authorId) {
    console.log(`\n✅ Valid author ID: ${authorId}`)
    console.log('You can use this ID for testing create post functionality.')
  } else {
    console.log('\n❌ No valid author ID found.')
    console.log('You may need to create a user first or check your database setup.')
  }
})
