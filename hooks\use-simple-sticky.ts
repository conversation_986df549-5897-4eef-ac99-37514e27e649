"use client"

import { useEffect, useState, useRef, useCallback } from 'react'

interface UseStickyOptions {
  topOffset?: number
  bottomOffset?: number
  enabled?: boolean
  containerSelector?: string
}

export function useAdvancedSticky(options: UseStickyOptions = {}) {
  const {
    topOffset = 96, // Default top offset (header height + padding)
    bottomOffset = 32, // Default bottom offset
    enabled = true,
    containerSelector = 'main'
  } = options

  const elementRef = useRef<HTMLDivElement>(null)
  const placeholderRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)
  const [isAtBottom, setIsAtBottom] = useState(false)
  const [elementWidth, setElementWidth] = useState(0)
  const [elementHeight, setElementHeight] = useState(0)
  const [originalTop, setOriginalTop] = useState(0)

  // Calculate initial position and dimensions
  const updateInitialPosition = useCallback(() => {
    const element = elementRef.current
    if (!element) return

    const rect = element.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop

    setOriginalTop(rect.top + scrollTop)
    setElementWidth(rect.width)
    setElementHeight(rect.height)
  }, [])

  // Main scroll handler
  const handleScroll = useCallback(() => {
    if (!enabled) return

    const element = elementRef.current
    const placeholder = placeholderRef.current
    const container = document.querySelector(containerSelector) as HTMLElement

    if (!element || !placeholder || !container) return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top + scrollTop
    const containerBottom = containerTop + container.offsetHeight

    // Calculate when sidebar should become sticky
    const stickyThreshold = originalTop - topOffset
    const shouldBeSticky = scrollTop >= stickyThreshold

    // Calculate when sidebar should unstick at bottom
    const sidebarBottom = scrollTop + windowHeight - bottomOffset
    const shouldUnstickAtBottom = sidebarBottom >= containerBottom

    const newIsSticky = shouldBeSticky && !shouldUnstickAtBottom
    const newIsAtBottom = shouldUnstickAtBottom && shouldBeSticky

    // Update sticky state
    if (newIsSticky !== isSticky) {
      setIsSticky(newIsSticky)

      if (newIsSticky) {
        // Show placeholder to maintain layout
        placeholder.style.height = `${elementHeight}px`
        placeholder.style.display = 'block'
      } else {
        // Hide placeholder
        placeholder.style.display = 'none'
      }
    }

    // Update bottom state
    if (newIsAtBottom !== isAtBottom) {
      setIsAtBottom(newIsAtBottom)
    }
  }, [enabled, originalTop, topOffset, bottomOffset, containerSelector, isSticky, isAtBottom, elementHeight])

  useEffect(() => {
    if (!enabled) return

    const element = elementRef.current
    if (!element) return

    const handleResize = () => {
      updateDimensions()
      handleScroll()
    }

    // Initial setup
    updateDimensions()
    handleScroll()

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleResize, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [topOffset, enabled, isSticky])

  const getStickyStyles = (): React.CSSProperties => {
    if (!enabled) return {}

    if (isSticky) {
      return {
        position: 'fixed',
        top: `${topOffset}px`,
        width: `${elementWidth}px`,
        zIndex: 10,
      }
    }

    return {}
  }

  return {
    elementRef,
    placeholderRef,
    isSticky,
    getStickyStyles,
    elementWidth,
    elementHeight,
  }
}

/**
 * Even simpler version using CSS classes
 */
export function useBasicSticky(topOffset: number = 96) {
  const elementRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const handleScroll = () => {
      const rect = element.getBoundingClientRect()
      const shouldStick = rect.top <= topOffset
      setIsSticky(shouldStick)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    handleScroll() // Initial check

    return () => window.removeEventListener('scroll', handleScroll)
  }, [topOffset])

  return {
    elementRef,
    isSticky,
    stickyClass: isSticky ? 'fixed z-10' : 'relative',
    stickyStyle: isSticky ? { top: `${topOffset}px` } : {},
  }
}
