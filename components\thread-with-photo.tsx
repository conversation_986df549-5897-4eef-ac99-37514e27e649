"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Heart, MessageSquare, Share, ThumbsUp, Brain, Clock, User, AlertCircle, Maximize2 } from "lucide-react"
import Image from "next/image"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"

interface Comment {
  id: string
  author: string
  content: string
  timestamp: string
  avatar?: string
  reactions: {
    thumbsUp: number
    heart: number
    brain?: number
  }
}

interface ThreadWithPhotoProps {
  title: string
  content: string
  author: string
  timestamp: string
  photoUrl?: string
  photoAlt?: string
  authorAvatar?: string
  reactions?: {
    thumbsUp: number
    heart: number
    brain: number
  }
  comments?: Comment[]
  onAddComment?: (comment: string) => void
  onReaction?: (type: string) => void
  className?: string
}

export function ThreadWithPhoto({
  title,
  content,
  author,
  timestamp,
  photoUrl,
  photoAlt = "Thread image",
  authorAvatar,
  reactions = { thumbsUp: 0, heart: 0, brain: 0 },
  comments = [],
  onAddComment,
  onReaction,
  className = "",
}: ThreadWithPhotoProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)
  const [newComment, setNewComment] = useState("")
  const [userReactions, setUserReactions] = useState<{ [key: string]: boolean }>({})

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleReaction = (type: string) => {
    setUserReactions((prev) => ({
      ...prev,
      [type]: !prev[type],
    }))
    onReaction?.(type)
  }

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddComment?.(newComment)
      setNewComment("")
    }
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return date.toISOString().split('T')[0] + ' ' + date.toISOString().split('T')[1].split('.')[0]
    } catch {
      return timestamp
    }
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card className="bg-gray-900/80 border-gray-800/50 backdrop-blur-sm shadow-2xl">
        {/* Thread Header */}
        <CardHeader className="pb-4">
          <div className="flex items-start gap-4">
            <Avatar className="w-12 h-12 border-2 border-green-500 shrink-0">
              <AvatarImage src={authorAvatar || "/placeholder.svg"} alt={author} />
              <AvatarFallback className="bg-green-600 text-black font-bold">
                {author
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                <h3 className="font-semibold text-white text-lg">{author}</h3>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock className="w-3 h-3" />
                  <span>{formatTimestamp(timestamp)}</span>
                </div>
              </div>
              <h1 className="text-xl sm:text-2xl font-bold text-white leading-tight mb-3">{title}</h1>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Thread Content */}
          <div className="prose prose-invert prose-green max-w-none">
            <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">{content}</p>
          </div>

          {/* Photo Section */}
          {photoUrl && (
            <div className="relative">
              {!imageError ? (
                <div className="relative group">
                  {imageLoading && (
                    <div className="absolute inset-0 bg-gray-800 rounded-lg flex items-center justify-center">
                      <div className="flex items-center gap-3 text-gray-400">
                        <div className="w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                        <span>Loading image...</span>
                      </div>
                    </div>
                  )}
                  <div className="relative overflow-hidden rounded-lg bg-gray-800">
                    <Image
                      src={photoUrl || "/placeholder.svg"}
                      alt={photoAlt}
                      width={800}
                      height={600}
                      className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
                      onError={handleImageError}
                      onLoad={handleImageLoad}
                      priority
                    />
                    {/* Expand Button */}
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute top-3 right-3 bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                          <Maximize2 className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-7xl max-h-[90vh] p-0 bg-black/90 border-gray-800">
                        <div className="relative w-full h-full flex items-center justify-center p-4">
                          <Image
                            src={photoUrl || "/placeholder.svg"}
                            alt={photoAlt}
                            width={1200}
                            height={900}
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ) : (
                <div className="bg-gray-800/50 border-2 border-dashed border-gray-700 rounded-lg p-8 text-center">
                  <div className="flex flex-col items-center gap-3 text-gray-500">
                    <div className="p-3 bg-gray-700/50 rounded-full">
                      <AlertCircle className="w-8 h-8" />
                    </div>
                    <div>
                      <p className="font-medium">Failed to load image</p>
                      <p className="text-sm">The image could not be displayed</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Reactions and Actions */}
          <div className="flex flex-wrap items-center justify-between gap-4 pt-4 border-t border-gray-800/50">
            <div className="flex items-center gap-3">
              <button
                onClick={() => handleReaction("thumbsUp")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                  userReactions.thumbsUp
                    ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                    : "text-gray-500 hover:text-blue-400 hover:bg-blue-500/10"
                }`}
              >
                <ThumbsUp className="w-4 h-4" />
                <span>{reactions.thumbsUp + (userReactions.thumbsUp ? 1 : 0)}</span>
              </button>
              <button
                onClick={() => handleReaction("heart")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                  userReactions.heart
                    ? "bg-red-500/20 text-red-400 border border-red-500/30"
                    : "text-gray-500 hover:text-red-400 hover:bg-red-500/10"
                }`}
              >
                <Heart className="w-4 h-4" />
                <span>{reactions.heart + (userReactions.heart ? 1 : 0)}</span>
              </button>
              <button
                onClick={() => handleReaction("brain")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                  userReactions.brain
                    ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                    : "text-gray-500 hover:text-purple-400 hover:bg-purple-500/10"
                }`}
              >
                <Brain className="w-4 h-4" />
                <span>{reactions.brain + (userReactions.brain ? 1 : 0)}</span>
              </button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="text-gray-400 hover:text-green-400 hover:bg-green-500/10">
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comments Section */}
      <div className="mt-8 space-y-6">
        <div className="flex items-center gap-3">
          <MessageSquare className="w-6 h-6 text-green-400" />
          <h2 className="text-2xl font-bold text-white">Comments ({comments.length})</h2>
        </div>

        {/* Add Comment */}
        {onAddComment && (
          <Card className="bg-gray-900/80 border-gray-800/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex gap-4">
                <Avatar className="w-10 h-10 border-2 border-green-500 shrink-0">
                  <AvatarFallback className="bg-green-600 text-black font-bold">
                    <User className="w-5 h-5" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Share your thoughts..."
                    className="bg-gray-800/50 border-gray-700 text-gray-100 mb-4 resize-none focus:border-green-500 transition-colors duration-200"
                    rows={3}
                  />
                  <Button
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-black font-medium"
                  >
                    Post Comment
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Comments List */}
        {comments.length > 0 ? (
          <div className="space-y-4">
            {comments.map((comment) => (
              <Card key={comment.id} className="bg-gray-900/80 border-gray-800/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    <Avatar className="w-10 h-10 border-2 border-gray-600 shrink-0">
                      <AvatarImage src={comment.avatar || "/placeholder.svg"} alt={comment.author} />
                      <AvatarFallback className="bg-gray-600 text-white font-bold">
                        {comment.author
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-3">
                        <div className="font-medium text-white">{comment.author}</div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>{formatTimestamp(comment.timestamp)}</span>
                        </div>
                      </div>
                      <p className="text-gray-300 mb-4 leading-relaxed whitespace-pre-wrap">{comment.content}</p>
                      <div className="flex items-center gap-4">
                        <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-400 transition-colors duration-200">
                          <ThumbsUp className="w-3 h-3" />
                          <span>{comment.reactions.thumbsUp}</span>
                        </button>
                        <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-red-400 transition-colors duration-200">
                          <Heart className="w-3 h-3" />
                          <span>{comment.reactions.heart}</span>
                        </button>
                        {comment.reactions.brain && (
                          <button className="flex items-center gap-1 text-sm text-gray-500 hover:text-purple-400 transition-colors duration-200">
                            <Brain className="w-3 h-3" />
                            <span>{comment.reactions.brain}</span>
                          </button>
                        )}
                        <button className="text-sm text-gray-500 hover:text-green-400 transition-colors duration-200">
                          Reply
                        </button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-800/50 rounded-full flex items-center justify-center">
              <MessageSquare className="w-8 h-8 text-gray-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-400 mb-2">No comments yet</h3>
            <p className="text-gray-500">Be the first to share your thoughts!</p>
          </div>
        )}
      </div>
    </div>
  )
}
