import { NextRequest } from 'next/server'
import { PostApiSupabaseService } from '@/services/post-api-supabase.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { withSupabaseDashboardMiddleware } from '@/lib/api/supabase-auth-middleware'
import { withError<PERSON>and<PERSON>, withLogging, withRateLimit } from '@/lib/api/middleware'

const postService = new PostApiSupabaseService()

async function getPostStatsHandler(request: NextRequest) {
  try {
    // Get comprehensive post statistics
    const stats = await postService.getPostStats()

    return ApiResponseBuilder.success(
      stats,
      'Post statistics retrieved successfully'
    )
  } catch (error) {
    console.error('Error in GET /api/dashboard/posts/stats:', error)
    return ApiResponseBuilder.error(
      'Failed to retrieve post statistics',
      500,
      'STATS_FETCH_ERROR'
    )
  }
}

// Apply dashboard middleware with authentication
export const GET = withSupabaseDashboardMiddleware(
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  withLogging,
  withRateLimit(200, 15 * 60 * 1000) // 200 requests per 15 minutes
)(getPostStatsHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
