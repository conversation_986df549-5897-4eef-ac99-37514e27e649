// Post API Service for dashboard API endpoints using Supabase
import { PostSupabaseRepository } from '@/repositories/post-supabase.repository'
import { Post, PostFilters } from '@/types'
import { PaginationOptions } from '@/types/api'

export interface PostStatsApiResponse {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  archivedPosts: number
  totalViews: number
  totalReactions: number
  totalComments: number
  postsByType: { type: string; count: number }[]
  mostViewedPosts: Post[]
  recentPosts: Post[]
  growthMetrics: {
    thisMonth: number
    lastMonth: number
    growth: number
  }
}

export class PostApiSupabaseService {
  private postRepository: PostSupabaseRepository

  constructor() {
    this.postRepository = new PostSupabaseRepository()
  }

  /**
   * Get all posts with pagination and filtering
   */
  async getAllPosts(
    pagination: PaginationOptions = {},
    filters: PostFilters = {}
  ): Promise<{ posts: Post[]; total: number }> {
    try {
      return await this.postRepository.findAll(pagination, filters)
    } catch (error) {
      console.error('Error getting posts:', error)
      throw error
    }
  }

  /**
   * Get post by ID
   */
  async getPostById(id: string): Promise<Post | null> {
    try {
      return await this.postRepository.findById(id)
    } catch (error) {
      console.error('Error getting post by id:', error)
      throw error
    }
  }

  /**
   * Get post by slug
   */
  async getPostBySlug(slug: string): Promise<Post | null> {
    try {
      return await this.postRepository.findBySlug(slug)
    } catch (error) {
      console.error('Error getting post by slug:', error)
      throw error
    }
  }

  /**
   * Get posts statistics
   */
  async getPostStats(): Promise<PostStatsApiResponse> {
    try {
      const stats = await this.postRepository.getStats()
      const recentPosts = await this.getRecentPosts(5)
      const mostViewedPosts = await this.getMostViewedPosts(5)

      return {
        totalPosts: stats.total,
        publishedPosts: stats.published,
        draftPosts: stats.draft,
        archivedPosts: 0, // Not implemented in database yet
        totalViews: stats.totalViews,
        totalReactions: stats.totalReactions,
        totalComments: 0, // Not implemented in database yet
        postsByType: [], // Will be implemented later
        mostViewedPosts,
        recentPosts,
        growthMetrics: {
          thisMonth: 0, // Will be implemented later
          lastMonth: 0, // Will be implemented later
          growth: 0 // Will be implemented later
        }
      }
    } catch (error) {
      console.error('Error getting post stats:', error)
      throw error
    }
  }

  /**
   * Get featured posts
   */
  async getFeaturedPosts(limit: number = 6): Promise<Post[]> {
    try {
      return await this.postRepository.getFeaturedPosts(limit)
    } catch (error) {
      console.error('Error getting featured posts:', error)
      throw error
    }
  }

  /**
   * Search posts
   */
  async searchPosts(
    query: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: Post[]; total: number }> {
    try {
      return await this.postRepository.findAll(pagination, { search: query })
    } catch (error) {
      console.error('Error searching posts:', error)
      throw error
    }
  }

  /**
   * Get posts by status
   */
  async getPostsByStatus(
    status: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: Post[]; total: number }> {
    try {
      return await this.postRepository.findAll(pagination, { status: status as any })
    } catch (error) {
      console.error('Error getting posts by status:', error)
      throw error
    }
  }

  /**
   * Get posts by type
   */
  async getPostsByType(
    type: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: Post[]; total: number }> {
    try {
      return await this.postRepository.findAll(pagination, { type })
    } catch (error) {
      console.error('Error getting posts by type:', error)
      throw error
    }
  }

  /**
   * Get recent posts
   */
  async getRecentPosts(limit: number = 10): Promise<Post[]> {
    try {
      const { posts } = await this.postRepository.findAll(
        { limit, sortBy: 'created_at', sortOrder: 'desc' },
        {}
      )
      return posts
    } catch (error) {
      console.error('Error getting recent posts:', error)
      throw error
    }
  }

  /**
   * Get most viewed posts
   */
  async getMostViewedPosts(limit: number = 5): Promise<Post[]> {
    try {
      const { posts } = await this.postRepository.findAll(
        { limit, sortBy: 'view_count', sortOrder: 'desc' },
        {}
      )
      return posts
    } catch (error) {
      console.error('Error getting most viewed posts:', error)
      throw error
    }
  }

  /**
   * Get posts count by status
   */
  async getPostsCountByStatus(): Promise<{
    published: number
    draft: number
    archived: number
    total: number
  }> {
    try {
      const stats = await this.postRepository.getStats()
      return {
        published: stats.published,
        draft: stats.draft,
        archived: 0, // Not implemented yet
        total: stats.total
      }
    } catch (error) {
      console.error('Error getting posts count by status:', error)
      throw error
    }
  }

  /**
   * Get posts count by type
   */
  async getPostsCountByType(): Promise<{ type: string; count: number }[]> {
    try {
      // This will be implemented later when we add post type counting
      return []
    } catch (error) {
      console.error('Error getting posts count by type:', error)
      throw error
    }
  }

  /**
   * Get growth metrics
   */
  async getGrowthMetrics(): Promise<{
    thisMonth: number
    lastMonth: number
    growth: number
  }> {
    try {
      // This will be implemented later with date-based queries
      return {
        thisMonth: 0,
        lastMonth: 0,
        growth: 0
      }
    } catch (error) {
      console.error('Error getting growth metrics:', error)
      throw error
    }
  }
}
