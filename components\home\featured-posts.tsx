"use client"

import { Star } from "lucide-react"
import { Post } from "@/types"
import { PostCard } from "@/components/cards/post-card"
import { useTranslations } from '@/contexts/locale-context'

interface FeaturedPostsProps {
  posts: Post[]
  locale?: string
}

export function FeaturedPosts({ posts, locale = 'en' }: FeaturedPostsProps) {
  const t = useTranslations()

  if (posts.length === 0) return null

  const isSinglePost = posts.length === 1

  return (
    <section>
      <div className="flex items-center gap-3 mb-4">
        <div className="p-1.5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
          <Star className="w-4 h-4 text-black" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white theme-transition">{t('featuredPosts.title')}</h2>
          <p className="text-xs text-gray-600 dark:text-gray-500 theme-transition">{t('featuredPosts.description')}</p>
        </div>
      </div>

      {/* Conditional Layout: Full width for single post, horizontal scroll for multiple */}
      <div className="relative">
        {isSinglePost ? (
          /* Single Post - Full Width Layout */
          <div className="w-full">
            <PostCard
              key={posts[0].id}
              post={posts[0]}
              variant="full"
              locale={locale}
            />
          </div>
        ) : (
          /* Multiple Posts - Horizontal Scrolling Layout */
          <>
            <div className="flex gap-6 overflow-x-auto scroll-smooth scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 pb-4">
              {posts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  variant="compact"
                  locale={locale}
                />
              ))}
            </div>

            {/* Scroll Indicator */}
            {posts.length > 2 && (
              <div className="flex justify-center mt-4">
                <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-500 theme-transition">
                  <span>Scroll to see more</span>
                  <div className="flex gap-1">
                    {posts.map((_, index) => (
                      <div key={index} className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-600 rounded-full theme-transition"></div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </section>
  )
}
