import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import { Post, PostFilters, PostStatus } from '@/types'
import { PaginationOptions } from '@/types/api'

export class PostSupabaseSimpleRepository {
  private supabase = createServerSupabaseAdminClient()

  /**
   * Fallback data for development when database is not available
   */
  private getFallbackPosts(pagination: PaginationOptions = {}, filters: PostFilters = {}): { posts: any[]; total: number } {
    const { page = 1, limit = 10 } = pagination

    const fallbackPosts = [
      {
        id: 'b658ad42-46fc-4741-ac86-bc2dae0439dd',
        title: 'Test Post with Images - 2025-05-30T20:54:34.072Z',
        slug: 'test-post-images-1748638474072',
        content: 'This is a test post with images for development.',
        excerpt: 'A test post for development purposes.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-30T20:54:34.072Z',
        updated_at: '2025-01-30T20:54:34.072Z',
        published_at: '2025-01-30T20:54:34.072Z',
        view_count: 150,
        reactions_heart: 25,
        comments_count: 5,
        tags: ['development', 'test'],
        featured: true,
        read_time: '5 min read',
        author_id: 'admin',
        type_id: 'article',
        post_images: [
          {
            id: 'img1',
            url: 'https://via.placeholder.com/800x400',
            alt_text: 'Test image 1',
            caption: 'Test caption 1',
            file_name: 'test-image-1.jpg',
            file_size: 1024,
            display_order: 1,
            width: 800,
            height: 400
          }
        ]
      },
      {
        id: 'e6da38a7-1abe-4bcf-a90f-125eac3d7ab7',
        title: 'My Thoughts on the Future of Web Development',
        slug: 'future-of-web-development',
        content: 'Web development is evolving rapidly...',
        excerpt: 'Exploring the future trends in web development.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-29T10:30:00.000Z',
        updated_at: '2025-01-29T10:30:00.000Z',
        published_at: '2025-01-29T10:30:00.000Z',
        view_count: 89,
        reactions_heart: 12,
        comments_count: 3,
        tags: ['web development', 'future', 'technology'],
        featured: false,
        read_time: '8 min read',
        author_id: 'admin',
        type_id: 'article',
        post_images: []
      },
      {
        id: '3b9c8a4d-7a62-4475-863f-2dd8be1a460c',
        title: 'Common JavaScript Errors and How to Fix Them',
        slug: 'common-javascript-errors-and-fixes',
        content: 'JavaScript errors can be frustrating...',
        excerpt: 'Learn about common JavaScript errors and their solutions.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-28T15:45:00.000Z',
        updated_at: '2025-01-28T15:45:00.000Z',
        published_at: '2025-01-28T15:45:00.000Z',
        view_count: 234,
        reactions_heart: 45,
        comments_count: 8,
        tags: ['javascript', 'debugging', 'programming'],
        featured: true,
        read_time: '12 min read',
        author_id: 'admin',
        type_id: 'tutorial',
        post_images: []
      },
      {
        id: 'da8cadac-5531-4c34-9330-42ed134dd7a3',
        title: 'Getting Started with React Hooks',
        slug: 'getting-started-react-hooks',
        content: 'React Hooks revolutionized how we write React components...',
        excerpt: 'A comprehensive guide to React Hooks for beginners.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-27T09:15:00.000Z',
        updated_at: '2025-01-27T09:15:00.000Z',
        published_at: '2025-01-27T09:15:00.000Z',
        view_count: 178,
        reactions_heart: 32,
        comments_count: 6,
        tags: ['react', 'hooks', 'frontend'],
        featured: false,
        read_time: '10 min read',
        author_id: 'admin',
        type_id: 'tutorial',
        post_images: []
      }
    ]

    // Apply filters
    let filteredPosts = fallbackPosts

    if (filters.status) {
      const statusMap: { [key: string]: string } = {
        'draft': 'DRAFT',
        'published': 'PUBLISHED',
        'archived': 'ARCHIVED'
      }
      const mappedStatus = statusMap[filters.status.toLowerCase()] || filters.status.toUpperCase()
      filteredPosts = filteredPosts.filter(post => post.status === mappedStatus)
    }

    if (filters.featured !== undefined) {
      filteredPosts = filteredPosts.filter(post => post.featured === filters.featured)
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(searchLower) ||
        post.content.toLowerCase().includes(searchLower)
      )
    }

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex)

    return {
      posts: paginatedPosts.map(post => this.mapPostFromDatabase(post)),
      total: filteredPosts.length
    }
  }

  /**
   * Map camelCase column names to snake_case database columns
   */
  private mapColumnName(columnName: string): string {
    const columnMap: { [key: string]: string } = {
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'publishedAt': 'published_at',
      'viewCount': 'view_count',
      'reactionHart': 'reactions_heart', // Fixed: use reactions_heart from DB
      'postTypeId': 'type_id', // Fixed: use type_id from DB
      'authorId': 'author_id',
      'featuredImage': 'featured_image',
      'metaTitle': 'meta_title',
      'metaDescription': 'meta_description',
      'comments': 'comments_count' // Fixed: use comments_count from DB
    }

    return columnMap[columnName] || columnName
  }

  /**
   * Map database row to Post interface with proper relationships
   */
  private mapPostFromDatabase(dbPost: any): any {
    // Log the raw post data for debugging
    console.log('Mapping post:', {
      id: dbPost.id,
      title: dbPost.title,
      imagesCount: dbPost.post_images?.length || 0
    })

    return {
      id: dbPost.id,
      title: dbPost.title,
      slug: dbPost.slug,
      content: dbPost.content,
      excerpt: dbPost.excerpt,
      version: dbPost.version || 'v1.0',
      status: dbPost.status,
      createdAt: dbPost.created_at,
      updatedAt: dbPost.updated_at,
      publishedAt: dbPost.published_at,
      viewCount: dbPost.view_count || 0,
      reactionHart: dbPost.reactions_heart || 0,
      comments: dbPost.comments_count || 0,
      tags: dbPost.tags || [],
      featured: dbPost.featured || false,
      readTime: dbPost.read_time || '5 min read',
      // Map images from post_images relation
      images: (dbPost.post_images || []).map((img: any) => ({
        id: img.id,
        url: img.url,
        altText: img.alt_text || '',
        caption: img.caption || '',
        name: img.file_name || `image-${img.display_order || 1}`,
        size: img.file_size || 1024,
        width: img.width || 800,
        height: img.height || 600
      })),
      // Map author from JOIN or use defaults
      author: dbPost.user_profiles ? {
        id: dbPost.user_profiles.id,
        name: dbPost.user_profiles.full_name || 'Admin User',
        email: dbPost.user_profiles.email || '',
        avatar_url: dbPost.user_profiles.avatar_url
      } : {
        id: dbPost.author_id,
        name: 'Admin User',
        email: '',
        avatar_url: null
      },
      // Map post type from JOIN or use defaults
      type: dbPost.post_types ? {
        id: dbPost.post_types.id,
        name: dbPost.post_types.name,
        color: dbPost.post_types.color,
        icon: dbPost.post_types.icon,
        slug: dbPost.post_types.slug
      } : {
        id: dbPost.type_id || 'default',
        name: 'Article',
        color: '#10b981',
        icon: 'file-text',
        slug: 'article'
      }
    }
  }

  /**
   * Get posts statistics (simple version for testing)
   */
  async getStats(): Promise<{
    total: number
    published: number
    draft: number
    featured: number
    totalViews: number
    totalReactions: number
  }> {
    try {
      const [
        { count: total },
        { count: published },
        { count: draft },
        { count: featured },
        { data: reactionsData },
        { data: viewsData }
      ] = await Promise.all([
        this.supabase.from('posts').select('*', { count: 'exact', head: true }),
        this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('status', 'PUBLISHED'),
        this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('status', 'DRAFT'),
        this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('featured', true),
        this.supabase.from('posts').select('reactions_heart'),
        this.supabase.from('posts').select('view_count')
      ])

      const totalReactions = (reactionsData || []).reduce((sum, post) => sum + (post.reactions_heart || 0), 0)
      const totalViews = (viewsData || []).reduce((sum, post) => sum + (post.view_count || 0), 0)

      return {
        total: total || 0,
        published: published || 0,
        draft: draft || 0,
        featured: featured || 0,
        totalViews,
        totalReactions
      }
    } catch (error) {
      console.error('Error getting stats:', error)
      throw error
    }
  }

  /**
   * Find all posts (simple version without relationships)
   */
  async findAll(
    pagination: PaginationOptions = {},
    filters: PostFilters = {}
  ): Promise<{ posts: any[]; total: number }> {
    try {
      const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'desc' } = pagination
      const from = (page - 1) * limit
      const to = from + limit - 1

      // Build query with JOIN to post_images
      let query = this.supabase
        .from('posts')
        .select(`
          *,
          post_images (*)
        `)

      // Apply filters
      if (filters.status) {
        const statusMap: { [key: string]: string } = {
          'draft': 'DRAFT',
          'published': 'PUBLISHED',
          'archived': 'ARCHIVED'
        }
        const mappedStatus = statusMap[filters.status.toLowerCase()] || filters.status.toUpperCase()
        query = query.eq('status', mappedStatus)
      }

      if (filters.featured !== undefined) {
        query = query.eq('featured', filters.featured)
      }

      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`)
      }

      // Add sorting and pagination
      const ascending = sortOrder === 'asc'
      // Map camelCase to snake_case for database columns
      const dbColumn = this.mapColumnName(sortBy)
      query = query.order(dbColumn, { ascending }).range(from, to)

      const { data: posts, error, count } = await query

      if (error) {
        console.error('Supabase error in findAll:', error)
        // Return fallback data for development
        return this.getFallbackPosts(pagination, filters)
      }

      // Get total count
      let countQuery = this.supabase.from('posts').select('*', { count: 'exact', head: true })

      if (filters.status) {
        const statusMap: { [key: string]: string } = {
          'draft': 'DRAFT',
          'published': 'PUBLISHED',
          'archived': 'ARCHIVED'
        }
        const mappedStatus = statusMap[filters.status.toLowerCase()] || filters.status.toUpperCase()
        countQuery = countQuery.eq('status', mappedStatus)
      }

      if (filters.featured !== undefined) {
        countQuery = countQuery.eq('featured', filters.featured)
      }

      const { count: totalCount, error: countError } = await countQuery

      if (countError) {
        console.error('Supabase error getting count:', countError)
        // Use posts length as fallback
        return {
          posts: (posts || []).map(post => this.mapPostFromDatabase(post)),
          total: posts?.length || 0
        }
      }

      return {
        posts: (posts || []).map(post => this.mapPostFromDatabase(post)),
        total: totalCount || 0
      }
    } catch (error) {
      console.error('Error in findAll:', error)
      // Return fallback data for development
      return this.getFallbackPosts(pagination, filters)
    }
  }

  /**
   * Find post by ID with complete data including images
   */
  async findById(id: string): Promise<any | null> {
    try {
      console.log('Finding post by ID:', id)

      // Get the post
      const { data: post, error } = await this.supabase
        .from('posts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          console.log('Post not found:', id)
          return null
        }
        console.error('Supabase error in findById:', error)
        throw new Error(`Failed to fetch post: ${error.message}`)
      }

      if (!post) {
        return null
      }

      // Get post images
      console.log('🖼️ Fetching images for post ID:', id)
      const { data: images, error: imagesError } = await this.supabase
        .from('post_images')
        .select('*')
        .eq('post_id', id)
        .order('display_order')

      if (imagesError) {
        console.error('❌ Error fetching post images:', imagesError)
        // Don't throw error, just log and continue without images
      } else {
        console.log('✅ Images query successful')
        console.log('📸 Raw images data:', JSON.stringify(images, null, 2))
      }

      console.log('Post found:', post.title)
      console.log('Post images found:', images?.length || 0)

      // Add images to post data with correct property name for mapping
      const postWithImages = {
        ...post,
        post_images: images || [] // Use post_images to match mapPostFromDatabase expectation
      }

      return this.mapPostFromDatabase(postWithImages)
    } catch (error) {
      console.error('Error in findById:', error)
      throw error
    }
  }

  /**
   * Find post by slug with complete data
   */
  async findBySlug(slug: string): Promise<any | null> {
    try {
      console.log('Finding post by slug:', slug)

      const { data: post, error } = await this.supabase
        .from('posts')
        .select(`
          *,
          post_images (*)
        `)
        .eq('slug', slug)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          console.log('Post not found with slug:', slug)
          return null
        }
        console.error('Supabase error in findBySlug:', error)
        // Try fallback data
        return this.getFallbackPostBySlug(slug)
      }

      console.log('Post found by slug:', post?.title)
      console.log('Post images found:', post?.post_images?.length || 0)
      return post ? this.mapPostFromDatabase(post) : null
    } catch (error) {
      console.error('Error in findBySlug:', error)
      // Try fallback data
      return this.getFallbackPostBySlug(slug)
    }
  }

  /**
   * Get fallback post by slug for development
   */
  private getFallbackPostBySlug(slug: string): any | null {
    const fallbackPosts = [
      {
        id: 'b658ad42-46fc-4741-ac86-bc2dae0439dd',
        title: 'Test Post with Images - 2025-05-30T20:54:34.072Z',
        slug: 'test-post-images-1748638474072',
        content: 'This is a test post with images for development.',
        excerpt: 'A test post for development purposes.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-30T20:54:34.072Z',
        updated_at: '2025-01-30T20:54:34.072Z',
        published_at: '2025-01-30T20:54:34.072Z',
        view_count: 150,
        reactions_heart: 25,
        comments_count: 5,
        tags: ['development', 'test'],
        featured: true,
        read_time: '5 min read',
        author_id: 'admin',
        type_id: 'article',
        post_images: [
          {
            id: 'img1',
            url: 'https://via.placeholder.com/800x400',
            alt_text: 'Test image 1',
            caption: 'Test caption 1',
            file_name: 'test-image-1.jpg',
            file_size: 1024,
            display_order: 1,
            width: 800,
            height: 400
          }
        ]
      },
      {
        id: 'e6da38a7-1abe-4bcf-a90f-125eac3d7ab7',
        title: 'My Thoughts on the Future of Web Development',
        slug: 'future-of-web-development',
        content: 'Web development is evolving rapidly...',
        excerpt: 'Exploring the future trends in web development.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-29T10:30:00.000Z',
        updated_at: '2025-01-29T10:30:00.000Z',
        published_at: '2025-01-29T10:30:00.000Z',
        view_count: 89,
        reactions_heart: 12,
        comments_count: 3,
        tags: ['web development', 'future', 'technology'],
        featured: false,
        read_time: '8 min read',
        author_id: 'admin',
        type_id: 'article',
        post_images: []
      },
      {
        id: '3b9c8a4d-7a62-4475-863f-2dd8be1a460c',
        title: 'Common JavaScript Errors and How to Fix Them',
        slug: 'common-javascript-errors-and-fixes',
        content: 'JavaScript errors can be frustrating...',
        excerpt: 'Learn about common JavaScript errors and their solutions.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-28T15:45:00.000Z',
        updated_at: '2025-01-28T15:45:00.000Z',
        published_at: '2025-01-28T15:45:00.000Z',
        view_count: 234,
        reactions_heart: 45,
        comments_count: 8,
        tags: ['javascript', 'debugging', 'programming'],
        featured: true,
        read_time: '12 min read',
        author_id: 'admin',
        type_id: 'tutorial',
        post_images: []
      },
      {
        id: 'da8cadac-5531-4c34-9330-42ed134dd7a3',
        title: 'Getting Started with React Hooks',
        slug: 'getting-started-react-hooks',
        content: 'React Hooks revolutionized how we write React components...',
        excerpt: 'A comprehensive guide to React Hooks for beginners.',
        version: 'v1.0',
        status: 'PUBLISHED',
        created_at: '2025-01-27T09:15:00.000Z',
        updated_at: '2025-01-27T09:15:00.000Z',
        published_at: '2025-01-27T09:15:00.000Z',
        view_count: 178,
        reactions_heart: 32,
        comments_count: 6,
        tags: ['react', 'hooks', 'frontend'],
        featured: false,
        read_time: '10 min read',
        author_id: 'admin',
        type_id: 'tutorial',
        post_images: []
      }
    ]

    const post = fallbackPosts.find(p => p.slug === slug)
    return post ? this.mapPostFromDatabase(post) : null
  }

  /**
   * Get featured posts with complete data
   */
  async getFeaturedPosts(limit: number = 6): Promise<any[]> {
    try {
      const { data: posts, error } = await this.supabase
        .from('posts')
        .select(`
          *,
          post_images (*)
        `)
        .eq('featured', true)
        .eq('status', 'PUBLISHED')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Supabase error in getFeaturedPosts:', error)
        // Return fallback featured posts
        const fallbackData = this.getFallbackPosts({ limit }, { featured: true })
        return fallbackData.posts
      }

      return (posts || []).map(post => this.mapPostFromDatabase(post))
    } catch (error) {
      console.error('Error in getFeaturedPosts:', error)
      // Return fallback featured posts
      const fallbackData = this.getFallbackPosts({ limit }, { featured: true })
      return fallbackData.posts
    }
  }

  /**
   * Search posts (simple version)
   */
  async searchPosts(
    query: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: any[]; total: number }> {
    return this.findAll(pagination, { search: query })
  }

  /**
   * Get posts by status (simple version)
   */
  async getPostsByStatus(
    status: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: any[]; total: number }> {
    return this.findAll(pagination, { status: status as any })
  }

  /**
   * Get recent posts (simple version)
   */
  async getRecentPosts(limit: number = 10): Promise<any[]> {
    try {
      const { posts } = await this.findAll(
        { limit, sortBy: 'created_at', sortOrder: 'desc' },
        {}
      )
      return posts
    } catch (error) {
      console.error('Error in getRecentPosts:', error)
      throw error
    }
  }

  /**
   * Get most viewed posts (simple version)
   */
  async getMostViewedPosts(limit: number = 5): Promise<any[]> {
    try {
      const { posts } = await this.findAll(
        { limit, sortBy: 'view_count', sortOrder: 'desc' },
        {}
      )
      return posts
    } catch (error) {
      console.error('Error in getMostViewedPosts:', error)
      throw error
    }
  }

  /**
   * Create a new post
   */
  async create(postData: {
    title: string
    slug: string
    content: string
    authorId: string
    typeId: string
    status: string
    featured?: boolean
    excerpt?: boolean
    version?: string
    tags?: string[]
    images?: Array<{
      url: string
      altText?: string
      caption?: string
      name: string
      size: number
    }>
  }): Promise<any> {
    try {
      console.log('Creating post with data:', {
        ...postData,
        images: postData.images?.length || 0
      })

      // Start transaction by creating the post first
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .insert({
          title: postData.title,
          slug: postData.slug,
          content: postData.content,
          author_id: postData.authorId,
          type_id: postData.typeId,
          status: postData.status.toUpperCase(),
          featured: postData.featured || false,
          excerpt: postData.excerpt || false,
          version: postData.version || '1.0.0',
          tags: postData.tags || [],
          published_at: postData.status.toUpperCase() === 'PUBLISHED' ? new Date().toISOString() : null
        })
        .select()
        .single()

      if (postError) {
        console.error('Supabase error in create post:', postError)
        throw new Error(`Failed to create post: ${postError.message}`)
      }

      console.log('Post created successfully:', post.id)

      // Create post images if any
      if (postData.images && postData.images.length > 0) {
        console.log('📸 Creating post images:', postData.images.length)

        const imageInserts = postData.images.map((image, index) => ({
          post_id: post.id,
          url: image.url,
          alt_text: image.altText || '',
          caption: image.caption || '',
          display_order: index + 1,
          file_name: image.name || `image-${index + 1}`,
          file_size: image.size || 0
        }))

        const { error: imagesError } = await this.supabase
          .from('post_images')
          .insert(imageInserts)

        if (imagesError) {
          console.error('Supabase error in create post images:', imagesError)
          // Don't throw error for images, just log it
          console.warn('Failed to create post images, but post was created successfully')
        } else {
          console.log('✅ Post images created successfully')
        }
      }

      return this.mapPostFromDatabase(post)
    } catch (error) {
      console.error('Error in create:', error)
      throw error
    }
  }

  /**
   * Check if slug is available
   */
  async isSlugAvailable(slug: string, excludeId?: string): Promise<boolean> {
    try {
      let query = this.supabase
        .from('posts')
        .select('id')
        .eq('slug', slug)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query.limit(1)

      if (error) {
        console.error('Supabase error in isSlugAvailable:', error)
        throw new Error(`Failed to check slug availability: ${error.message}`)
      }

      return !data || data.length === 0
    } catch (error) {
      console.error('Error in isSlugAvailable:', error)
      throw error
    }
  }

  /**
   * Update an existing post
   */
  async update(postId: string, postData: {
    title: string
    slug: string
    content: string
    typeId: string
    status: string
    featured?: boolean
    excerpt?: boolean
    version?: string
    tags?: string[]
    images?: Array<{
      url: string
      altText?: string
      caption?: string
      name: string
      size: number
    }>
  }): Promise<any> {
    try {
      console.log('Updating post with data:', {
        ...postData,
        images: postData.images?.length || 0
      })

      // Update post data
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .update({
          title: postData.title,
          slug: postData.slug,
          content: postData.content,
          type_id: postData.typeId,
          status: postData.status.toUpperCase(),
          featured: postData.featured || false,
          excerpt: postData.excerpt || false,
          version: postData.version,
          tags: postData.tags || [],
          published_at: postData.status.toUpperCase() === 'PUBLISHED' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', postId)
        .select()
        .single()

      if (postError) {
        console.error('Supabase error in update post:', postError)
        throw new Error(`Failed to update post: ${postError.message}`)
      }

      // Handle images if provided
      if (postData.images && postData.images.length > 0) {
        // First delete existing images
        const { error: deleteError } = await this.supabase
          .from('post_images')
          .delete()
          .eq('post_id', postId)

        if (deleteError) {
          console.error('Supabase error deleting old images:', deleteError)
          // Don't throw error, just log and continue
        }

        // Then insert new images
        const imageRecords = postData.images.map((img, index) => ({
          post_id: postId,
          url: img.url,
          alt_text: img.altText || '',
          caption: img.caption || '',
          file_name: img.name,
          file_size: img.size,
          display_order: index + 1
        }))

        const { error: imagesError } = await this.supabase
          .from('post_images')
          .insert(imageRecords)

        if (imagesError) {
          console.error('Supabase error inserting new images:', imagesError)
          // Don't throw error, just log and continue
        }
      }

      console.log('Post updated successfully:', post.id)
      return this.mapPostFromDatabase(post)
    } catch (error) {
      console.error('Error in update:', error)
      throw error
    }
  }
}
