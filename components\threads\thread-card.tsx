import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Heart,
  ThumbsUp,
  Brain,
  MessageSquare,
  Clock,
  GitBranch,
  Calendar,
  ImageIcon,
  ExternalLink
} from "lucide-react"
import Link from "next/link"
import NextImage from "next/image"
import { Thread } from "@/lib/types"
import { THREAD_TYPE_CONFIG } from "@/lib/constants"
import { extractContentPreview } from "@/lib/utils"
import { ThreadSlideshow } from "@/components/thread-slideshow"

interface ThreadCardProps {
  thread: Thread
  variant?: 'compact' | 'full'
  showImages?: boolean
}

export function ThreadCard({
  thread,
  variant = 'full',
  showImages = true
}: ThreadCardProps) {
  const typeConfig = THREAD_TYPE_CONFIG[thread.type]
  const IconComponent = typeConfig.icon
  // Dynamic content preview length based on image presence for better height balance
  const hasImages = thread.images && thread.images.length > 0
  const contentLength = variant === 'compact'
    ? (hasImages ? 90 : 450)  // Shorter for cards with images, much longer for text-only cards
    : 200
  const contentPreview = extractContentPreview(thread.content, contentLength)

  if (variant === 'compact') {
    return (
      <Card className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20 h-full min-h-[300px] w-96 flex-shrink-0 flex flex-col theme-transition">
        <CardContent className="p-3 flex flex-col h-full">
          {/* Compact Header */}
          <div className="flex items-start justify-between gap-2 mb-2">
            <h3 className="text-base font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-green-500 dark:group-hover:text-green-400 transition-colors duration-200 flex-1 theme-transition leading-tight">
              {thread.title}
            </h3>
            <Badge className={`${typeConfig.color} shrink-0 text-xs`}>
              <IconComponent className="w-3 h-3 mr-1" />
              {typeConfig.label}
            </Badge>
          </div>

          {/* Compact Meta Info */}
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-600 mb-2 theme-transition">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{thread.readTime}m</span>
            </div>
            {thread.images && thread.images.length > 0 && (
              <div className="flex items-center gap-1">
                <ImageIcon className="w-3 h-3" />
                <span>{thread.images.length}</span>
              </div>
            )}
          </div>

          {/* Compact Content Preview */}
          <p className={`text-xs text-gray-600 dark:text-gray-400 mb-2 leading-relaxed theme-transition ${
            hasImages ? 'line-clamp-2' : 'line-clamp-8'
          }`}>
            {contentPreview}
          </p>

          {/* Compact Image Display - Horizontal Layout */}
          {showImages && thread.images && thread.images.length > 0 && (
            <div className="mb-2">
              {thread.images.length === 1 ? (
                <div className="relative overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 w-20 h-20 theme-transition">
                  <NextImage
                    src={thread.images[0].url || "/placeholder.svg"}
                    alt={thread.images[0].alt || "Thread image"}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="flex gap-1">
                  {thread.images.slice(0, 4).map((image, index) => (
                    <div
                      key={image.id}
                      className="relative overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 w-16 h-16 theme-transition"
                    >
                      <NextImage
                        src={image.url || "/placeholder.svg"}
                        alt={image.alt || `Image ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                      {index === 3 && thread.images!.length > 4 && (
                        <div className="absolute inset-0 bg-gray-900/60 dark:bg-black/60 flex items-center justify-center theme-transition">
                          <span className="text-white text-xs font-medium">
                            +{thread.images!.length - 4}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Compact Tags */}
          <div className="flex flex-wrap gap-1 mb-2 flex-grow">
            {thread.tags.slice(0, 2).map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200 px-1.5 py-0.5 theme-transition"
              >
                #{tag}
              </Badge>
            ))}
            {thread.tags.length > 2 && (
              <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-500 px-1.5 py-0.5 theme-transition">
                +{thread.tags.length - 2}
              </Badge>
            )}
          </div>

          {/* Compact Stats and CTA */}
          <div className="flex items-center justify-between mt-auto">
            <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-500 theme-transition">
              <div className="flex items-center gap-1 hover:text-red-400 transition-colors duration-200">
                <Heart className="w-3 h-3" />
                <span>{thread.reactions.heart}</span>
              </div>
              <div className="flex items-center gap-1 hover:text-blue-400 transition-colors duration-200">
                <ThumbsUp className="w-3 h-3" />
                <span>{thread.reactions.thumbsUp}</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageSquare className="w-3 h-3" />
                <span>{thread.comments}</span>
              </div>
            </div>
            <Link href={`/thread/${thread.id}`}>
              <Button
                variant="ghost"
                size="sm"
                className="text-green-500 hover:text-green-600 hover:bg-green-500/10 dark:text-green-400 dark:hover:text-green-300 transition-all duration-200 text-xs px-2 py-1 h-auto theme-transition"
              >
                Read
                <ExternalLink className="w-3 h-3 ml-1" />
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Full variant
  return (
    <Card className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20 theme-transition">
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-start justify-between gap-3">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-green-500 dark:group-hover:text-green-400 transition-colors duration-200 theme-transition">
              {thread.title}
            </h3>
            <Badge className={`${typeConfig.color} shrink-0`}>
              <IconComponent className="w-3 h-3 mr-1" />
              {typeConfig.label}
            </Badge>
          </div>

          <p className="text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed theme-transition">
            {contentPreview}
          </p>

          {/* Slideshow for multiple images */}
          {showImages && thread.images && thread.images.length > 1 && (
            <div className="my-4">
              <ThreadSlideshow images={thread.images} autoPlay={false} showThumbnails={true} />
            </div>
          )}

          {/* Single image display */}
          {showImages && thread.images && thread.images.length === 1 && (
            <div className="my-4">
              <div className="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800 theme-transition">
                <NextImage
                  src={thread.images[0].url || "/placeholder.svg"}
                  alt={thread.images[0].alt || "Thread image"}
                  width={800}
                  height={400}
                  className="w-full h-auto object-cover"
                />
              </div>
              {thread.images[0].caption && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 italic theme-transition">{thread.images[0].caption}</p>
              )}
            </div>
          )}

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-500 theme-transition">
            <div className="flex items-center gap-1">
              <GitBranch className="w-3 h-3" />
              <span>{thread.version}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{thread.readTime}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{thread.updatedAt}</span>
            </div>
            {thread.images && thread.images.length > 0 && (
              <div className="flex items-center gap-1">
                <ImageIcon className="w-3 h-3" />
                <span>
                  {thread.images.length} image{thread.images.length !== 1 ? "s" : ""}
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            {thread.tags.map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200 theme-transition"
              >
                #{tag}
              </Badge>
            ))}
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-800/50 theme-transition">
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-500 theme-transition">
              <div className="flex items-center gap-1 hover:text-blue-400 transition-colors duration-200">
                <ThumbsUp className="w-4 h-4" />
                <span>{thread.reactions.thumbsUp}</span>
              </div>
              <div className="flex items-center gap-1 hover:text-red-400 transition-colors duration-200">
                <Heart className="w-4 h-4" />
                <span>{thread.reactions.heart}</span>
              </div>
              <div className="flex items-center gap-1 hover:text-purple-400 transition-colors duration-200">
                <Brain className="w-4 h-4" />
                <span>{thread.reactions.brain}</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageSquare className="w-4 h-4" />
                <span>{thread.comments}</span>
              </div>
            </div>
            <Link href={`/thread/${thread.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="border-green-500/30 text-green-500 hover:bg-green-500/10 hover:border-green-500 dark:text-green-400 transition-all duration-200 theme-transition"
              >
                Read Article
                <ExternalLink className="w-3 h-3 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
