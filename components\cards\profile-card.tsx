import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MapPin, Calendar, Code2, Sparkles, Zap, Star, Cpu } from "lucide-react"
import { Profile } from "@/types"
import { SOCIAL_LINKS } from "@/lib/constants"

interface ProfileCardProps {
  profile: Profile
  locale: string
  translations: {
    title: string
    status: string
    contact: string
  }
}

export function ProfileCard({ profile, locale, translations }: ProfileCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
  }

  const getSocialUrl = (platform: string, username: string) => {
    switch (platform) {
      case 'github':
        return `https://github.com/${username}`
      case 'instagram':
        return `https://instagram.com/${username}`
      case 'facebook':
        return `https://facebook.com/${username}`
      case 'linkedin':
        return `https://linkedin.com/in/${username}`
      case 'email':
        return `mailto:${username}`
      default:
        return '#'
    }
  }

  return (
    <Card className="group relative overflow-hidden bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl theme-transition hover:scale-[1.02] transition-all duration-300">
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }} />

      <CardContent className="relative p-8 pt-10">
        {/* Decorative Header Background */}
        <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-br from-green-500/10 via-emerald-500/5 to-teal-500/10 dark:from-green-400/10 dark:via-emerald-400/5 dark:to-teal-400/10" />
        <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-r from-transparent via-green-500/5 to-transparent" />

        {/* Floating Code Elements */}
        <div className="absolute top-4 left-4 opacity-20 dark:opacity-30">
          <div className="text-xs font-mono text-green-600 dark:text-green-400 transform -rotate-12">
            &lt;dev/&gt;
          </div>
        </div>
        <div className="absolute top-6 right-6 opacity-20 dark:opacity-30">
          <div className="text-xs font-mono text-green-600 dark:text-green-400 transform rotate-12">
            {`{ }`}
          </div>
        </div>
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 opacity-15 dark:opacity-25">
          <div className="text-xs font-mono text-green-600 dark:text-green-400">
            console.log('hello')
          </div>
        </div>

        {/* Profile Header */}
        <div className="text-center mb-6 relative z-10">
          <div className="relative inline-block mb-4 group/avatar">
            {/* Background Circle with Pattern */}
            <div className="absolute inset-0 w-36 h-36 -m-6 rounded-full bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/20 dark:via-emerald-900/20 dark:to-teal-900/20 opacity-60 group-hover/avatar:opacity-80 transition-opacity duration-500" />

            {/* Hexagon Pattern Background */}
            <div className="absolute inset-0 w-36 h-36 -m-6 rounded-full opacity-10 dark:opacity-20" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.4'%3E%3Cpath d='M20 20l-8-4.64v-9.28L20 1.44l8 4.64v9.28L20 20zm8-15.36L20 9.28 12 4.64V15.36l8 4.64 8-4.64V4.64z'/%3E%3C/g%3E%3C/svg%3E")`
            }} />

            {/* Outer Ring - Static */}
            <div className="absolute inset-0 w-32 h-32 -m-4">
              <div className="absolute inset-0 rounded-full border-2 border-dashed border-green-500/30 dark:border-green-400/30 group-hover/avatar:border-green-500/50 dark:group-hover/avatar:border-green-400/50 transition-colors duration-300" />
              <div className="absolute inset-2 rounded-full border border-gray-300/20 dark:border-gray-600/20 group-hover/avatar:border-gray-300/40 dark:group-hover/avatar:border-gray-600/40 transition-colors duration-300" />
            </div>

            {/* Multi-layered Glow Effects */}
            <div className="absolute inset-0 w-28 h-28 -m-2">
              {/* Primary glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 rounded-full blur-xl opacity-20 group-hover/avatar:opacity-40 transition-all duration-500 animate-pulse" />
              {/* Secondary glow */}
              <div className="absolute inset-2 bg-gradient-to-r from-blue-500 via-cyan-500 to-green-500 rounded-full blur-lg opacity-15 group-hover/avatar:opacity-30 transition-all duration-700" />
              {/* Tertiary glow */}
              <div className="absolute inset-4 bg-gradient-to-r from-emerald-400 to-green-400 rounded-full blur-md opacity-25 group-hover/avatar:opacity-50 transition-all duration-300" />
            </div>

            {/* Border Frame */}
            <div className="absolute inset-0 w-28 h-28 -m-2 opacity-0 group-hover/avatar:opacity-100 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full p-[2px]">
                <div className="w-full h-full bg-white dark:bg-gray-900 rounded-full" />
              </div>
            </div>

            {/* Main Avatar Container */}
            <div className="relative w-24 h-24 group-hover/avatar:scale-110 transition-all duration-500 ease-out">
              {/* Static Border */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 via-emerald-500 to-green-500 rounded-full p-[3px]">
                <div className="w-full h-full bg-white dark:bg-gray-900 rounded-full p-[1px]">
                  <Avatar className="w-full h-full shadow-2xl group-hover/avatar:shadow-green-500/25 transition-all duration-300">
                    <AvatarImage src={profile.avatar || "/placeholder.svg"} className="object-cover" />
                    <AvatarFallback className="bg-gradient-to-br from-green-600 via-emerald-600 to-teal-600 text-white text-xl font-bold relative overflow-hidden">
                      {/* Animated background pattern for fallback */}
                      <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 via-transparent to-blue-500/20 animate-pulse" />
                      <span className="relative z-10">{getInitials(profile.name)}</span>
                    </AvatarFallback>
                  </Avatar>
                </div>
              </div>
            </div>

            {/* Floating Tech Icons */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center opacity-0 group-hover/avatar:opacity-100 transition-all duration-500 hover:scale-125 shadow-lg animate-bounce [animation-delay:0s] border-2 border-white dark:border-gray-900">
              <Code2 className="w-4 h-4 text-white" />
            </div>

            <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center opacity-0 group-hover/avatar:opacity-100 transition-all duration-500 hover:scale-125 shadow-lg animate-bounce [animation-delay:0.2s] border-2 border-white dark:border-gray-900">
              <Cpu className="w-4 h-4 text-white" />
            </div>

            <div className="absolute -bottom-4 -left-4 w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center opacity-0 group-hover/avatar:opacity-100 transition-all duration-500 hover:scale-125 shadow-lg animate-bounce [animation-delay:0.4s] border-2 border-white dark:border-gray-900">
              <Zap className="w-4 h-4 text-white" />
            </div>

            {/* Additional Decorative Elements */}
            <div className="absolute -bottom-4 -right-4 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center opacity-0 group-hover/avatar:opacity-100 transition-all duration-500 hover:scale-125 shadow-lg animate-bounce [animation-delay:0.6s] border-2 border-white dark:border-gray-900">
              <Sparkles className="w-4 h-4 text-white" />
            </div>

            {/* Particle Effects */}
            <div className="absolute inset-0 w-32 h-32 -m-4 pointer-events-none opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-700">
              {/* Static positioned particles to avoid hydration mismatch */}
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[20%] left-[90%]" style={{ animationDelay: '0s', animationDuration: '2s' }} />
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[55%] left-[85%]" style={{ animationDelay: '0.2s', animationDuration: '2s' }} />
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[80%] left-[50%]" style={{ animationDelay: '0.4s', animationDuration: '2s' }} />
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[80%] left-[15%]" style={{ animationDelay: '0.6s', animationDuration: '2s' }} />
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[55%] left-[10%]" style={{ animationDelay: '0.8s', animationDuration: '2s' }} />
              <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping top-[20%] left-[15%]" style={{ animationDelay: '1s', animationDuration: '2s' }} />
            </div>
          </div>

          {/* Enhanced Name and Title */}
          <div className="space-y-2 mb-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1 theme-transition group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
              {profile.name}
            </h2>

            {/* Title with Icon */}
            <div className="flex items-center justify-center gap-2">
              <Sparkles className="w-4 h-4 text-green-500 dark:text-green-400 animate-pulse" />
              <p className="text-green-500 dark:text-green-400 font-medium theme-transition bg-green-50 dark:bg-green-900/20 px-3 py-1 rounded-full text-sm">
                {profile.title}
              </p>
              <Sparkles className="w-4 h-4 text-green-500 dark:text-green-400 animate-pulse" />
            </div>
          </div>

          {/* Enhanced Bio */}
          <div className="relative">
            <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed theme-transition max-w-none">
              {profile.bio}
            </p>
            {/* Subtle highlight on hover */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg" />
          </div>
        </div>

        {/* Enhanced Profile Info */}
        <div className="space-y-3 mb-6">
          <div className="group/info flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400 p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-800/30 theme-transition hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 border border-gray-200/50 dark:border-gray-700/50 hover:border-green-200 dark:hover:border-green-700/50 transition-all duration-300">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center group-hover/info:bg-green-200 dark:group-hover/info:bg-green-800/50 transition-colors duration-300">
              <MapPin className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <span className="font-medium">{profile.location}</span>
          </div>

          <div className="group/info flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400 p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-800/30 theme-transition hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 border border-gray-200/50 dark:border-gray-700/50 hover:border-green-200 dark:hover:border-green-700/50 transition-all duration-300">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center group-hover/info:bg-green-200 dark:group-hover/info:bg-green-800/50 transition-colors duration-300">
              <Calendar className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <span className="font-medium">{locale === 'id' ? 'Bergabung' : 'Joined'} {profile.joinDate}</span>
          </div>
        </div>

        {/* Enhanced Social Links */}
        <div className="space-y-3">
          <div className="flex items-center justify-center gap-1 mb-2">
            <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent flex-1" />
            <span className="text-xs text-gray-500 dark:text-gray-400 px-2 font-medium">{locale === 'id' ? 'Terhubung' : 'Connect'}</span>
            <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent flex-1" />
          </div>

          <div className="flex justify-center gap-2">
            {SOCIAL_LINKS.map((social, index) => (
              <Button
                key={social.key}
                variant="ghost"
                size="sm"
                className={`group/social relative w-10 h-10 p-0 transition-all duration-300 hover:scale-110 rounded-lg ${
                  social.key === 'github'
                    ? 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800'
                    : social.key === 'instagram'
                    ? 'text-gray-600 dark:text-gray-400 hover:text-pink-500 dark:hover:text-pink-400 hover:bg-pink-50 dark:hover:bg-pink-900/20'
                    : social.key === 'facebook'
                    ? 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    : social.key === 'linkedin'
                    ? 'text-gray-600 dark:text-gray-400 hover:text-blue-700 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20'
                }`}
                asChild
              >
                <a
                  href={getSocialUrl(social.key, profile.social[social.key])}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                >
                  <social.icon className="w-4 h-4 transition-transform duration-300 group-hover/social:scale-110" />
                </a>
              </Button>
            ))}
          </div>

          {/* Bottom Decorative Elements */}
          <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-green-500/10 via-emerald-500/20 to-green-500/10 rounded-b-xl" />
          <div className="absolute bottom-0 left-1/4 right-1/4 h-1 bg-gradient-to-r from-transparent via-green-500/30 to-transparent" />
        </div>
      </CardContent>
    </Card>
  )
}
