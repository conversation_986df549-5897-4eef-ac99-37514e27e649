"use client"

import { useState, useCallback } from 'react'

export interface Reactions {
  thumbsUp: number
  heart: number
  brain: number
}

export type ReactionType = 'thumbsUp' | 'heart' | 'brain'

interface UseReactionsOptions {
  initialReactions: Reactions
  onReactionAdd?: (type: ReactionType, newCount: number) => void
  optimistic?: boolean
}

interface UseReactionsReturn {
  reactions: Reactions
  addReaction: (type: ReactionType) => void
  setReactions: (reactions: Reactions) => void
  isLoading: boolean
  error: string | null
}

/**
 * Custom hook for managing reaction state and interactions
 */
export function useReactions({
  initialReactions,
  onReactionAdd,
  optimistic = true
}: UseReactionsOptions): UseReactionsReturn {
  const [reactions, setReactions] = useState<Reactions>(initialReactions)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const addReaction = useCallback(async (type: ReactionType) => {
    if (isLoading) return

    const newCount = reactions[type] + 1

    // Optimistic update
    if (optimistic) {
      setReactions(prev => ({
        ...prev,
        [type]: newCount
      }))
    }

    try {
      setIsLoading(true)
      setError(null)

      // Call the callback if provided
      if (onReactionAdd) {
        await onReactionAdd(type, newCount)
      }

      // If not optimistic, update after success
      if (!optimistic) {
        setReactions(prev => ({
          ...prev,
          [type]: newCount
        }))
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add reaction')
      
      // Revert optimistic update on error
      if (optimistic) {
        setReactions(prev => ({
          ...prev,
          [type]: prev[type] - 1
        }))
      }
    } finally {
      setIsLoading(false)
    }
  }, [reactions, isLoading, onReactionAdd, optimistic])

  return {
    reactions,
    addReaction,
    setReactions,
    isLoading,
    error
  }
}
