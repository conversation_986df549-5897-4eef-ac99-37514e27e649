import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import crypto from 'crypto'

export interface PostInteractionData {
  postId: string
  userId?: string
  userIdentifier: string
  ipAddress?: string
  userAgent?: string
}

export interface LikeStatus {
  hasLiked: boolean
  likeCount: number
  userLikeCount: number
}

export interface ViewStatus {
  hasViewedToday: boolean
  viewCount: number
  lastViewedAt?: string
}

export class PostInteractionsRepository {
  private supabase = createServerSupabaseAdminClient()

  /**
   * Generate user identifier for anonymous users
   */
  generateUserIdentifier(ipAddress: string, userAgent: string): string {
    const combined = `${ipAddress}:${userAgent}`
    return crypto.createHash('sha256').update(combined).digest('hex')
  }

  /**
   * Add a like to a post
   */
  async addLike(data: PostInteractionData): Promise<{ success: boolean; likeCount: number }> {
    try {
      console.log('Adding like for post:', data.postId)

      const { error } = await this.supabase
        .from('post_likes')
        .insert({
          post_id: data.postId,
          user_id: data.userId || null,
          user_identifier: data.userIdentifier
        })

      if (error) {
        console.error('Error adding like:', error)

        // Check if table doesn't exist
        if (error.message.includes('does not exist')) {
          throw new Error('Post interactions system not set up. Please run database migration.')
        }

        throw new Error(`Failed to add like: ${error.message}`)
      }

      // Get updated like count
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .select('reactions_heart')
        .eq('id', data.postId)
        .single()

      if (postError) {
        console.error('Error getting updated like count:', postError)
        throw new Error(`Failed to get like count: ${postError.message}`)
      }

      console.log('Like added successfully, new count:', post.reactions_heart)
      return {
        success: true,
        likeCount: post.reactions_heart || 0
      }

    } catch (error) {
      console.error('Error in addLike:', error)
      throw error
    }
  }

  /**
   * Get like status for a user on a post
   */
  async getLikeStatus(postId: string, userIdentifier: string): Promise<LikeStatus> {
    try {
      // Get total likes for post
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .select('reactions_heart')
        .eq('id', postId)
        .single()

      if (postError) {
        console.error('Error getting post like count:', postError)
        throw new Error(`Failed to get post: ${postError.message}`)
      }

      // Get user's likes for this post
      const { data: userLikes, error: likesError } = await this.supabase
        .from('post_likes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_identifier', userIdentifier)

      if (likesError) {
        console.error('Error getting user likes:', likesError)
        throw new Error(`Failed to get user likes: ${likesError.message}`)
      }

      return {
        hasLiked: (userLikes?.length || 0) > 0,
        likeCount: post.reactions_heart || 0,
        userLikeCount: userLikes?.length || 0
      }

    } catch (error) {
      console.error('Error in getLikeStatus:', error)
      throw error
    }
  }

  /**
   * Add a view to a post (max 1 per 24h per user)
   */
  async addView(data: PostInteractionData): Promise<{ success: boolean; viewCount: number; wasNewView: boolean }> {
    try {
      console.log('Adding view for post:', data.postId)

      // Check if user has viewed this post today
      const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
      
      const { data: existingView, error: checkError } = await this.supabase
        .from('post_views')
        .select('id')
        .eq('post_id', data.postId)
        .eq('user_identifier', data.userIdentifier)
        .gte('viewed_at', `${today}T00:00:00.000Z`)
        .lt('viewed_at', `${today}T23:59:59.999Z`)
        .limit(1)

      if (checkError) {
        console.error('Error checking existing view:', checkError)
        throw new Error(`Failed to check existing view: ${checkError.message}`)
      }

      let wasNewView = false

      // Only add view if user hasn't viewed today
      if (!existingView || existingView.length === 0) {
        const { error: insertError } = await this.supabase
          .from('post_views')
          .insert({
            post_id: data.postId,
            user_id: data.userId || null,
            user_identifier: data.userIdentifier
          })

        if (insertError) {
          console.error('Error adding view:', insertError)
          throw new Error(`Failed to add view: ${insertError.message}`)
        }

        wasNewView = true
        console.log('New view added successfully')
      } else {
        console.log('User has already viewed this post today')
      }

      // Get updated view count
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .select('view_count')
        .eq('id', data.postId)
        .single()

      if (postError) {
        console.error('Error getting updated view count:', postError)
        throw new Error(`Failed to get view count: ${postError.message}`)
      }

      return {
        success: true,
        viewCount: post.view_count || 0,
        wasNewView
      }

    } catch (error) {
      console.error('Error in addView:', error)
      throw error
    }
  }

  /**
   * Get view status for a user on a post
   */
  async getViewStatus(postId: string, userIdentifier: string): Promise<ViewStatus> {
    try {
      // Get total views for post
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .select('view_count')
        .eq('id', postId)
        .single()

      if (postError) {
        console.error('Error getting post view count:', postError)
        throw new Error(`Failed to get post: ${postError.message}`)
      }

      // Check if user has viewed today
      const today = new Date().toISOString().split('T')[0]
      
      const { data: todayView, error: viewError } = await this.supabase
        .from('post_views')
        .select('viewed_at')
        .eq('post_id', postId)
        .eq('user_identifier', userIdentifier)
        .gte('viewed_at', `${today}T00:00:00.000Z`)
        .lt('viewed_at', `${today}T23:59:59.999Z`)
        .order('viewed_at', { ascending: false })
        .limit(1)

      if (viewError) {
        console.error('Error getting user view status:', viewError)
        throw new Error(`Failed to get view status: ${viewError.message}`)
      }

      return {
        hasViewedToday: (todayView?.length || 0) > 0,
        viewCount: post.view_count || 0,
        lastViewedAt: todayView?.[0]?.viewed_at
      }

    } catch (error) {
      console.error('Error in getViewStatus:', error)
      throw error
    }
  }

  /**
   * Get interaction summary for a post
   */
  async getPostInteractionSummary(postId: string, userIdentifier?: string) {
    try {
      const [post, likeStatus, viewStatus] = await Promise.all([
        this.supabase
          .from('posts')
          .select('reactions_heart, view_count')
          .eq('id', postId)
          .single(),
        userIdentifier ? this.getLikeStatus(postId, userIdentifier) : null,
        userIdentifier ? this.getViewStatus(postId, userIdentifier) : null
      ])

      if (post.error) {
        throw new Error(`Failed to get post: ${post.error.message}`)
      }

      return {
        likeCount: post.data.reactions_heart || 0,
        viewCount: post.data.view_count || 0,
        userStatus: userIdentifier ? {
          hasLiked: likeStatus?.hasLiked || false,
          userLikeCount: likeStatus?.userLikeCount || 0,
          hasViewedToday: viewStatus?.hasViewedToday || false,
          lastViewedAt: viewStatus?.lastViewedAt
        } : null
      }

    } catch (error) {
      console.error('Error in getPostInteractionSummary:', error)
      throw error
    }
  }
}
