'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { Palette, Pipette, RotateCcw } from 'lucide-react'

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
  label?: string
  className?: string
  disabled?: boolean
  showPresets?: boolean
  showGradient?: boolean
}

const PRESET_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
  '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#2C3E50',
  '#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6',
  '#1ABC9C', '#34495E', '#E67E22', '#E91E63', '#8E44AD'
]

// Color conversion utilities
const hexToHsv = (hex: string): [number, number, number] => {
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min

  let h = 0
  if (diff !== 0) {
    if (max === r) h = ((g - b) / diff) % 6
    else if (max === g) h = (b - r) / diff + 2
    else h = (r - g) / diff + 4
  }
  h = Math.round(h * 60)
  if (h < 0) h += 360

  const s = max === 0 ? 0 : diff / max
  const v = max

  return [h, s * 100, v * 100]
}

const hsvToHex = (h: number, s: number, v: number): string => {
  h = h / 360
  s = s / 100
  v = v / 100

  const c = v * s
  const x = c * (1 - Math.abs(((h * 6) % 2) - 1))
  const m = v - c

  let r = 0, g = 0, b = 0
  if (0 <= h && h < 1/6) { r = c; g = x; b = 0 }
  else if (1/6 <= h && h < 2/6) { r = x; g = c; b = 0 }
  else if (2/6 <= h && h < 3/6) { r = 0; g = c; b = x }
  else if (3/6 <= h && h < 4/6) { r = 0; g = x; b = c }
  else if (4/6 <= h && h < 5/6) { r = x; g = 0; b = c }
  else if (5/6 <= h && h < 1) { r = c; g = 0; b = x }

  r = Math.round((r + m) * 255)
  g = Math.round((g + m) * 255)
  b = Math.round((b + m) * 255)

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

export function ColorPicker({
  value,
  onChange,
  label,
  className,
  disabled = false,
  showPresets = true,
  showGradient = false
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState(value)
  const [hsv, setHsv] = useState<[number, number, number]>([0, 100, 100])
  const [isDragging, setIsDragging] = useState(false)
  const [popoverSide, setPopoverSide] = useState<'top' | 'bottom'>('bottom')
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const triggerRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    setInputValue(value)
    if (value.startsWith('#') && value.length === 7) {
      setHsv(hexToHsv(value))
    }
  }, [value])

  // Detect viewport position and set popover direction
  useEffect(() => {
    const detectPopoverPosition = () => {
      if (triggerRef.current && isOpen) {
        const triggerRect = triggerRef.current.getBoundingClientRect()
        const viewportHeight = window.innerHeight
        const popoverHeight = 500 // Estimated height of color picker popup
        const spaceBelow = viewportHeight - triggerRect.bottom
        const spaceAbove = triggerRect.top
        
        // If there's not enough space below and there's more space above, show popup above
        if (spaceBelow < popoverHeight && spaceAbove > spaceBelow) {
          setPopoverSide('top')
        } else {
          setPopoverSide('bottom')
        }
      }
    }

    if (isOpen) {
      detectPopoverPosition()
      window.addEventListener('resize', detectPopoverPosition)
      window.addEventListener('scroll', detectPopoverPosition)
      
      return () => {
        window.removeEventListener('resize', detectPopoverPosition)
        window.removeEventListener('scroll', detectPopoverPosition)
      }
    }
  }, [isOpen])

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue)
    if (isValidColor(newValue)) {
      onChange(newValue)
    }
  }

  const isValidColor = (color: string): boolean => {
    const s = new Option().style
    s.color = color
    return s.color !== ''
  }

  const handlePresetClick = (color: string) => {
    setInputValue(color)
    onChange(color)
    if (color.startsWith('#') && color.length === 7) {
      setHsv(hexToHsv(color))
    }
    setIsOpen(false)
  }

  const resetToDefault = () => {
    const defaultColor = '#3B82F6'
    setInputValue(defaultColor)
    onChange(defaultColor)
    setHsv(hexToHsv(defaultColor))
  }

  // Draw circular color picker canvas
  const drawColorPicker = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) {
      console.log('Canvas not found')
      return
    }

    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.log('Context not found')
      return
    }

    const width = canvas.width
    const height = canvas.height
    const centerX = width / 2
    const centerY = height / 2
    const radius = Math.min(width, height) / 2 - 10

    console.log('Drawing color picker', { width, height, centerX, centerY, radius })

    // Clear canvas with white background first
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, width, height)

    // Draw color wheel using traditional method
    for (let angle = 0; angle < 360; angle += 1) {
      const startAngle = (angle - 1) * Math.PI / 180
      const endAngle = angle * Math.PI / 180
      
      for (let r = 0; r < radius; r += 1) {
        const saturation = (r / radius) * 100
        const hex = hsvToHex(angle, saturation, 100)
        
        ctx.strokeStyle = hex
        ctx.lineWidth = 2
        ctx.beginPath()
        ctx.arc(centerX, centerY, r, startAngle, endAngle)
        ctx.stroke()
      }
    }
  }, [])

  useEffect(() => {
    // Delay to ensure canvas is mounted and ready
    const timer = setTimeout(() => {
      drawColorPicker()
    }, 100)
    
    return () => clearTimeout(timer)
  }, [drawColorPicker])

  // Also redraw when popover opens
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        drawColorPicker()
      }, 50)
      
      return () => clearTimeout(timer)
    }
  }, [isOpen, drawColorPicker])

  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const radius = Math.min(canvas.width, canvas.height) / 2 - 10
    
    const dx = x - centerX
    const dy = y - centerY
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    // Only respond to clicks within the circle
    if (distance <= radius) {
      // Calculate hue from angle
      let angle = Math.atan2(dy, dx) * 180 / Math.PI
      if (angle < 0) angle += 360
      
      // Calculate saturation from distance
      const saturation = Math.min((distance / radius) * 100, 100)
      
      const newHsv: [number, number, number] = [angle, saturation, 100]
      setHsv(newHsv)
      
      const newColor = hsvToHex(newHsv[0], newHsv[1], newHsv[2])
      setInputValue(newColor)
      onChange(newColor)
    }
  }



  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const canvas = canvasRef.current
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      const centerX = canvas.width / 2
      const centerY = canvas.height / 2
      const radius = Math.min(canvas.width, canvas.height) / 2 - 10
      
      const dx = x - centerX
      const dy = y - centerY
      const distance = Math.sqrt(dx * dx + dy * dy)
      
      // Only respond to drags within the circle
      if (distance <= radius) {
        // Calculate hue from angle
        let angle = Math.atan2(dy, dx) * 180 / Math.PI
        if (angle < 0) angle += 360
        
        // Calculate saturation from distance
        const saturation = Math.min((distance / radius) * 100, 100)
        
        const newHsv: [number, number, number] = [angle, saturation, 100]
        setHsv(newHsv)
        
        const newColor = hsvToHex(newHsv[0], newHsv[1], newHsv[2])
        setInputValue(newColor)
        onChange(newColor)
      }
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, hsv])



  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </Label>
      )}
      
      <div className="flex items-center space-x-2">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              ref={triggerRef}
              variant="outline"
              className={cn(
                "w-12 h-10 p-0 border-2 rounded-lg transition-all duration-200 hover:scale-105",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              style={{
                background: value.startsWith('linear-gradient') ? value : value,
                borderColor: value.startsWith('#') ? value : '#e5e7eb'
              }}
              disabled={disabled}
            >
              <span className="sr-only">Pick color</span>
            </Button>
          </PopoverTrigger>
          
          <PopoverContent 
            className="w-64 max-w-[85vw] p-2 z-[99999] max-h-[75vh] overflow-y-auto" 
            align="start" 
            side="top" 
            sideOffset={8}
            avoidCollisions={true}
            collisionPadding={30}
          >
            <div className="space-y-4">
              {/* Circular Color Picker Canvas */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Color Wheel
                </Label>
                <div className="relative flex justify-center">
                  <div className="relative">
                    <canvas
                      ref={canvasRef}
                      width={150}
                      height={150}
                      className="border border-gray-300 dark:border-gray-600 rounded-full cursor-crosshair"
                      onClick={handleCanvasClick}
                      onMouseDown={() => setIsDragging(true)}
                    />
                    {/* Color indicator */}
                    <div
                      className="absolute w-4 h-4 border-2 border-white rounded-full pointer-events-none transform -translate-x-1/2 -translate-y-1/2"
                      style={{
                        left: `${75 + (hsv[1] / 100) * 67.5 * Math.cos(hsv[0] * Math.PI / 180)}px`,
                        top: `${75 + (hsv[1] / 100) * 67.5 * Math.sin(hsv[0] * Math.PI / 180)}px`,
                        boxShadow: '0 0 0 1px rgba(0,0,0,0.5)'
                      }}
                    />
                  </div>
                </div>
              </div>



              {/* Preset Colors */}
              {showPresets && (
                <div>
                  <Label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2 block">
                    Preset Colors
                  </Label>
                  <div className="grid grid-cols-10 gap-1">
                    {PRESET_COLORS.map((color, index) => (
                      <button
                        key={index}
                        onClick={() => handlePresetClick(color)}
                        className={cn(
                          "w-6 h-6 rounded border-2 transition-all duration-200 hover:scale-110",
                          value === color ? "border-gray-900 dark:border-white" : "border-gray-300 dark:border-gray-600"
                        )}
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Color Input */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Color Value
                </Label>
                <div className="flex space-x-2">
                  <Input
                    value={inputValue}
                    onChange={(e) => handleInputChange(e.target.value)}
                    placeholder="#3B82F6 or rgb(59, 130, 246)"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetToDefault}
                    className="px-3"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                </div>
              </div>


            </div>
          </PopoverContent>
        </Popover>
        
        <Input
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          placeholder="#3B82F6"
          className="flex-1"
          disabled={disabled}
        />
      </div>
    </div>
  )
}