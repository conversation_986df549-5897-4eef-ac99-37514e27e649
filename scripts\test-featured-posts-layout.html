<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Featured Posts Adaptive Layout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .layout-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-box {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .single-post {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .multiple-posts {
            border-color: #007bff;
            background-color: #d1ecf1;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .before {
            background-color: #f8d7da;
        }
        .after {
            background-color: #d4edda;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>⭐ Test: Featured Posts Adaptive Layout</h1>
    
    <div class="container">
        <div class="success">
            <h2>✅ New Feature: Smart Layout Adaptation!</h2>
            <p>Featured Posts sekarang menggunakan <strong>adaptive layout</strong> berdasarkan jumlah posts:</p>
            <ul>
                <li>📄 <strong>1 Post</strong> → Full width layout (variant="full")</li>
                <li>📋 <strong>2+ Posts</strong> → Horizontal scroll layout (variant="compact")</li>
                <li>🎯 <strong>Optimal UX</strong> → Layout sesuai dengan content</li>
                <li>📱 <strong>Responsive</strong> → Works di semua screen sizes</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎨 Layout Comparison</h2>
        
        <div class="layout-demo">
            <div class="demo-box single-post">
                <h3>📄 Single Post Layout</h3>
                <strong>When: posts.length === 1</strong>
                <ul>
                    <li>✅ Full width container</li>
                    <li>✅ PostCard variant="full"</li>
                    <li>✅ Better content visibility</li>
                    <li>✅ No horizontal scroll</li>
                    <li>✅ Larger images & text</li>
                </ul>
            </div>
            
            <div class="demo-box multiple-posts">
                <h3>📋 Multiple Posts Layout</h3>
                <strong>When: posts.length > 1</strong>
                <ul>
                    <li>✅ Horizontal scroll container</li>
                    <li>✅ PostCard variant="compact"</li>
                    <li>✅ Space-efficient display</li>
                    <li>✅ Scroll indicators</li>
                    <li>✅ Consistent card sizes</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Before vs After</h2>
        
        <table class="comparison-table">
            <tr>
                <th>Scenario</th>
                <th class="before">❌ Before (Fixed Layout)</th>
                <th class="after">✅ After (Adaptive Layout)</th>
            </tr>
            <tr>
                <td><strong>1 Featured Post</strong></td>
                <td class="before">Small compact card, wasted space</td>
                <td class="after">Full width card, optimal use of space</td>
            </tr>
            <tr>
                <td><strong>Multiple Posts</strong></td>
                <td class="before">Same compact layout</td>
                <td class="after">Same compact layout (no change)</td>
            </tr>
            <tr>
                <td><strong>Content Visibility</strong></td>
                <td class="before">Limited for single posts</td>
                <td class="after">Maximized for single posts</td>
            </tr>
            <tr>
                <td><strong>User Experience</strong></td>
                <td class="before">Inconsistent space usage</td>
                <td class="after">Optimal layout for each scenario</td>
            </tr>
            <tr>
                <td><strong>Mobile Experience</strong></td>
                <td class="before">Tiny cards on mobile</td>
                <td class="after">Full width cards when appropriate</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>🧪 Test Scenarios</h2>
        
        <div class="feature-item">
            <h3>Test 1: Single Featured Post</h3>
            <p><strong>Setup:</strong> Ensure only 1 post has featured status</p>
            <ol>
                <li>Go to homepage</li>
                <li>Scroll to Featured Posts section</li>
                <li>Verify single post uses full width</li>
                <li>Check that it uses "full" variant styling</li>
                <li>Verify no horizontal scroll</li>
            </ol>
            <a href="http://localhost:3000" class="link" target="_blank">🏠 Test Single Featured</a>
        </div>

        <div class="feature-item">
            <h3>Test 2: Multiple Featured Posts</h3>
            <p><strong>Setup:</strong> Ensure 2+ posts have featured status</p>
            <ol>
                <li>Go to homepage</li>
                <li>Scroll to Featured Posts section</li>
                <li>Verify posts use horizontal scroll layout</li>
                <li>Check that they use "compact" variant styling</li>
                <li>Test horizontal scrolling</li>
                <li>Verify scroll indicators appear (if >2 posts)</li>
            </ol>
            <a href="http://localhost:3000" class="link" target="_blank">📋 Test Multiple Featured</a>
        </div>

        <div class="feature-item">
            <h3>Test 3: Responsive Behavior</h3>
            <ol>
                <li>Test on desktop (wide screen)</li>
                <li>Test on tablet (medium screen)</li>
                <li>Test on mobile (narrow screen)</li>
                <li>Verify layout adapts properly</li>
                <li>Check touch scrolling on mobile</li>
            </ol>
            <div style="background: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <strong>Tip:</strong> Use browser dev tools to test different screen sizes
            </div>
        </div>

        <div class="feature-item">
            <h3>Test 4: Dynamic Content Changes</h3>
            <ol>
                <li>Start with multiple featured posts</li>
                <li>Remove featured status from all but one</li>
                <li>Refresh page</li>
                <li>Verify layout switches to single post layout</li>
                <li>Add more featured posts back</li>
                <li>Verify layout switches back to horizontal scroll</li>
            </ol>
            <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <strong>Note:</strong> Requires dashboard access to change featured status
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔍 What to Look For</h2>
        
        <div class="success">
            <h3>✅ Single Post Layout (Expected):</h3>
            <ul>
                <li><strong>Full Width:</strong> Post card spans entire container width</li>
                <li><strong>Larger Content:</strong> Bigger images, more text visible</li>
                <li><strong>Full Variant:</strong> Uses PostCard variant="full" styling</li>
                <li><strong>No Scroll:</strong> No horizontal scroll container</li>
                <li><strong>Better Spacing:</strong> Optimal use of available space</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Multiple Posts Layout (Expected):</h3>
            <ul>
                <li><strong>Horizontal Scroll:</strong> Cards in scrollable container</li>
                <li><strong>Compact Cards:</strong> Uses PostCard variant="compact"</li>
                <li><strong>Consistent Sizing:</strong> All cards same size</li>
                <li><strong>Scroll Indicators:</strong> Dots showing scroll progress (if >2)</li>
                <li><strong>Smooth Scrolling:</strong> Smooth scroll behavior</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ Potential Issues to Check:</h3>
            <ul>
                <li><strong>Layout Switching:</strong> Should adapt immediately</li>
                <li><strong>Styling Consistency:</strong> Proper variant application</li>
                <li><strong>Responsive Issues:</strong> Layout breaks on small screens</li>
                <li><strong>Scroll Behavior:</strong> Horizontal scroll not working</li>
                <li><strong>Content Overflow:</strong> Text or images cut off</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Implementation Details</h2>
        
        <div class="feature-item">
            <h3>🔧 Logic Implementation</h3>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">
const isSinglePost = posts.length === 1

{isSinglePost ? (
  // Single Post - Full Width Layout
  &lt;div className="w-full"&gt;
    &lt;PostCard variant="full" /&gt;
  &lt;/div&gt;
) : (
  // Multiple Posts - Horizontal Scroll Layout
  &lt;div className="flex gap-6 overflow-x-auto"&gt;
    {posts.map(post =&gt; (
      &lt;PostCard variant="compact" /&gt;
    ))}
  &lt;/div&gt;
)}</pre>
        </div>

        <div class="feature-item">
            <h3>🎨 Styling Differences</h3>
            <ul>
                <li><strong>Single Post:</strong> w-full container, variant="full"</li>
                <li><strong>Multiple Posts:</strong> flex + overflow-x-auto, variant="compact"</li>
                <li><strong>Scroll Indicators:</strong> Only show when posts.length > 2</li>
                <li><strong>Responsive:</strong> Both layouts work on all screen sizes</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Benefits Achieved</h2>
        
        <div class="success">
            <h3>✅ User Experience:</h3>
            <ul>
                <li><strong>Optimal Space Usage:</strong> Single posts get full attention</li>
                <li><strong>Better Content Visibility:</strong> Larger images and text when appropriate</li>
                <li><strong>Consistent Behavior:</strong> Layout matches content amount</li>
                <li><strong>Mobile Friendly:</strong> Better experience on small screens</li>
                <li><strong>Intuitive Design:</strong> Layout feels natural for content</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Technical Benefits:</h3>
            <ul>
                <li><strong>Smart Adaptation:</strong> Automatic layout switching</li>
                <li><strong>Reusable Components:</strong> Same PostCard with different variants</li>
                <li><strong>Clean Code:</strong> Simple conditional rendering</li>
                <li><strong>Performance:</strong> No unnecessary DOM elements</li>
                <li><strong>Maintainable:</strong> Easy to understand and modify</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Ready to Test!</h2>
        <p>Featured Posts sekarang menggunakan adaptive layout yang optimal untuk setiap scenario!</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">🏠 Test Adaptive Layout</a>
        </div>
        
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center; margin: 20px 0;">
            <strong>🎯 Goal Achieved:</strong> Smart layout adaptation based on content amount!
        </div>
    </div>
</body>
</html>
