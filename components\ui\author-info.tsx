import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { textStyles } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"

interface AuthorInfoProps {
  author: string
  avatarUrl?: string
  date?: string
  dateLabel?: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showAvatar?: boolean
}

const sizeConfig = {
  sm: {
    avatar: "w-8 h-8",
    text: "text-sm",
    name: "font-medium"
  },
  md: {
    avatar: "w-10 h-10",
    text: "text-sm",
    name: "font-medium"
  },
  lg: {
    avatar: "w-12 h-12",
    text: "text-base",
    name: "font-semibold"
  }
}

/**
 * Reusable author information component with avatar and date
 */
export function AuthorInfo({
  author,
  avatarUrl = "/placeholder.svg",
  date,
  dateLabel = "Updated",
  size = 'md',
  className,
  showAvatar = true
}: AuthorInfoProps) {
  const config = sizeConfig[size]
  const initials = author.split(' ').map(n => n[0]).join('')

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {showAvatar && (
        <Avatar className={cn(config.avatar, "border-2 border-green-500")}>
          <AvatarImage src={avatarUrl} alt={author} />
          <AvatarFallback className="bg-green-600 text-white font-bold">
            {initials}
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className="flex-1 min-w-0">
        <div className={cn(config.name, config.text, textStyles.body.primary)}>
          {author}
        </div>
        {date && (
          <div className={cn("text-sm", textStyles.body.secondary)}>
            {dateLabel} {date}
          </div>
        )}
      </div>
    </div>
  )
}

interface AuthorInfoCompactProps {
  author: string
  avatarUrl?: string
  className?: string
}

/**
 * Compact author info for cards and small spaces
 */
export function AuthorInfoCompact({
  author,
  avatarUrl = "/placeholder.svg",
  className
}: AuthorInfoCompactProps) {
  const initials = author.split(' ').map(n => n[0]).join('')

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Avatar className="w-6 h-6 border border-gray-300 dark:border-gray-600">
        <AvatarImage src={avatarUrl} alt={author} />
        <AvatarFallback className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-white text-xs font-bold">
          {initials}
        </AvatarFallback>
      </Avatar>
      <span className={cn("text-xs font-medium", textStyles.body.secondary)}>
        {author}
      </span>
    </div>
  )
}
