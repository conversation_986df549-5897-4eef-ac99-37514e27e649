# Slug Validation System

## Overview

The slug validation system provides real-time validation for post slugs using Supabase database integration. It ensures that slugs are unique, properly formatted, and SEO-friendly.

## Features

### ✅ **Real-time Validation**
- Debounced API calls (500ms delay)
- Visual feedback with icons (loading, success, error)
- Instant validation messages

### ✅ **Auto-generation from Title**
- Automatically generates slug from post title
- Converts to lowercase, removes special characters
- Replaces spaces with hyphens
- Handles multiple consecutive hyphens

### ✅ **Manual Override**
- Users can manually edit the slug
- Sync button appears when slug differs from auto-generated
- One-click sync back to title-based slug

### ✅ **Format Validation**
- Only allows lowercase letters, numbers, and hyphens
- Validates length (1-200 characters)
- Prevents invalid characters

### ✅ **Uniqueness Check**
- Checks against existing posts in database
- Supports excluding current post ID (for editing)
- Real-time availability feedback

## API Endpoints

### `GET /api/dashboard/posts/validate-slug`
**Authentication Required**

Validates slug availability and format.

**Query Parameters:**
- `slug` (required): The slug to validate
- `excludeId` (optional): Post ID to exclude from uniqueness check

**Response:**
```json
{
  "success": true,
  "data": {
    "slug": "my-post-slug",
    "available": true,
    "message": "Slug is available"
  },
  "message": "Slug is available"
}
```

### `GET /api/test/validate-slug`
**No Authentication (Testing Only)**

Same functionality as above but without authentication requirement.

## Components

### `SlugInput`
**Location:** `components/dashboard/forms/slug-input.tsx`

A comprehensive slug input component with real-time validation.

**Props:**
```typescript
interface SlugInputProps {
  value: string
  onChange: (value: string) => void
  title?: string          // For auto-generation
  excludeId?: string      // For edit mode
  className?: string
  disabled?: boolean
  required?: boolean
}
```

**Features:**
- Auto-generation from title
- Real-time validation with debounce
- Visual status indicators
- Sync button for manual changes
- URL preview
- Error messages

## Usage Examples

### Basic Usage
```tsx
import { SlugInput } from '@/components/dashboard/forms/slug-input'

function CreatePostForm() {
  const [slug, setSlug] = useState('')
  const [title, setTitle] = useState('')

  return (
    <SlugInput
      value={slug}
      onChange={setSlug}
      title={title}
      required
    />
  )
}
```

### Edit Mode (Exclude Current Post)
```tsx
<SlugInput
  value={slug}
  onChange={setSlug}
  title={title}
  excludeId={postId}
  required
/>
```

## Validation Rules

### Format Rules
1. **Characters:** Only lowercase letters (a-z), numbers (0-9), and hyphens (-)
2. **Length:** 1-200 characters
3. **Pattern:** Must match regex `/^[a-z0-9-]+$/`

### Auto-generation Rules
1. Convert to lowercase
2. Remove special characters except spaces and hyphens
3. Replace spaces with hyphens
4. Replace multiple consecutive hyphens with single hyphen
5. Remove leading and trailing hyphens

### Examples:
- `"My Awesome Post!"` → `"my-awesome-post"`
- `"CSS Grid Layout Tips"` → `"css-grid-layout-tips"`
- `"React.js & Next.js Guide"` → `"reactjs-nextjs-guide"`

## Database Integration

### Repository Method
```typescript
async isSlugAvailable(slug: string, excludeId?: string): Promise<boolean>
```

### Service Method
```typescript
async checkSlugAvailability(slug: string, excludeId?: string): Promise<boolean>
```

### SQL Query
```sql
SELECT id FROM posts 
WHERE slug = $1 
AND ($2 IS NULL OR id != $2)
LIMIT 1
```

## Error Handling

### Client-side Errors
- Invalid format (regex validation)
- Empty slug
- Network errors
- API response errors

### Server-side Errors
- Database connection issues
- Invalid parameters
- Authentication failures

### Error Messages
- `"Slug is required"`
- `"Slug can only contain lowercase letters, numbers, and hyphens"`
- `"Slug is already taken. Please choose a different slug."`
- `"Failed to validate slug"`

## Performance Considerations

### Debouncing
- 500ms delay prevents excessive API calls
- Only validates after user stops typing

### Caching
- Results could be cached for better performance
- Consider implementing client-side cache for recent validations

### Database Optimization
- Index on `slug` column for fast lookups
- Limit query results to 1 for existence check

## Testing

### Test Script
Run comprehensive tests with:
```bash
node scripts/test-slug-validation.js
```

### Test Cases
1. Valid new slugs
2. Existing slugs (should be unavailable)
3. Invalid formats (uppercase, special chars)
4. Empty slugs
5. ExcludeId functionality

### Manual Testing
1. Open `/dashboard/posts/new`
2. Type in title field
3. Observe auto-generated slug
4. Manually edit slug
5. Check validation feedback
6. Test sync button functionality

## Future Enhancements

### Potential Improvements
1. **Slug History:** Track slug changes for SEO
2. **Suggestions:** Provide alternative slugs when taken
3. **Bulk Validation:** Validate multiple slugs at once
4. **Analytics:** Track slug validation performance
5. **Caching:** Implement Redis cache for validation results

### SEO Considerations
1. **Redirects:** Handle slug changes with 301 redirects
2. **Canonical URLs:** Ensure proper canonical URL handling
3. **Sitemap:** Update sitemap when slugs change
4. **Social Media:** Update social media meta tags

## Troubleshooting

### Common Issues
1. **Validation not working:** Check API endpoint and authentication
2. **Auto-generation not working:** Verify title prop is passed
3. **Sync button not appearing:** Check if slug differs from auto-generated
4. **Database errors:** Verify Supabase connection and table structure

### Debug Steps
1. Check browser console for errors
2. Verify API responses in Network tab
3. Check server logs for database errors
4. Test API endpoints directly with curl
