const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkAdminUser() {
  console.log('🔍 Checking for admin users...\n')

  try {
    // Check auth.users table
    console.log('📋 Checking auth.users table...')
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message)
      return
    }

    if (users && users.users.length > 0) {
      console.log(`✅ Found ${users.users.length} user(s) in auth.users:`)
      users.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (ID: ${user.id})`)
        console.log(`      Created: ${new Date(user.created_at).toLocaleString()}`)
        console.log(`      Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`)
        console.log('')
      })
    } else {
      console.log('⚠️ No users found in auth.users table')
    }

    // Check user_profiles table
    console.log('📋 Checking user_profiles table...')
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
    
    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError.message)
      return
    }

    if (profiles && profiles.length > 0) {
      console.log(`✅ Found ${profiles.length} profile(s) in user_profiles:`)
      profiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. ${profile.name} (ID: ${profile.id})`)
        console.log(`      Created: ${new Date(profile.created_at).toLocaleString()}`)
        console.log('')
      })
    } else {
      console.log('⚠️ No profiles found in user_profiles table')
    }

    // Suggest creating admin user if none exists
    if (!users || users.users.length === 0) {
      console.log('💡 No admin user found. You can create one using:')
      console.log('   node scripts/create-admin-user.js')
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

checkAdminUser()
