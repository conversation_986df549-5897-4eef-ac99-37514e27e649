#!/usr/bin/env tsx

import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

async function testAPIEndpoint() {
  console.log('🧪 Testing API endpoint...')
  
  try {
    // Test the post types API endpoint
    const response = await fetch('http://localhost:3000/api/dashboard/post-types', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add a dummy auth token for testing (you'll need to replace this with a real token)
        'Cookie': 'auth-token=dummy-token-for-testing'
      }
    })
    
    console.log('Response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))
    
    const data = await response.json()
    console.log('Response data:', JSON.stringify(data, null, 2))
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

// Run the test
testAPIEndpoint()
