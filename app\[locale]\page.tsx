import { PostApiSimpleService } from "@/services/post-api-simple.service"
import { HomePageClient } from "@/components/home/<USER>"
import { DataService } from "@/lib/services/data-service"
import { getMessages, createTranslator, type Locale } from '@/lib/i18n'
import { getProfileData } from '@/lib/data/profile-bilingual'
import { Metadata } from 'next'

interface HomePageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = await params
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    generator: 'v0.dev'
  }
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = await params

  // Get bilingual profile data
  const profile = getProfileData(locale as 'en' | 'id')
  const portfolio = DataService.portfolio.getAllProjects()

  // Use new service layer for posts - only get published posts
  const postService = new PostApiSimpleService()
  const { posts: allPosts } = await postService.getPostsByStatus('PUBLISHED', { limit: 20 })
  const featuredPosts = await postService.getFeaturedPosts(6)

  // Get translations for sidebar
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)

  const sidebarTranslations = {
    profile: {
      title: t('sidebar.profile.title'),
      status: t('sidebar.profile.status'),
      contact: t('sidebar.profile.contact')
    },
    experience: {
      title: t('sidebar.experience.title'),
      current: t('sidebar.experience.current'),
      years: t('sidebar.experience.years'),
      viewAll: t('sidebar.experience.viewAll')
    },
    portfolio: {
      title: t('sidebar.portfolio.title'),
      projects: t('sidebar.portfolio.projects'),
      viewAll: t('sidebar.portfolio.viewAll')
    },
    stats: {
      title: t('sidebar.stats.title'),
      posts: t('sidebar.stats.posts'),
      views: t('sidebar.stats.views'),
      reactions: t('sidebar.stats.reactions')
    }
  }

  return (
    <HomePageClient
      profile={profile}
      allPosts={allPosts}
      featuredPosts={featuredPosts}
      portfolio={portfolio}
      locale={locale}
      sidebarTranslations={sidebarTranslations}
    />
  )
}
