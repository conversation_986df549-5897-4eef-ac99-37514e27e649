// Core data types for the application

export interface PostImage {
  id: string
  url: string
  alt?: string
  caption?: string
  width?: number
  height?: number
}

export interface PostReactions {
  heart: number
}

export interface PostType {
  id: string
  name: string
  color: string
  icon: string
  slug: string
  createdAt: string
  updatedAt: string
}

export interface Post {
  id: string
  title: string
  slug: string
  content: string
  version: string
  type: PostType
  authorId: string
  createdAt: string
  updatedAt: string
  reactions: PostReactions
  comments: number
  tags: string[]
  featured: boolean
  readTime: string
  excerpt: boolean
  images?: PostImage[]
}

export interface Experience {
  id: string
  company: string
  position: string
  duration: string
  location: string
  type: string
  logo: string
  description: string
  achievements: string[]
  technologies: string[]
  projects: ExperienceProject[]
}

export interface ExperienceProject {
  name: string
  description: string
  impact: string
}

export interface PortfolioProject {
  id: string
  title: string
  description: string
  longDescription: string
  image: string
  technologies: string[]
  category: string
  status: 'Live' | 'In Development' | 'Completed'
  year: string
  duration: string
  team: string
  role: string
  links: {
    live?: string
    github?: string
    case_study?: string
  }
  metrics: {
    users?: string
    transactions?: string
    uptime?: string
    performance?: string
    api_calls?: string
    accuracy?: string
    accounts?: string
    data_points?: string
    reports?: string
    projects?: string
  }
  features: string[]
  challenges: string[]
  solutions: string[]
}

export interface Profile {
  name: string
  title: string
  bio: string
  location: string
  joinDate: string
  avatar: string
  social: {
    github: string
    instagram: string
    facebook: string
    linkedin: string
    email: string
  }
  experience: Experience[]
  portfolio: PortfolioProject[]
}

export interface SkillGroup {
  category: string
  technologies: string[]
}

export interface SocialLink {
  icon: any // Lucide icon component
  href: string
  label: string
}

export interface NavigationItem {
  label: string
  href: string
  icon?: any // Lucide icon component
}
