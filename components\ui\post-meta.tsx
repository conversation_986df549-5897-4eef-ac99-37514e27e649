import { Badge } from "@/components/ui/badge"
import { Clock, GitBranch, Eye, Calendar } from "lucide-react"
import { getIconByName, getPostTypeClasses } from "@/lib/constants"
import { textStyles } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"

interface PostMetaProps {
  type: {
    name: string
    icon: string
    color: string
  }
  version?: string
  readTime?: string
  viewCount?: number
  createdAt?: string
  updatedAt?: string
  className?: string
  showAll?: boolean
}

/**
 * Reusable post metadata component with consistent styling
 */
export function PostMeta({
  type,
  version,
  readTime,
  viewCount,
  createdAt,
  updatedAt,
  className,
  showAll = true
}: PostMetaProps) {
  const IconComponent = getIconByName(type.icon)
  const typeStyles = getPostTypeClasses(type.color)

  return (
    <div className={cn("flex items-center gap-4 flex-wrap", className)}>
      <Badge
        className="border"
        style={{
          background: typeStyles.background,
          color: typeStyles.color,
          borderColor: typeStyles.borderColor
        }}
      >
        <IconComponent className="w-3 h-3 mr-1" />
        {type.name}
      </Badge>

      {version && showAll && (
        <div className={cn("flex items-center gap-2 text-sm", textStyles.body.secondary)}>
          <GitBranch className="w-3 h-3" />
          <span>{version}</span>
        </div>
      )}

      {readTime && (
        <div className={cn("flex items-center gap-2 text-sm", textStyles.body.secondary)}>
          <Clock className="w-3 h-3" />
          <span>{readTime}</span>
        </div>
      )}

      {viewCount !== undefined && showAll && (
        <div className={cn("flex items-center gap-2 text-sm", textStyles.body.secondary)}>
          <Eye className="w-3 h-3" />
          <span>{viewCount} views</span>
        </div>
      )}

      {(createdAt || updatedAt) && showAll && (
        <div className={cn("flex items-center gap-2 text-sm", textStyles.body.secondary)}>
          <Calendar className="w-3 h-3" />
          <span>{updatedAt ? `Updated ${updatedAt}` : `Created ${createdAt}`}</span>
        </div>
      )}
    </div>
  )
}

interface PostMetaCompactProps {
  type: {
    name: string
    icon: string
    color: string
  }
  readTime?: string
  className?: string
}

/**
 * Compact version of post metadata for cards
 */
export function PostMetaCompact({
  type,
  readTime,
  className
}: PostMetaCompactProps) {
  const IconComponent = getIconByName(type.icon)
  const typeStyles = getPostTypeClasses(type.color)

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge
        className="border text-xs"
        style={{
          background: typeStyles.background,
          color: typeStyles.color,
          borderColor: typeStyles.borderColor
        }}
      >
        <IconComponent className="w-2.5 h-2.5 mr-1" />
        {type.name}
      </Badge>

      {readTime && (
        <div className={cn("flex items-center gap-1 text-xs", textStyles.body.secondary)}>
          <Clock className="w-2.5 h-2.5" />
          <span>{readTime}</span>
        </div>
      )}
    </div>
  )
}
