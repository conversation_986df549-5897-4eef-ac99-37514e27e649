import { NextResponse } from 'next/server'
import { ApiResponse, ApiError, ApiMeta, HttpStatus, ErrorCode, PaginationMeta } from '@/types/api'
import { v4 as uuidv4 } from 'uuid'

export class ApiResponseBuilder {
  private static generateRequestId(): string {
    return uuidv4()
  }

  private static generateMeta(pagination?: PaginationMeta): ApiMeta {
    return {
      pagination,
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
      version: '1.0.0',
    }
  }

  /**
   * Success Response
   */
  static success<T>(
    data?: T,
    message?: string,
    status: HttpStatus = HttpStatus.OK,
    pagination?: PaginationMeta
  ): NextResponse<ApiResponse<T>> {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message: message || 'Request successful',
      meta: this.generateMeta(pagination),
    }

    return NextResponse.json(response, { status })
  }

  /**
   * Created Response
   */
  static created<T>(
    data?: T,
    message?: string
  ): NextResponse<ApiResponse<T>> {
    return this.success(data, message || 'Resource created successfully', HttpStatus.CREATED)
  }

  /**
   * No Content Response
   */
  static noContent(): NextResponse {
    return new NextResponse(null, { status: HttpStatus.NO_CONTENT })
  }

  /**
   * Error Response
   */
  static error(
    code: ErrorCode,
    message: string,
    status: HttpStatus = HttpStatus.BAD_REQUEST,
    details?: Record<string, any>,
    field?: string
  ): NextResponse<ApiResponse> {
    const error: ApiError = {
      code,
      message,
      details,
      field,
      timestamp: new Date().toISOString(),
    }

    const response: ApiResponse = {
      success: false,
      error,
      meta: this.generateMeta(),
    }

    return NextResponse.json(response, { status })
  }

  /**
   * Bad Request Response
   */
  static badRequest(
    message: string = 'Bad request',
    details?: Record<string, any>
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.VALIDATION_ERROR,
      message,
      HttpStatus.BAD_REQUEST,
      details
    )
  }

  /**
   * Validation Error Response
   */
  static validationError(
    message: string,
    field?: string,
    details?: Record<string, any>
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.VALIDATION_ERROR,
      message,
      HttpStatus.UNPROCESSABLE_ENTITY,
      details,
      field
    )
  }

  /**
   * Unauthorized Response
   */
  static unauthorized(
    message: string = 'Authentication required'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.UNAUTHORIZED_ACCESS,
      message,
      HttpStatus.UNAUTHORIZED
    )
  }

  /**
   * Forbidden Response
   */
  static forbidden(
    message: string = 'Insufficient permissions'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.INSUFFICIENT_PERMISSIONS,
      message,
      HttpStatus.FORBIDDEN
    )
  }

  /**
   * Not Found Response
   */
  static notFound(
    message: string = 'Resource not found'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.RESOURCE_NOT_FOUND,
      message,
      HttpStatus.NOT_FOUND
    )
  }

  /**
   * Conflict Response
   */
  static conflict(
    message: string = 'Resource already exists'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.RESOURCE_CONFLICT,
      message,
      HttpStatus.CONFLICT
    )
  }

  /**
   * Rate Limit Response
   */
  static rateLimitExceeded(
    message: string = 'Rate limit exceeded'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      message,
      HttpStatus.TOO_MANY_REQUESTS
    )
  }

  /**
   * Internal Server Error Response
   */
  static internalError(
    message: string = 'Internal server error',
    details?: Record<string, any>
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.INTERNAL_ERROR,
      message,
      HttpStatus.INTERNAL_SERVER_ERROR,
      details
    )
  }

  /**
   * Database Error Response
   */
  static databaseError(
    message: string = 'Database operation failed',
    details?: Record<string, any>
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.DATABASE_ERROR,
      message,
      HttpStatus.INTERNAL_SERVER_ERROR,
      details
    )
  }

  /**
   * Service Unavailable Response
   */
  static serviceUnavailable(
    message: string = 'Service temporarily unavailable'
  ): NextResponse<ApiResponse> {
    return this.error(
      ErrorCode.SERVICE_UNAVAILABLE,
      message,
      HttpStatus.SERVICE_UNAVAILABLE
    )
  }
}
