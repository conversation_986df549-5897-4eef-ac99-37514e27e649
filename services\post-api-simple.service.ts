// Simple Post API Service for testing Supabase
import { PostSupabaseSimpleRepository } from '@/repositories/post-supabase-simple.repository'
import { PostFilters } from '@/types'
import { PaginationOptions } from '@/types/api'

export interface PostStatsApiResponse {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  archivedPosts: number
  totalViews: number
  totalReactions: number
  totalComments: number
  postsByType: { type: string; count: number }[]
  mostViewedPosts: any[]
  recentPosts: any[]
  growthMetrics: {
    thisMonth: number
    lastMonth: number
    growth: number
  }
}

export class PostApiSimpleService {
  private postRepository: PostSupabaseSimpleRepository

  constructor() {
    this.postRepository = new PostSupabaseSimpleRepository()
  }

  /**
   * Get all posts with pagination and filtering
   */
  async getAllPosts(
    pagination: PaginationOptions = {},
    filters: PostFilters = {}
  ): Promise<{ posts: any[]; total: number }> {
    try {
      return await this.postRepository.findAll(pagination, filters)
    } catch (error) {
      console.error('Error getting posts:', error)
      throw error
    }
  }

  /**
   * Get post by ID
   */
  async getPostById(id: string): Promise<any | null> {
    try {
      return await this.postRepository.findById(id)
    } catch (error) {
      console.error('Error getting post by id:', error)
      throw error
    }
  }

  /**
   * Get post by slug
   */
  async getPostBySlug(slug: string): Promise<any | null> {
    try {
      return await this.postRepository.findBySlug(slug)
    } catch (error) {
      console.error('Error getting post by slug:', error)
      throw error
    }
  }

  /**
   * Get posts statistics
   */
  async getPostStats(): Promise<PostStatsApiResponse> {
    try {
      const stats = await this.postRepository.getStats()
      const recentPosts = await this.getRecentPosts(5)
      const mostViewedPosts = await this.getMostViewedPosts(5)

      return {
        totalPosts: stats.total,
        publishedPosts: stats.published,
        draftPosts: stats.draft,
        archivedPosts: 0, // Not implemented in database yet
        totalViews: stats.totalViews,
        totalReactions: stats.totalReactions,
        totalComments: 0, // Not implemented in database yet
        postsByType: [], // Will be implemented later
        mostViewedPosts,
        recentPosts,
        growthMetrics: {
          thisMonth: 0, // Will be implemented later
          lastMonth: 0, // Will be implemented later
          growth: 0 // Will be implemented later
        }
      }
    } catch (error) {
      console.error('Error getting post stats:', error)
      throw error
    }
  }

  /**
   * Get featured posts
   */
  async getFeaturedPosts(limit: number = 6): Promise<any[]> {
    try {
      return await this.postRepository.getFeaturedPosts(limit)
    } catch (error) {
      console.error('Error getting featured posts:', error)
      throw error
    }
  }

  /**
   * Search posts
   */
  async searchPosts(
    query: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: any[]; total: number }> {
    try {
      return await this.postRepository.searchPosts(query, pagination)
    } catch (error) {
      console.error('Error searching posts:', error)
      throw error
    }
  }

  /**
   * Get posts by status
   */
  async getPostsByStatus(
    status: string,
    pagination: PaginationOptions = {}
  ): Promise<{ posts: any[]; total: number }> {
    try {
      return await this.postRepository.getPostsByStatus(status, pagination)
    } catch (error) {
      console.error('Error getting posts by status:', error)
      throw error
    }
  }

  /**
   * Get recent posts
   */
  async getRecentPosts(limit: number = 10): Promise<any[]> {
    try {
      return await this.postRepository.getRecentPosts(limit)
    } catch (error) {
      console.error('Error getting recent posts:', error)
      throw error
    }
  }

  /**
   * Get most viewed posts
   */
  async getMostViewedPosts(limit: number = 5): Promise<any[]> {
    try {
      return await this.postRepository.getMostViewedPosts(limit)
    } catch (error) {
      console.error('Error getting most viewed posts:', error)
      throw error
    }
  }

  /**
   * Create a new post
   */
  async createPost(postData: {
    title: string
    slug: string
    content: string
    authorId: string
    typeId: string
    status: string
    featured?: boolean
    excerpt?: boolean
    version?: string
    tags?: string[]
    images?: Array<{
      url: string
      altText?: string
      caption?: string
      name: string
      size: number
    }>
  }): Promise<any> {
    try {
      // Check if slug is available
      const isSlugAvailable = await this.postRepository.isSlugAvailable(postData.slug)
      if (!isSlugAvailable) {
        throw new Error('Slug is already taken. Please choose a different slug.')
      }

      console.log('📸 Creating post with images:', postData.images?.length || 0)
      return await this.postRepository.create(postData)
    } catch (error) {
      console.error('Error creating post:', error)
      throw error
    }
  }

  /**
   * Check if slug is available
   */
  async checkSlugAvailability(slug: string, excludeId?: string): Promise<boolean> {
    try {
      return await this.postRepository.isSlugAvailable(slug, excludeId)
    } catch (error) {
      console.error('Error checking slug availability:', error)
      throw error
    }
  }

  /**
   * Update an existing post
   */
  async updatePost(postId: string, postData: {
    title: string
    slug: string
    content: string
    typeId: string
    status: string
    featured?: boolean
    excerpt?: boolean
    version?: string
    tags?: string[]
    images?: Array<{
      url: string
      altText?: string
      caption?: string
      name: string
      size: number
    }>
  }): Promise<any> {
    try {
      // Check if slug is available (excluding current post)
      const isSlugAvailable = await this.postRepository.isSlugAvailable(postData.slug, postId)
      if (!isSlugAvailable) {
        throw new Error('Slug is already taken. Please choose a different slug.')
      }

      console.log('📸 Updating post with images:', postData.images?.length || 0)
      return await this.postRepository.update(postId, postData)
    } catch (error) {
      console.error('Error updating post:', error)
      throw error
    }
  }
}
