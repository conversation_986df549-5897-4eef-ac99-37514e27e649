-- ============================================
-- SUPABASE QUERIES: Add file_name and file_size to post_images
-- ============================================

-- 1. CHECK CURRENT SCHEMA
-- Run this first to see current structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'post_images' 
ORDER BY ordinal_position;

-- 2. CHECK EXISTING DATA
-- See what data we currently have
SELECT 
    id,
    post_id,
    url,
    alt_text,
    caption,
    width,
    height,
    display_order,
    created_at
FROM post_images 
ORDER BY created_at DESC 
LIMIT 10;

-- ============================================
-- ADD MISSING FIELDS TO post_images TABLE
-- ============================================

-- 3. ADD file_name COLUMN
ALTER TABLE post_images 
ADD COLUMN file_name TEXT;

-- 4. ADD file_size COLUMN  
ALTER TABLE post_images 
ADD COLUMN file_size BIGINT;

-- 5. VERIFY NEW SCHEMA
-- Check that columns were added successfully
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'post_images' 
ORDER BY ordinal_position;

-- ============================================
-- UPDATE EXISTING DATA (OPTIONAL)
-- ============================================

-- 6. UPDATE EXISTING RECORDS WITH DEFAULT VALUES
-- For existing images, set reasonable defaults

-- Update file_name based on display_order
UPDATE post_images 
SET file_name = CONCAT('image-', display_order, '.jpg')
WHERE file_name IS NULL;

-- Update file_size with default value (1KB = 1024 bytes)
UPDATE post_images 
SET file_size = 1024
WHERE file_size IS NULL;

-- ============================================
-- VERIFY UPDATES
-- ============================================

-- 7. CHECK UPDATED DATA
SELECT 
    id,
    post_id,
    url,
    alt_text,
    caption,
    file_name,    -- NEW FIELD
    file_size,    -- NEW FIELD
    width,
    height,
    display_order,
    created_at
FROM post_images 
ORDER BY created_at DESC 
LIMIT 10;

-- ============================================
-- OPTIONAL: ADD CONSTRAINTS
-- ============================================

-- 8. ADD NOT NULL CONSTRAINT (if desired)
-- Only run this after updating existing data
-- ALTER TABLE post_images 
-- ALTER COLUMN file_name SET NOT NULL;

-- 9. ADD CHECK CONSTRAINT for file_size
-- Ensure file_size is positive
ALTER TABLE post_images 
ADD CONSTRAINT check_file_size_positive 
CHECK (file_size > 0);

-- ============================================
-- TEST QUERIES
-- ============================================

-- 10. TEST INSERT with new fields
INSERT INTO post_images (
    id,
    post_id,
    url,
    alt_text,
    caption,
    file_name,
    file_size,
    width,
    height,
    display_order
) VALUES (
    gen_random_uuid(),
    'your-post-id-here',
    'https://example.com/test-image.jpg',
    'Test image alt text',
    'Test image caption',
    'test-image.jpg',
    2048,
    800,
    600,
    1
);

-- 11. TEST SELECT with new fields
SELECT 
    p.title as post_title,
    pi.file_name,
    pi.file_size,
    pi.url,
    pi.alt_text,
    pi.caption
FROM post_images pi
JOIN posts p ON pi.post_id = p.id
WHERE p.id = 'your-post-id-here'
ORDER BY pi.display_order;

-- ============================================
-- CLEANUP (if needed)
-- ============================================

-- 12. REMOVE TEST DATA (if you inserted test data)
-- DELETE FROM post_images 
-- WHERE url = 'https://example.com/test-image.jpg';

-- ============================================
-- ROLLBACK (if something goes wrong)
-- ============================================

-- 13. REMOVE ADDED COLUMNS (DANGER: This will delete data!)
-- Only use if you need to rollback the changes
-- ALTER TABLE post_images DROP COLUMN file_name;
-- ALTER TABLE post_images DROP COLUMN file_size;
-- ALTER TABLE post_images DROP CONSTRAINT check_file_size_positive;
