"use client"

import { useState, useEffect, useCallback } from 'react'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { Post, PostType } from '@/types'
import { PostFilters } from '@/repositories'

// Type definitions for compatibility
export interface PostListResult {
  posts: Post[]
  total: number
}

export interface PostReactionUpdate {
  postId: string
  reactionType: 'thumbsUp' | 'heart' | 'brain'
}

export interface UsePostsOptions {
  filters?: PostFilters
  page?: number
  limit?: number
  autoFetch?: boolean
}

export interface UsePostsReturn {
  posts: Post[]
  total: number
  page: number
  limit: number
  totalPages: number
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  fetchMore: () => Promise<void>
  hasMore: boolean
}

export function usePosts(options: UsePostsOptions = {}): UsePostsReturn {
  const {
    filters = {},
    page = 1,
    limit = 10,
    autoFetch = true
  } = options

  const [data, setData] = useState<PostListResult>({
    posts: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postService = new PostApiSimpleService()

  const fetchPosts = useCallback(async (pageNum: number = page, reset: boolean = true) => {
    try {
      setLoading(true)
      setError(null)

      const result = await postService.getAllPosts(filters, pageNum, limit)

      setData(prevData => ({
        ...result,
        posts: reset ? result.posts : [...prevData.posts, ...result.posts]
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch posts')
    } finally {
      setLoading(false)
    }
  }, [filters, page, limit])

  const refetch = useCallback(() => fetchPosts(1, true), [fetchPosts])

  const fetchMore = useCallback(() => {
    if (data.page < data.totalPages && !loading) {
      fetchPosts(data.page + 1, false)
    }
  }, [data.page, data.totalPages, loading, fetchPosts])

  useEffect(() => {
    if (autoFetch) {
      fetchPosts()
    }
  }, [autoFetch, fetchPosts])

  return {
    posts: data.posts,
    total: data.total,
    page: data.page,
    limit: data.limit,
    totalPages: data.totalPages,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore: data.page < data.totalPages
  }
}

export interface UsePostOptions {
  slug?: string
  id?: string
  autoFetch?: boolean
}

export interface UsePostReturn {
  post: Post | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  addReaction: (reactionType: 'thumbsUp' | 'heart' | 'brain') => Promise<void>
}

export function usePost(options: UsePostOptions): UsePostReturn {
  const { slug, id, autoFetch = true } = options
  const [post, setPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postService = new PostApiSimpleService()

  const fetchPost = useCallback(async () => {
    if (!slug && !id) return

    try {
      setLoading(true)
      setError(null)

      const result = slug
        ? await postService.getPostBySlug(slug)
        : await postService.getPostById(id!)

      setPost(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post')
    } finally {
      setLoading(false)
    }
  }, [slug, id])

  const addReaction = useCallback(async (reactionType: 'thumbsUp' | 'heart' | 'brain') => {
    if (!post) return

    try {
      // Note: addReaction method not implemented in PostApiSimpleService yet
      // This is a placeholder for future implementation
      console.log('Adding reaction:', reactionType, 'to post:', post.id)
      // const updatedPost = await postService.addReaction(reactionData)
      // setPost(updatedPost)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add reaction')
    }
  }, [post])

  const refetch = useCallback(() => fetchPost(), [fetchPost])

  useEffect(() => {
    if (autoFetch && (slug || id)) {
      fetchPost()
    }
  }, [autoFetch, slug, id, fetchPost])

  return {
    post,
    loading,
    error,
    refetch,
    addReaction
  }
}

export interface UseFeaturedPostsReturn {
  posts: Post[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useFeaturedPosts(limit: number = 6): UseFeaturedPostsReturn {
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postService = new PostApiSimpleService()

  const fetchFeaturedPosts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await postService.getFeaturedPosts(limit)
      setPosts(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch featured posts')
    } finally {
      setLoading(false)
    }
  }, [limit])

  const refetch = useCallback(() => fetchFeaturedPosts(), [fetchFeaturedPosts])

  useEffect(() => {
    fetchFeaturedPosts()
  }, [fetchFeaturedPosts])

  return {
    posts,
    loading,
    error,
    refetch
  }
}

export interface UsePostSearchOptions {
  searchTerm: string
  filters?: Omit<PostFilters, 'search'>
  page?: number
  limit?: number
  debounceMs?: number
}

export interface UsePostSearchReturn extends UsePostsReturn {
  searchTerm: string
  setSearchTerm: (term: string) => void
}

export function usePostSearch(options: UsePostSearchOptions): UsePostSearchReturn {
  const {
    searchTerm: initialSearchTerm,
    filters = {},
    page = 1,
    limit = 10,
    debounceMs = 300
  } = options

  const [searchTerm, setSearchTerm] = useState(initialSearchTerm)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(initialSearchTerm)
  const [data, setData] = useState<PostListResult>({
    posts: [],
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const postService = new PostApiSimpleService()

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchTerm, debounceMs])

  const searchPosts = useCallback(async (pageNum: number = page, reset: boolean = true) => {
    if (!debouncedSearchTerm.trim()) {
      setData({
        posts: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      })
      return
    }

    try {
      setLoading(true)
      setError(null)

      const result = await postService.searchPosts(debouncedSearchTerm, { page: pageNum, limit })

      setData(prevData => ({
        ...result,
        page: pageNum,
        limit,
        totalPages: Math.ceil(result.total / limit),
        posts: reset ? result.posts : [...prevData.posts, ...result.posts]
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search posts')
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchTerm, filters, page, limit])

  const refetch = useCallback(() => searchPosts(1, true), [searchPosts])

  const fetchMore = useCallback(() => {
    if (data.page < data.totalPages && !loading) {
      searchPosts(data.page + 1, false)
    }
  }, [data.page, data.totalPages, loading, searchPosts])

  useEffect(() => {
    searchPosts()
  }, [searchPosts])

  return {
    posts: data.posts,
    total: data.total,
    page: data.page,
    limit: data.limit,
    totalPages: data.totalPages,
    loading,
    error,
    refetch,
    fetchMore,
    hasMore: data.page < data.totalPages,
    searchTerm,
    setSearchTerm
  }
}
