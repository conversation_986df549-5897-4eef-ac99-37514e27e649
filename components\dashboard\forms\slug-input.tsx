"use client"

import { useState, useEffect, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, Loader2, RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useDebounce } from '@/hooks/use-debounce'
import { useAuth } from '@/contexts/auth-context'

interface SlugInputProps {
  value: string
  onChange: (value: string) => void
  title?: string
  excludeId?: string
  className?: string
  disabled?: boolean
  required?: boolean
}

interface SlugValidationResult {
  available: boolean
  message: string
  loading: boolean
  error?: string
}

export function SlugInput({
  value,
  onChange,
  title = '',
  excludeId,
  className,
  disabled = false,
  required = false
}: SlugInputProps) {
  const [validation, setValidation] = useState<SlugValidationResult>({
    available: true,
    message: '',
    loading: false
  })
  const [hasManual<PERSON>hange, setHasManualChange] = useState(false)

  // Get auth token from context
  const { token, isAuthenticated } = useAuth()

  // Debounce slug value for API calls
  const debouncedSlug = useDebounce(value, 500)

  // Generate slug from title
  const generateSlugFromTitle = useCallback((title: string): string => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  }, [])

  // Auto-generate slug from title if not manually changed
  useEffect(() => {
    if (!hasManualChange) {
      if (title) {
        // Generate slug from title if title exists
        const autoSlug = generateSlugFromTitle(title)
        if (autoSlug !== value) {
          onChange(autoSlug)
        }
      } else {
        // Clear slug if title is empty
        if (value) {
          onChange('')
        }
      }
    }
  }, [title, hasManualChange, value, onChange, generateSlugFromTitle])

  // Validate slug with API
  const validateSlug = useCallback(async (slug: string) => {
    if (!slug || slug.length < 1) {
      setValidation({
        available: false,
        message: 'Slug is required',
        loading: false,
        error: 'Slug is required'
      })
      return
    }

    // Check if user is authenticated
    if (!isAuthenticated || !token) {
      setValidation({
        available: false,
        message: 'Authentication required',
        loading: false,
        error: 'Not authenticated'
      })
      return
    }

    // Check slug format
    const slugRegex = /^[a-z0-9-]+$/
    if (!slugRegex.test(slug)) {
      setValidation({
        available: false,
        message: 'Slug can only contain lowercase letters, numbers, and hyphens',
        loading: false,
        error: 'Invalid format'
      })
      return
    }

    setValidation(prev => ({ ...prev, loading: true, error: undefined }))

    try {
      const params = new URLSearchParams({ slug })
      if (excludeId) {
        params.append('excludeId', excludeId)
      }

      const response = await fetch(`/api/dashboard/posts/check-slug?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      const result = await response.json()

      if (response.ok) {
        setValidation({
          available: result.data.available,
          message: result.data.message,
          loading: false
        })
      } else {
        // Handle authentication errors
        if (response.status === 401) {
          setValidation({
            available: false,
            message: 'Authentication required. Please login.',
            loading: false,
            error: 'Authentication failed'
          })
        } else {
          setValidation({
            available: false,
            message: result.message || 'Validation failed',
            loading: false,
            error: result.message
          })
        }
      }
    } catch (error) {
      console.error('Slug validation error:', error)
      setValidation({
        available: false,
        message: 'Failed to validate slug',
        loading: false,
        error: 'Network error'
      })
    }
  }, [excludeId, isAuthenticated, token])

  // Validate when debounced slug changes
  useEffect(() => {
    if (debouncedSlug) {
      validateSlug(debouncedSlug)
    } else {
      // Clear validation when slug is empty
      setValidation({
        available: true,
        message: '',
        loading: false
      })
    }
  }, [debouncedSlug, validateSlug])

  // Handle manual input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setHasManualChange(true)
    onChange(newValue)
  }

  // Sync slug with title
  const handleSyncWithTitle = () => {
    if (title) {
      const autoSlug = generateSlugFromTitle(title)
      onChange(autoSlug)
      setHasManualChange(false)
    } else {
      // If title is empty, clear the slug
      onChange('')
      setHasManualChange(false)
    }
  }

  // Show sync button when slug differs from auto-generated
  const autoGeneratedSlug = title ? generateSlugFromTitle(title) : ''
  const showSyncButton = hasManualChange && value !== autoGeneratedSlug

  // Get status icon
  const getStatusIcon = () => {
    if (validation.loading) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
    }
    if (validation.error) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    if (value && validation.available) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    }
    if (value && !validation.available) {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
    return null
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor="slug" className="text-sm font-medium">
        Slug {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <Input
          id="slug"
          type="text"
          value={value}
          onChange={handleInputChange}
          disabled={disabled}
          placeholder="post-slug-example"
          className={cn(
            "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
            "focus:outline-none focus:border-green-500 dark:focus:border-green-400 focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
            validation.error && "border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500 dark:focus:ring-red-400 focus-visible:ring-red-500 dark:focus-visible:ring-red-400",
            disabled && "opacity-50 cursor-not-allowed",
            "pr-20",
            validation.available && value && !validation.error && "border-green-500 focus:border-green-500 dark:border-green-400 dark:focus:border-green-400",
            className
          )}
        />

        {/* Status Icon */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
          {showSyncButton && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleSyncWithTitle}
              className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
              title="Sync with title"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          )}
          {getStatusIcon()}
        </div>
      </div>

      {/* Validation Message */}
      {validation.message && (
        <p className={cn(
          "text-xs",
          validation.available ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
        )}>
          {validation.message}
        </p>
      )}

      {/* URL Preview */}
      {value && (
        <p className="text-xs text-gray-500 dark:text-gray-400">
          URL: <span className="font-mono">/posts/{value}</span>
        </p>
      )}
    </div>
  )
}
