{"common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "backToHome": "Back to Home", "readMore": "Read More", "viewAll": "View All", "search": "Search", "filter": "Filter", "all": "All"}, "navigation": {"home": "Home", "posts": "Posts", "portfolio": "Portfolio", "experience": "Experience", "about": "About", "contact": "Contact"}, "header": {"tagline": "Personal Portfolio", "version": "v1.0.0", "searchPlaceholder": "Search posts..."}, "hero": {"title": "Sharing Ideas &", "subtitle": "Building Knowledge", "description": "Software Engineer & Full-<PERSON><PERSON> Developer passionate about creating innovative solutions and sharing knowledge through code. Besides programming, I also enjoy writing about technology insights, life experiences, and software engineering practices."}, "featuredPosts": {"title": "Featured Posts", "description": "Handpicked content worth reading"}, "postsSection": {"title": "All Posts", "description": "Browse all my content", "filters": {"all": "All", "learning": "Learning", "opinion": "Opinion", "tip": "Tips", "photo": "Photos", "showcase": "Showcase", "error-log": "<PERSON><PERSON><PERSON>"}, "more": "More", "noPostsFound": "No posts found", "tryDifferentFilter": "Try a different filter or search term"}, "sidebar": {"profile": {"title": "Profile", "location": "Jakarta, Indonesia", "status": "Available for work", "contact": "Contact Me"}, "experience": {"title": "Experience", "current": "Current", "years": "years", "viewAll": "View All Experience"}, "portfolio": {"title": "Portfolio", "projects": "Projects", "viewAll": "View All Projects"}, "stats": {"title": "Statistics", "posts": "Posts", "views": "Views", "reactions": "Reactions"}}, "postCard": {"readTime": "min read", "reactions": "reactions", "comments": "comments", "views": "views", "featured": "Featured", "new": "New"}, "postDetail": {"publishedOn": "Published on", "lastUpdated": "Last updated", "readTime": "min read", "sharePost": "Share Post", "relatedPosts": "Related Posts", "backToPosts": "Back to Posts", "author": "Author", "tags": "Tags", "share": "Share", "edit": "Edit", "copyLink": "Copy Link", "report": "Report", "like": "Like", "unlike": "Unlike", "comments": "Comments", "noComments": "No comments yet", "addComment": "Add a comment", "writeComment": "Write your comment...", "postComment": "Post Comment", "reply": "Reply"}, "languageSwitcher": {"switchLanguage": "Switch Language", "currentLanguage": "Current Language"}, "portfolio": {"title": "My Portfolio", "subtitle": "Crafting Digital Solutions", "description": "A showcase of my best work in web development, featuring full-stack applications, data visualizations, and innovative solutions that solve real-world problems.", "categories": "Categories", "searchPlaceholder": "Search projects...", "viewProject": "View Project", "viewCode": "View Code", "caseStudy": "Case Study", "technologies": "Technologies", "features": "Features", "challenges": "Challenges", "solutions": "Solutions", "metrics": "Metrics", "status": "Status", "duration": "Duration", "team": "Team", "role": "Role", "stats": {"projects": "Projects", "users": "Users", "rating": "Rating"}, "cta": {"startProject": "Start a Project", "viewExperience": "View Experience"}}, "experience": {"title": "Professional Experience", "subtitle": "Career journey and achievements", "description": "A comprehensive look at my professional journey, technical expertise, and career achievements in software development.", "overview": {"professionalJourney": "Professional Journey", "title": "Experience Overview", "description": "A comprehensive look at my professional journey, technical expertise, and career achievements in software development."}, "stats": {"yearsExperience": "Years Experience", "projectsDelivered": "Projects Delivered", "companies": "Companies", "roles": "Roles"}, "section": {"title": "Professional Experience", "description": "Detailed career journey and achievements"}, "details": {"duration": "Duration", "teamSize": "Team Size", "projects": "Projects", "delivered": "Delivered", "keyHighlights": "Key Highlights", "coreTechnologies": "Core Technologies", "showDetails": "Show Details", "hideDetails": "Hide Details", "keyAchievements": "Key Achievements", "notableProjects": "Notable Projects", "technologiesUsed": "Technologies Used", "members": "Members", "now": "Now"}, "skills": {"title": "Technical Skills", "description": "Technologies and tools I work with", "frontend": "Frontend", "backend": "Backend", "devops": "DevOps", "tools": "Tools"}}, "meta": {"title": "Threadbook - Personal Knowledge Management", "description": "A hybrid platform combining Twitter threads, GitHub repositories, and blogging functionality for organizing and sharing thoughts.", "keywords": "blog, portfolio, software engineer, full-stack developer, technology, programming"}}