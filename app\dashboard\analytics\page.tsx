"use client"

import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  Clock,
  Globe,
  Smartphone,
  Monitor,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

const analyticsData = {
  overview: {
    totalViews: 45230,
    uniqueVisitors: 12450,
    avgSessionDuration: '3m 24s',
    bounceRate: 34.2,
    changes: {
      views: { value: 12.5, type: 'increase' },
      visitors: { value: 8.3, type: 'increase' },
      duration: { value: 5.2, type: 'decrease' },
      bounce: { value: 2.1, type: 'decrease' },
    }
  },
  topPages: [
    { path: '/posts/nextjs-chat-app', views: 2340, percentage: 15.2 },
    { path: '/posts/typescript-errors', views: 1890, percentage: 12.3 },
    { path: '/experience', views: 1560, percentage: 10.1 },
    { path: '/portfolio', views: 1340, percentage: 8.7 },
    { path: '/', views: 1120, percentage: 7.3 },
  ],
  devices: [
    { type: 'Desktop', percentage: 65.4, count: 8140 },
    { type: 'Mobile', percentage: 28.7, count: 3570 },
    { type: 'Tablet', percentage: 5.9, count: 740 },
  ],
  countries: [
    { name: 'Indonesia', percentage: 45.2, count: 5630 },
    { name: 'United States', percentage: 18.7, count: 2330 },
    { name: 'Singapore', percentage: 12.1, count: 1510 },
    { name: 'Malaysia', percentage: 8.9, count: 1110 },
    { name: 'Others', percentage: 15.1, count: 1870 },
  ]
}

function StatCard({ title, value, change, icon: Icon, suffix = '' }: any) {
  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-400 mb-1">{title}</p>
            <p className="text-2xl font-bold text-white">{value}{suffix}</p>
            {change && (
              <div className={cn(
                "flex items-center text-xs mt-2",
                change.type === 'increase' ? "text-green-400" : "text-red-400"
              )}>
                {change.type === 'increase' ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {Math.abs(change.value)}% from last month
              </div>
            )}
          </div>
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function AnalyticsPage() {
  return (
    <DashboardLayout
      title="Analytics Dashboard"
      description="Monitor your website traffic and user engagement"
    >
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard
          title="Total Views"
          value={analyticsData.overview.totalViews.toLocaleString()}
          change={analyticsData.overview.changes.views}
          icon={Eye}
        />
        <StatCard
          title="Unique Visitors"
          value={analyticsData.overview.uniqueVisitors.toLocaleString()}
          change={analyticsData.overview.changes.visitors}
          icon={Users}
        />
        <StatCard
          title="Avg. Session"
          value={analyticsData.overview.avgSessionDuration}
          change={analyticsData.overview.changes.duration}
          icon={Clock}
        />
        <StatCard
          title="Bounce Rate"
          value={analyticsData.overview.bounceRate}
          change={analyticsData.overview.changes.bounce}
          icon={BarChart3}
          suffix="%"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
              Top Pages
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analyticsData.topPages.map((page, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300 font-mono">{page.path}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-900 dark:text-white font-medium">{page.views.toLocaleString()}</span>
                    <Badge variant="outline" className="text-xs text-gray-600 dark:text-gray-400 border-gray-300 dark:border-gray-600">
                      {page.percentage}%
                    </Badge>
                  </div>
                </div>
                <Progress value={page.percentage} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Device Types */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center">
              <Monitor className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
              Device Types
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analyticsData.devices.map((device, index) => {
              const Icon = device.type === 'Desktop' ? Monitor : device.type === 'Mobile' ? Smartphone : Monitor

              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                      <Icon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{device.type}</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">{device.count.toLocaleString()} visitors</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-blue-600 dark:text-blue-400 border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20">
                    {device.percentage}%
                  </Badge>
                </div>
              )
            })}
          </CardContent>
        </Card>

        {/* Geographic Data */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white flex items-center">
              <Globe className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
              Top Countries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analyticsData.countries.map((country, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{country.name}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900 dark:text-white font-medium">{country.count.toLocaleString()}</span>
                      <Badge variant="outline" className="text-xs text-gray-600 dark:text-gray-400 border-gray-300 dark:border-gray-600">
                        {country.percentage}%
                      </Badge>
                    </div>
                  </div>
                  <Progress value={country.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
