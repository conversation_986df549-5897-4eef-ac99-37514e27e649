import { Button } from '@/components/ui/button'
import { LucideIcon, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ActionButtonProps {
  children: React.ReactNode
  icon?: LucideIcon
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  onClick?: () => void
  disabled?: boolean
  loading?: boolean
  className?: string
}

const variantClasses = {
  primary: 'bg-green-600 hover:bg-green-700 text-white border-green-600',
  secondary: 'bg-gray-600 hover:bg-gray-700 text-white border-gray-600',
  outline: 'border-green-600 dark:border-green-500 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20',
  ghost: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
}

export function ActionButton({
  children,
  icon: Icon,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false,
  loading = false,
  className
}: ActionButtonProps) {
  const buttonVariant = variant === 'primary' || variant === 'secondary' ? 'default' : variant

  return (
    <Button
      variant={buttonVariant}
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        variantClasses[variant],
        'transition-all duration-200',
        className
      )}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : Icon ? (
        <Icon className="h-4 w-4 mr-2" />
      ) : null}
      {children}
    </Button>
  )
}
