import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import { Post, PostFilters, CreatePostData, UpdatePostData, PostStatus, CreatePostResponse } from '@/types'
import { PaginationOptions } from '@/types/api'
import { Database } from '@/types/supabase'

type PostRow = Database['public']['Tables']['posts']['Row']
type PostTypeRow = Database['public']['Tables']['post_types']['Row']
type PostImageRow = Database['public']['Tables']['post_images']['Row']
type UserProfileRow = Database['public']['Tables']['user_profiles']['Row']

// Extended types with relations
type PostWithRelations = PostRow & {
  post_types: PostTypeRow
  post_images: PostImageRow[]
  user_profiles: UserProfileRow
}

export class PostSupabaseRepository {
  private supabase = createServerSupabaseAdminClient()

  /**
   * Transform Supabase post to application Post type
   */
  private async transformPost(supabasePost: PostRow | PostWithRelations): Promise<Post> {
    // Check if this is already a PostWithRelations or needs separate queries
    let postType: PostTypeRow | null = null
    let userProfile: UserProfileRow | null = null
    let postImages: PostImageRow[] = []

    if ('post_types' in supabasePost && 'user_profiles' in supabasePost && 'post_images' in supabasePost) {
      // Already has relations loaded
      const postWithRelations = supabasePost as PostWithRelations
      postType = postWithRelations.post_types
      userProfile = postWithRelations.user_profiles
      postImages = postWithRelations.post_images || []
    } else {
      // Get related data separately
      const { data: fetchedPostType } = await this.supabase
        .from('post_types')
        .select('*')
        .eq('id', supabasePost.type_id)
        .single()

      const { data: fetchedUserProfile } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', supabasePost.author_id)
        .single()

      const { data: fetchedPostImages } = await this.supabase
        .from('post_images')
        .select('*')
        .eq('post_id', supabasePost.id)
        .order('display_order')

      postType = fetchedPostType
      userProfile = fetchedUserProfile
      postImages = fetchedPostImages || []
    }

    return {
      id: supabasePost.id,
      title: supabasePost.title,
      slug: supabasePost.slug,
      content: supabasePost.content,
      excerpt: supabasePost.excerpt,
      version: supabasePost.version,
      type: postType ? {
        id: postType.id,
        name: postType.name,
        slug: postType.slug,
        color: postType.color,
        icon: postType.icon,
        createdAt: postType.created_at,
        updatedAt: postType.updated_at,
      } : {
        id: 'unknown',
        name: 'Unknown',
        slug: 'unknown',
        color: '#666666',
        icon: 'FileText',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      author: userProfile ? {
        id: userProfile.id,
        name: userProfile.name,
        email: '', // We don't expose email in public data
        createdAt: userProfile.created_at,
        updatedAt: userProfile.updated_at,
      } : {
        id: 'unknown',
        name: 'Unknown Author',
        email: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      status: supabasePost.status as PostStatus,
      createdAt: supabasePost.created_at,
      updatedAt: supabasePost.updated_at,
      publishedAt: supabasePost.published_at || supabasePost.created_at,
      reactions: {
        heart: supabasePost.reactions_heart,
      },
      comments: supabasePost.comments_count,
      viewCount: supabasePost.view_count,
      tags: supabasePost.tags,
      featured: supabasePost.featured,
      readTime: supabasePost.read_time,
      images: postImages.map(img => ({
        id: img.id,
        url: img.url,
        alt: img.alt_text || '',
        caption: img.caption || '',
        width: img.width || 0,
        height: img.height || 0,
      })),
    }
  }

  /**
   * Build filter conditions for Supabase query
   */
  private buildFilters(filters: PostFilters = {}) {
    let query = this.supabase
      .from('posts')
      .select('*')

    if (filters.status) {
      const statusMap: { [key: string]: string } = {
        'draft': 'DRAFT',
        'published': 'PUBLISHED',
        'archived': 'ARCHIVED'
      }
      const mappedStatus = statusMap[filters.status.toLowerCase()] || filters.status.toUpperCase()
      query = query.eq('status', mappedStatus)
    }

    if (filters.type) {
      query = query.eq('post_types.slug', filters.type)
    }

    if (filters.authorId) {
      query = query.eq('author_id', filters.authorId)
    }

    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%,tags.cs.{${filters.search}}`)
    }

    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured)
    }

    return query
  }

  /**
   * Find all posts with pagination and filtering
   */
  async findAll(
    pagination: PaginationOptions = {},
    filters: PostFilters = {}
  ): Promise<{ posts: Post[]; total: number }> {
    const { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'desc' } = pagination
    const from = (page - 1) * limit
    const to = from + limit - 1

    // Build base query with filters
    let query = this.buildFilters(filters)

    // Add sorting
    const ascending = sortOrder === 'asc'
    query = query.order(sortBy, { ascending })

    // Add pagination
    query = query.range(from, to)

    const { data: posts, error } = await query

    if (error) {
      console.error('Supabase error in findAll:', error)
      throw new Error(`Failed to fetch posts: ${error.message}`)
    }

    // Get total count for pagination
    const { count: totalCount, error: countError } = await this.buildFilters(filters)
      .select('*', { count: 'exact', head: true })

    if (countError) {
      console.error('Supabase error getting count:', countError)
      throw new Error(`Failed to get posts count: ${countError.message}`)
    }

    // Transform posts with await
    const transformedPosts = await Promise.all(
      (posts || []).map(post => this.transformPost(post as PostRow))
    )

    return {
      posts: transformedPosts,
      total: totalCount || 0
    }
  }

  /**
   * Find post by ID
   */
  async findById(id: string): Promise<Post | null> {
    try {
      // Get the post with basic data
      const { data: supabasePost, error: postError } = await this.supabase
        .from('posts')
        .select('*')
        .eq('id', id)
        .single()

      if (postError) {
        if (postError.code === 'PGRST116') {
          return null // Post not found
        }
        console.error('Supabase error fetching post:', postError)
        throw new Error(`Failed to fetch post: ${postError.message}`)
      }

      if (!supabasePost) {
        return null
      }

      // Transform using the async method
      return await this.transformPost(supabasePost as PostRow)
    } catch (error) {
      console.error('Error in findById:', error)
      throw new Error(`Failed to fetch post: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Find post by slug
   */
  async findBySlug(slug: string): Promise<Post | null> {
    const { data: post, error } = await this.supabase
      .from('posts')
      .select(`
        *,
        post_types!posts_type_id_fkey (*),
        post_images (*),
        user_profiles!posts_author_id_fkey (*)
      `)
      .eq('slug', slug)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Post not found
      }
      console.error('Supabase error in findBySlug:', error)
      throw new Error(`Failed to fetch post: ${error.message}`)
    }

    return post ? await this.transformPost(post as PostWithRelations) : null
  }

  /**
   * Get featured posts
   */
  async getFeaturedPosts(limit: number = 6): Promise<Post[]> {
    const { data: posts, error } = await this.supabase
      .from('posts')
      .select(`
        *,
        post_types!posts_type_id_fkey (*),
        post_images (*),
        user_profiles!posts_author_id_fkey (*)
      `)
      .eq('featured', true)
      .eq('status', 'PUBLISHED')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Supabase error in getFeaturedPosts:', error)
      throw new Error(`Failed to fetch featured posts: ${error.message}`)
    }

    return await Promise.all((posts || []).map(post => this.transformPost(post as PostWithRelations)))
  }

  /**
   * Get posts statistics
   */
  async getStats(): Promise<{
    total: number
    published: number
    draft: number
    featured: number
    totalViews: number
    totalReactions: number
  }> {
    const [
      { count: total },
      { count: published },
      { count: draft },
      { count: featured },
      { data: reactionsData },
      { data: viewsData }
    ] = await Promise.all([
      this.supabase.from('posts').select('*', { count: 'exact', head: true }),
      this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('status', 'PUBLISHED'),
      this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('status', 'DRAFT'),
      this.supabase.from('posts').select('*', { count: 'exact', head: true }).eq('featured', true),
      this.supabase.from('posts').select('reactions_heart'),
      this.supabase.from('posts').select('view_count')
    ])

    const totalReactions = (reactionsData || []).reduce((sum, post) => sum + (post.reactions_heart || 0), 0)
    const totalViews = (viewsData || []).reduce((sum, post) => sum + (post.view_count || 0), 0)

    return {
      total: total || 0,
      published: published || 0,
      draft: draft || 0,
      featured: featured || 0,
      totalViews,
      totalReactions
    }
  }

  /**
   * Generate excerpt from content
   */
  private generateExcerpt(content: string, maxLength: number = 200): string {
    const plainText = content.replace(/<[^>]*>/g, '').trim()

    if (plainText.length <= maxLength) {
      return plainText
    }

    const truncated = plainText.substring(0, maxLength)
    const lastSpaceIndex = truncated.lastIndexOf(' ')

    if (lastSpaceIndex > 0) {
      return truncated.substring(0, lastSpaceIndex) + '...'
    }

    return truncated + '...'
  }

  /**
   * Calculate estimated read time
   */
  private calculateReadTime(content: string): string {
    const wordsPerMinute = 200
    const plainText = content.replace(/<[^>]*>/g, '')
    const wordCount = plainText.split(/\s+/).length
    const minutes = Math.ceil(wordCount / wordsPerMinute)

    return `${minutes} min read`
  }

  /**
   * Create new post
   */
  async create(data: CreatePostData, slug: string): Promise<CreatePostResponse> {
    // Get post type by ID
    const { data: postType, error: typeError } = await this.supabase
      .from('post_types')
      .select('*')
      .eq('id', data.typeId)
      .single()

    if (typeError || !postType) {
      throw new Error(`Post type not found: ${data.typeId}`)
    }

    // Calculate read time based on content length
    const readTime = this.calculateReadTime(data.content)

    const { data: post, error } = await this.supabase
      .from('posts')
      .insert({
        title: data.title,
        slug: slug,
        content: data.content,
        excerpt: data.excerpt || false,
        version: 'v1.0',
        type_id: postType.id,
        author_id: data.authorId,
        status: data.status || PostStatus.DRAFT,
        published_at: data.status === PostStatus.PUBLISHED ? new Date().toISOString() : null,
        tags: data.tags || [],
        featured: data.featured || false,
        read_time: readTime,
        view_count: 0,
        reactions_heart: 0,
        comments_count: 0,
      })
      .select(`
        *,
        post_types!posts_type_id_fkey (*),
        post_images (*),
        user_profiles!posts_author_id_fkey (*)
      `)
      .single()

    if (error) {
      console.error('Supabase error in create:', error)
      throw new Error(`Failed to create post: ${error.message}`)
    }

    // Transform to CreatePostResponse format
    const transformedPost = await this.transformPost(post as PostWithRelations)

    return {
      id: transformedPost.id,
      title: transformedPost.title,
      slug: transformedPost.slug,
      content: transformedPost.content,
      excerpt: transformedPost.excerpt,
      status: transformedPost.status,
      featured: transformedPost.featured,
      featuredImage: undefined, // Not implemented yet
      author: transformedPost.author,
      type: {
        id: transformedPost.type.id,
        name: transformedPost.type.name,
        color: transformedPost.type.color,
        icon: transformedPost.type.icon,
      },
      tags: transformedPost.tags,
      viewCount: transformedPost.viewCount,
      reactions: {
        heart: transformedPost.reactions_heart
      },
      comments: transformedPost.comments || 0,
      createdAt: transformedPost.createdAt,
      updatedAt: transformedPost.updatedAt,
      publishedAt: transformedPost.publishedAt,
    }
  }

  /**
   * Update post
   */
  async update(id: string, data: UpdatePostData): Promise<Post | null> {
    const updateData: any = { ...data }

    // Handle type update
    if (data.typeId) {
      const { data: postType, error: typeError } = await this.supabase
        .from('post_types')
        .select('*')
        .eq('id', data.typeId)
        .single()

      if (typeError || !postType) {
        throw new Error(`Post type not found: ${data.typeId}`)
      }

      updateData.type_id = postType.id
      delete updateData.typeId
    }

    // Map field names to Supabase column names
    if (data.authorId) {
      updateData.author_id = data.authorId
      delete updateData.authorId
    }

    const { data: post, error } = await this.supabase
      .from('posts')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        post_types!posts_type_id_fkey (*),
        post_images (*),
        user_profiles!posts_author_id_fkey (*)
      `)
      .single()

    if (error) {
      console.error('Supabase error in update:', error)
      throw new Error(`Failed to update post: ${error.message}`)
    }

    return post ? await this.transformPost(post as PostWithRelations) : null
  }

  /**
   * Delete post
   */
  async delete(id: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('posts')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Supabase error in delete:', error)
      return false
    }

    return true
  }
}
