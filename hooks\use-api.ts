"use client"

import { useState, useEffect, useCallback } from 'react'
import { apiClient, ApiError } from '@/lib/api/client'
import { ApiResponse, PaginationOptions } from '@/types/api'

export interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
  meta?: any
}

export interface UseApiOptions {
  immediate?: boolean
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
}

/**
 * Generic API hook for data fetching
 */
export function useApi<T>(
  endpoint: string,
  options: UseApiOptions = {}
) {
  const { immediate = true, onSuccess, onError } = options
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const execute = useCallback(async (params?: Record<string, any>) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await apiClient.get<T>(endpoint, params)
      
      setState({
        data: response.data || null,
        loading: false,
        error: null,
        meta: response.meta,
      })

      if (onSuccess) {
        onSuccess(response.data)
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? error.message 
        : 'An unexpected error occurred'

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }))

      if (onError) {
        onError(errorMessage)
      }

      throw error
    }
  }, [endpoint, onSuccess, onError])

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [execute, immediate])

  return {
    ...state,
    execute,
    refetch: () => execute(),
  }
}

/**
 * Hook for paginated data fetching
 */
export function usePaginatedApi<T>(
  endpoint: string,
  initialOptions: PaginationOptions = {},
  options: UseApiOptions = {}
) {
  const [paginationOptions, setPaginationOptions] = useState<PaginationOptions>({
    page: 1,
    limit: 10,
    ...initialOptions,
  })

  const { data, loading, error, meta, execute } = useApi<T[]>(endpoint, {
    ...options,
    immediate: false,
  })

  const fetchData = useCallback(async (newOptions?: Partial<PaginationOptions>) => {
    const mergedOptions = { ...paginationOptions, ...newOptions }
    setPaginationOptions(mergedOptions)
    return execute(mergedOptions)
  }, [execute, paginationOptions])

  useEffect(() => {
    if (options.immediate !== false) {
      fetchData()
    }
  }, [fetchData, options.immediate])

  const nextPage = useCallback(() => {
    if (meta?.pagination?.hasNext) {
      fetchData({ page: (paginationOptions.page || 1) + 1 })
    }
  }, [fetchData, meta?.pagination?.hasNext, paginationOptions.page])

  const prevPage = useCallback(() => {
    if (meta?.pagination?.hasPrev) {
      fetchData({ page: (paginationOptions.page || 1) - 1 })
    }
  }, [fetchData, meta?.pagination?.hasPrev, paginationOptions.page])

  const goToPage = useCallback((page: number) => {
    fetchData({ page })
  }, [fetchData])

  const setSearch = useCallback((search: string) => {
    fetchData({ search, page: 1 })
  }, [fetchData])

  const setFilters = useCallback((filters: Record<string, any>) => {
    fetchData({ filters, page: 1 })
  }, [fetchData])

  return {
    data,
    loading,
    error,
    pagination: meta?.pagination,
    options: paginationOptions,
    fetchData,
    nextPage,
    prevPage,
    goToPage,
    setSearch,
    setFilters,
    refetch: () => fetchData(),
  }
}

/**
 * Hook for API mutations (POST, PUT, DELETE)
 */
export function useMutation<T, TVariables = any>(
  endpoint: string,
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const mutate = useCallback(async (variables?: TVariables) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      let response: ApiResponse<T>

      switch (method) {
        case 'POST':
          response = await apiClient.post<T>(endpoint, variables)
          break
        case 'PUT':
          response = await apiClient.put<T>(endpoint, variables)
          break
        case 'PATCH':
          response = await apiClient.patch<T>(endpoint, variables)
          break
        case 'DELETE':
          response = await apiClient.delete<T>(endpoint)
          break
        default:
          throw new Error(`Unsupported method: ${method}`)
      }

      setState({
        data: response.data || null,
        loading: false,
        error: null,
        meta: response.meta,
      })

      if (options.onSuccess) {
        options.onSuccess(response.data)
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? error.message 
        : 'An unexpected error occurred'

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }))

      if (options.onError) {
        options.onError(errorMessage)
      }

      throw error
    }
  }, [endpoint, method, options])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    })
  }, [])

  return {
    ...state,
    mutate,
    reset,
  }
}

/**
 * Specific hooks for common operations
 */
export function usePosts(options?: PaginationOptions) {
  return usePaginatedApi('/posts', options)
}

export function useCreatePost() {
  return useMutation('/posts', 'POST')
}

export function useUpdatePost(id: string) {
  return useMutation(`/posts/${id}`, 'PUT')
}

export function useDeletePost(id: string) {
  return useMutation(`/posts/${id}`, 'DELETE')
}
