"use client"

import { useState, useEffect, useCallback } from 'react'

interface UsePostInteractionsFallbackOptions {
  postId: string
  initialLikeCount?: number
  initialViewCount?: number
  autoRecordView?: boolean
}

interface UsePostInteractionsFallbackReturn {
  likeCount: number
  viewCount: number
  hasLiked: boolean
  userLikeCount: number
  hasViewedToday: boolean
  isLiking: boolean
  isRecordingView: boolean
  error: string | null
  likePost: () => Promise<void>
  recordView: () => Promise<void>
  refreshStatus: () => Promise<void>
}

/**
 * Fallback hook for post interactions when database tables don't exist yet
 * Uses direct post updates and localStorage for user state
 */
export function usePostInteractionsFallback({
  postId,
  initialLikeCount = 0,
  initialViewCount = 0,
  autoRecordView = true
}: UsePostInteractionsFallbackOptions): UsePostInteractionsFallbackReturn {
  const [likeCount, setLikeCount] = useState(initialLikeCount)
  const [viewCount, setViewCount] = useState(initialViewCount)
  const [hasLiked, setHasLiked] = useState(false)
  const [userLikeCount, setUserLikeCount] = useState(0)
  const [hasViewedToday, setHasViewedToday] = useState(false)
  const [isLiking, setIsLiking] = useState(false)
  const [isRecordingView, setIsRecordingView] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Storage keys
  const likeStorageKey = `post_likes_${postId}`
  const viewStorageKey = `post_views_${postId}`

  /**
   * Load user state from localStorage
   */
  const loadUserState = useCallback(() => {
    if (typeof window === 'undefined') return

    try {
      // Load like state
      const likedData = localStorage.getItem(likeStorageKey)
      if (likedData) {
        const { count, lastLiked } = JSON.parse(likedData)
        setUserLikeCount(count || 0)
        setHasLiked(count > 0)
      }

      // Load view state
      const viewData = localStorage.getItem(viewStorageKey)
      if (viewData) {
        const { lastViewed } = JSON.parse(viewData)
        const today = new Date().toDateString()
        const viewedToday = new Date(lastViewed).toDateString() === today
        setHasViewedToday(viewedToday)
      }
    } catch (err) {
      console.error('Error loading user state:', err)
    }
  }, [postId, likeStorageKey, viewStorageKey])

  /**
   * Save user state to localStorage
   */
  const saveUserState = useCallback((type: 'like' | 'view', data: any) => {
    if (typeof window === 'undefined') return

    try {
      if (type === 'like') {
        localStorage.setItem(likeStorageKey, JSON.stringify(data))
      } else if (type === 'view') {
        localStorage.setItem(viewStorageKey, JSON.stringify(data))
      }
    } catch (err) {
      console.error('Error saving user state:', err)
    }
  }, [likeStorageKey, viewStorageKey])

  /**
   * Refresh status from server
   */
  const refreshStatus = useCallback(async () => {
    try {
      setError(null)
      
      const response = await fetch(`/api/test/post-interactions-simple`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get-status',
          postId
        })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to get status')
      }

      setLikeCount(result.data.likeCount)
      setViewCount(result.data.viewCount)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh status'
      setError(errorMessage)
      console.error('Error refreshing status:', err)
    }
  }, [postId])

  /**
   * Like a post (fallback method)
   */
  const likePost = useCallback(async () => {
    if (isLiking) return

    try {
      setIsLiking(true)
      setError(null)

      // Optimistic update
      setLikeCount(prev => prev + 1)
      setUserLikeCount(prev => prev + 1)
      setHasLiked(true)

      const response = await fetch(`/api/test/post-interactions-simple`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'simulate-like',
          postId
        })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to like post')
      }

      // Update with actual count from server
      setLikeCount(result.data.newLikeCount)
      
      // Save user state
      const newUserLikeCount = userLikeCount + 1
      saveUserState('like', {
        count: newUserLikeCount,
        lastLiked: new Date().toISOString()
      })
      
      console.log('✅ Post liked successfully (fallback)')

    } catch (err) {
      // Revert optimistic update on error
      setLikeCount(prev => Math.max(0, prev - 1))
      setUserLikeCount(prev => Math.max(0, prev - 1))
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to like post'
      setError(errorMessage)
      console.error('Error liking post:', err)
    } finally {
      setIsLiking(false)
    }
  }, [postId, isLiking, userLikeCount, saveUserState])

  /**
   * Record a view (fallback method)
   */
  const recordView = useCallback(async () => {
    if (isRecordingView || hasViewedToday) return

    try {
      setIsRecordingView(true)
      setError(null)

      const response = await fetch(`/api/test/post-interactions-simple`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'simulate-view',
          postId
        })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to record view')
      }

      // Update view count and status
      setViewCount(result.data.newViewCount)
      setHasViewedToday(true)
      
      // Save user state
      saveUserState('view', {
        lastViewed: new Date().toISOString()
      })
      
      console.log('✅ View recorded successfully (fallback)')

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to record view'
      setError(errorMessage)
      console.error('Error recording view:', err)
    } finally {
      setIsRecordingView(false)
    }
  }, [postId, isRecordingView, hasViewedToday, saveUserState])

  // Load initial state and auto-record view
  useEffect(() => {
    let mounted = true

    const initialize = async () => {
      loadUserState()
      await refreshStatus()
      
      // Auto-record view if enabled and user hasn't viewed today
      if (mounted && autoRecordView && !hasViewedToday) {
        setTimeout(() => {
          if (mounted && !hasViewedToday) {
            recordView()
          }
        }, 1000)
      }
    }

    initialize()

    return () => {
      mounted = false
    }
  }, [postId, autoRecordView, loadUserState, refreshStatus, recordView, hasViewedToday])

  return {
    likeCount,
    viewCount,
    hasLiked,
    userLikeCount,
    hasViewedToday,
    isLiking,
    isRecordingView,
    error,
    likePost,
    recordView,
    refreshStatus
  }
}
