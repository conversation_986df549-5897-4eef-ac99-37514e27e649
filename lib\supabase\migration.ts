import { createServerSupabaseAdminClient } from './server'

/**
 * <PERSON>QL script to create the database schema in Supabase
 * This mirrors the current Prisma schema
 */
export const createTablesSQL = `
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create post_status enum
CREATE TYPE post_status AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED');

-- Create user_profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    bio TEXT,
    website VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create post_types table
CREATE TABLE IF NOT EXISTS post_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    color VARCHAR(200) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    icon VARCHAR(50) DEFAULT 'BookOpen',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create posts table
CREATE TABLE IF NOT EXISTS posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(600) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(50) DEFAULT 'v1.0',
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE RESTRICT,
    type_id UUID NOT NULL REFERENCES post_types(id) ON DELETE RESTRICT,
    status post_status DEFAULT 'DRAFT',
    excerpt BOOLEAN DEFAULT FALSE,
    featured BOOLEAN DEFAULT FALSE,
    tags TEXT[] DEFAULT '{}',
    reactions_heart INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    read_time VARCHAR(50) DEFAULT '5 min read',
    published_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create post_images table
CREATE TABLE IF NOT EXISTS post_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    url VARCHAR(1000) NOT NULL,
    alt_text VARCHAR(500),
    caption TEXT,
    width INTEGER,
    height INTEGER,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_name ON user_profiles(name);
CREATE INDEX IF NOT EXISTS idx_posts_slug ON posts(slug);
CREATE INDEX IF NOT EXISTS idx_posts_type_id ON posts(type_id);
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts(author_id);
CREATE INDEX IF NOT EXISTS idx_posts_featured ON posts(featured);
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_published_at ON posts(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_posts_tags ON posts USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_post_types_slug ON post_types(slug);
CREATE INDEX IF NOT EXISTS idx_post_images_post_id ON post_images(post_id);
CREATE INDEX IF NOT EXISTS idx_post_images_post_id_order ON post_images(post_id, display_order);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_images ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
-- User profiles: users can read all profiles, but only update their own
CREATE POLICY "Public profiles are viewable by everyone" ON user_profiles
FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON user_profiles
FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
FOR UPDATE USING (auth.uid() = id);

-- Posts: published posts are public, drafts only visible to author
CREATE POLICY "Published posts are viewable by everyone" ON posts
FOR SELECT USING (status = 'PUBLISHED');

CREATE POLICY "Authors can view their own posts" ON posts
FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authenticated users can insert posts" ON posts
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authors can update their own posts" ON posts
FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own posts" ON posts
FOR DELETE USING (auth.uid() = author_id);

-- Post types: readable by everyone
CREATE POLICY "Post types are viewable by everyone" ON post_types
FOR SELECT USING (true);

-- Post images: follow post permissions
CREATE POLICY "Post images are viewable with posts" ON post_images
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM posts
    WHERE posts.id = post_images.post_id
    AND (posts.status = 'PUBLISHED' OR posts.author_id = auth.uid())
  )
);

CREATE POLICY "Authors can manage their post images" ON post_images
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM posts
    WHERE posts.id = post_images.post_id
    AND posts.author_id = auth.uid()
  )
);

-- Function to automatically create user profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, name)
  VALUES (new.id, COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)));
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Insert default post types
INSERT INTO post_types (name, color, slug, icon) VALUES
    ('Learning', '#3B82F6', 'learning', 'BookOpen'),
    ('Error Log', '#EF4444', 'error-log', 'AlertCircle'),
    ('Opinion', '#8B5CF6', 'opinion', 'MessageCircle'),
    ('Tip', '#10B981', 'tip', 'Lightbulb'),
    ('Showcase', '#F59E0B', 'showcase', 'Star'),
    ('Photo', '#EC4899', 'photo', 'Camera')
ON CONFLICT (slug) DO NOTHING;
`

/**
 * Function to run the migration
 * Note: This will output the SQL for manual execution in Supabase Dashboard
 */
export async function runSupabaseMigration() {
  try {
    console.log('🚀 Starting Supabase migration...')
    console.log('\n📋 Please execute the following SQL in your Supabase Dashboard:')
    console.log('   1. Go to https://app.supabase.com/project/[your-project]/sql')
    console.log('   2. Copy and paste the SQL below')
    console.log('   3. Click "Run" to execute\n')

    console.log('--- SQL START ---')
    console.log(createTablesSQL)
    console.log('--- SQL END ---')

    console.log('\n✅ SQL migration script generated!')
    console.log('💡 After running the SQL in Supabase Dashboard, run this command again to verify.')

    return { success: true }
  } catch (error) {
    console.error('❌ Migration error:', error)
    return { success: false, error }
  }
}

/**
 * Function to check if tables exist
 */
export async function checkSupabaseSchema() {
  const supabase = createServerSupabaseAdminClient()

  try {
    // Check if main tables exist
    const { data: userProfiles } = await supabase.from('user_profiles').select('id').limit(1)
    const { data: posts } = await supabase.from('posts').select('id').limit(1)
    const { data: postTypes } = await supabase.from('post_types').select('id').limit(1)

    return {
      userProfiles: userProfiles !== null,
      posts: posts !== null,
      postTypes: postTypes !== null
    }
  } catch (error) {
    console.error('Schema check error:', error)
    return {
      userProfiles: false,
      posts: false,
      postTypes: false
    }
  }
}
