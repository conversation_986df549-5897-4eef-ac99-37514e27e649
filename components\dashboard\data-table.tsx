import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DataTableProps<T> {
  title: string
  data: T[]
  loading?: boolean
  error?: string | null
  emptyMessage?: string
  renderItem: (item: T, index: number) => React.ReactNode
  onRetry?: () => void
  className?: string
  headerActions?: React.ReactNode
}

export function DataTable<T>({
  title,
  data,
  loading = false,
  error = null,
  emptyMessage = "No data found",
  renderItem,
  onRetry,
  className,
  headerActions
}: DataTableProps<T>) {
  return (
    <Card className={cn(
      "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-gray-900 dark:text-white">{title}</CardTitle>
        {headerActions && (
          <div className="flex items-center space-x-2">
            {headerActions}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-green-600 dark:text-green-400" />
            <span className="ml-3 text-gray-600 dark:text-gray-400">Loading...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <p className="font-medium">Something went wrong</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
            {onRetry && (
              <Button
                variant="outline"
                onClick={onRetry}
                className="border-green-600 dark:border-green-500 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
              >
                Try Again
              </Button>
            )}
          </div>
        ) : data.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">{emptyMessage}</p>
          </div>
        ) : (
          <div className="space-y-3">
            {data.map((item, index) => renderItem(item, index))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
