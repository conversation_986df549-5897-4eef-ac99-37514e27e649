@tailwind base;
@tailwind components;
@tailwind utilities;



/* Premium dropdown animations - Override Tailwind utilities directly */
@layer utilities {
  /* Override specific Tailwind animation classes */
  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: premium-dropdown-enter 300ms cubic-bezier(0.16, 1, 0.3, 1) !important;
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: premium-dropdown-exit 200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Override fade and zoom animations specifically */
  .data-\[state\=open\]\:fade-in-0[data-state="open"],
  .data-\[state\=open\]\:zoom-in-95[data-state="open"],
  .data-\[state\=open\]\:zoom-in-98[data-state="open"] {
    animation: premium-dropdown-enter 300ms cubic-bezier(0.16, 1, 0.3, 1) !important;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"],
  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"],
  .data-\[state\=closed\]\:zoom-out-98[data-state="closed"] {
    animation: premium-dropdown-exit 200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

@keyframes premium-dropdown-enter {
  0% {
    opacity: 0;
    transform: scale(0.96);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes premium-dropdown-exit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.98);
  }
}

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    /* Green accent colors (preserved across themes) */
    --green-primary: 142 76% 36%;
    --green-secondary: 142 69% 58%;
    --green-accent: 142 76% 36%;
    --emerald-primary: 160 84% 39%;
    --emerald-secondary: 160 81% 58%;

    /* Semantic color mapping for light theme */
    --theme-bg-primary: 0 0% 100%;
    --theme-bg-secondary: 0 0% 98%;
    --theme-bg-tertiary: 0 0% 96.1%;
    --theme-text-primary: 0 0% 3.9%;
    --theme-text-secondary: 0 0% 45.1%;
    --theme-text-muted: 0 0% 64.9%;
    --theme-border: 0 0% 89.8%;
    --theme-border-hover: 0 0% 80%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;

    /* Green accent colors (same as light theme - preserved) */
    --green-primary: 142 76% 36%;
    --green-secondary: 142 69% 58%;
    --green-accent: 142 76% 36%;
    --emerald-primary: 160 84% 39%;
    --emerald-secondary: 160 81% 58%;

    /* Semantic color mapping for dark theme */
    --theme-bg-primary: 0 0% 3.9%;
    --theme-bg-secondary: 0 0% 7%;
    --theme-bg-tertiary: 0 0% 14.9%;
    --theme-text-primary: 0 0% 98%;
    --theme-text-secondary: 0 0% 63.9%;
    --theme-text-muted: 0 0% 45.1%;
    --theme-border: 0 0% 14.9%;
    --theme-border-hover: 0 0% 25%;

    /* Chart colors for dark theme */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Theme transition class for smooth animations */
.theme-transition {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Apply theme transitions to common elements */
html {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced scrollbar styling - theme aware */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(243 244 246);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(31 41 55);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(34 197 94), rgb(16 185 129));
  border-radius: 4px;
  border: 1px solid rgb(229 231 235);
}

.dark ::-webkit-scrollbar-thumb {
  border: 1px solid rgb(55 65 81);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(22 163 74), rgb(5 150 105));
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Enhanced text selection */
::selection {
  background: rgb(34 197 94 / 0.3);
  color: rgb(34 197 94);
}

/* Smooth animations */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(to right, rgb(34 197 94), rgb(16 185 129));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced focus styles */
.focus-visible\:ring-green-500:focus-visible {
  --tw-ring-color: rgb(34 197 94);
}

/* Backdrop blur enhancement */
.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04), 0 0 0 1px rgb(34 197 94 / 0.1);
}

/* Typography enhancements */
.prose-enhanced {
  line-height: 1.75;
  color: rgb(209 213 219);
}

.prose-enhanced h1,
.prose-enhanced h2,
.prose-enhanced h3 {
  color: rgb(255 255 255);
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose-enhanced p {
  margin-bottom: 1.5rem;
}

.prose-enhanced code {
  background: rgb(31 41 55);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: rgb(34 197 94);
}

.prose-enhanced pre {
  background: rgb(17 24 39);
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  border: 1px solid rgb(55 65 81);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, rgb(31 41 55) 25%, rgb(55 65 81) 50%, rgb(31 41 55) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-responsive {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
