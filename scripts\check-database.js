const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.log('Required:')
  console.log('- NEXT_PUBLIC_SUPABASE_URL')
  console.log('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkDatabase() {
  console.log('🔍 Checking Supabase database schema...\n')

  try {
    // Check if tables exist
    console.log('📋 Checking tables:')
    
    // Check user_profiles
    const { data: userProfiles, error: userProfilesError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1)
    
    console.log(`  user_profiles: ${userProfilesError ? '❌ Missing' : '✅ Exists'}`)
    if (userProfilesError) console.log(`    Error: ${userProfilesError.message}`)

    // Check post_types
    const { data: postTypes, error: postTypesError } = await supabase
      .from('post_types')
      .select('id')
      .limit(1)
    
    console.log(`  post_types: ${postTypesError ? '❌ Missing' : '✅ Exists'}`)
    if (postTypesError) console.log(`    Error: ${postTypesError.message}`)

    // Check posts
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id')
      .limit(1)
    
    console.log(`  posts: ${postsError ? '❌ Missing' : '✅ Exists'}`)
    if (postsError) console.log(`    Error: ${postsError.message}`)

    // Check posts structure
    if (!postsError) {
      console.log('\n📊 Checking posts table structure:')
      const { data: postsStructure, error: structureError } = await supabase
        .from('posts')
        .select('*')
        .limit(1)
      
      if (!structureError && postsStructure && postsStructure.length > 0) {
        const post = postsStructure[0]
        console.log('  Available columns:', Object.keys(post).join(', '))
      } else {
        console.log('  No posts found or structure error')
      }
    }

    // Check if we can create a test post
    console.log('\n🧪 Testing post creation:')
    const { data: testPost, error: createError } = await supabase
      .from('posts')
      .insert({
        title: 'Test Post',
        slug: 'test-post-' + Date.now(),
        content: 'This is a test post',
        author_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        type_id: '00000000-0000-0000-0000-000000000000' // Dummy UUID
      })
      .select()
    
    if (createError) {
      console.log(`  ❌ Cannot create post: ${createError.message}`)
    } else {
      console.log('  ✅ Post creation works')
      
      // Clean up test post
      if (testPost && testPost.length > 0) {
        await supabase.from('posts').delete().eq('id', testPost[0].id)
        console.log('  🧹 Test post cleaned up')
      }
    }

  } catch (error) {
    console.error('❌ Database check failed:', error.message)
  }
}

checkDatabase()
