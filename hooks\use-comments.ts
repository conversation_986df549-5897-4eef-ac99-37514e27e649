"use client"

import { useState, useCallback } from 'react'

export interface Comment {
  id: string
  author: string
  content: string
  createdAt: string
  reactions: {
    thumbsUp: number
    heart: number
  }
}

interface UseCommentsOptions {
  initialComments?: Comment[]
  onCommentSubmit?: (content: string) => Promise<Comment>
  onCommentReaction?: (commentId: string, type: 'thumbsUp' | 'heart') => Promise<void>
}

interface UseCommentsReturn {
  comments: Comment[]
  newComment: string
  setNewComment: (content: string) => void
  submitComment: () => Promise<void>
  addCommentReaction: (commentId: string, type: 'thumbsUp' | 'heart') => Promise<void>
  isSubmitting: boolean
  error: string | null
}

/**
 * Custom hook for managing comments state and interactions
 */
export function useComments({
  initialComments = [],
  onCommentSubmit,
  onCommentReaction
}: UseCommentsOptions): UseCommentsReturn {
  const [comments, setComments] = useState<Comment[]>(initialComments)
  const [newComment, setNewComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const submitComment = useCallback(async () => {
    if (!newComment.trim() || isSubmitting) return

    try {
      setIsSubmitting(true)
      setError(null)

      if (onCommentSubmit) {
        const comment = await onCommentSubmit(newComment.trim())
        setComments(prev => [...prev, comment])
      } else {
        // Mock comment for development
        const mockComment: Comment = {
          id: Date.now().toString(),
          author: "Current User",
          content: newComment.trim(),
          createdAt: new Date().toISOString(),
          reactions: { thumbsUp: 0, heart: 0 }
        }
        setComments(prev => [...prev, mockComment])
      }

      setNewComment("")
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit comment')
    } finally {
      setIsSubmitting(false)
    }
  }, [newComment, isSubmitting, onCommentSubmit])

  const addCommentReaction = useCallback(async (commentId: string, type: 'thumbsUp' | 'heart') => {
    try {
      setError(null)

      // Optimistic update
      setComments(prev => prev.map(comment => 
        comment.id === commentId
          ? {
              ...comment,
              reactions: {
                ...comment.reactions,
                [type]: comment.reactions[type] + 1
              }
            }
          : comment
      ))

      if (onCommentReaction) {
        await onCommentReaction(commentId, type)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add reaction')
      
      // Revert optimistic update
      setComments(prev => prev.map(comment => 
        comment.id === commentId
          ? {
              ...comment,
              reactions: {
                ...comment.reactions,
                [type]: Math.max(0, comment.reactions[type] - 1)
              }
            }
          : comment
      ))
    }
  }, [onCommentReaction])

  return {
    comments,
    newComment,
    setNewComment,
    submitComment,
    addCommentReaction,
    isSubmitting,
    error
  }
}
