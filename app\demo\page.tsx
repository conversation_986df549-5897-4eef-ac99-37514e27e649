"use client"

import { useState } from "react"
import { ThreadWithPhoto } from "@/components/thread-with-photo"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>Left } from "lucide-react"
import Link from "next/link"

const mockComments = [
  {
    id: "1",
    author: "<PERSON>",
    content:
      "This is absolutely stunning! The composition and lighting are perfect. Thanks for sharing the behind-the-scenes process.",
    timestamp: "2024-01-20T10:30:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
    reactions: {
      thumbsUp: 8,
      heart: 12,
      brain: 3,
    },
  },
  {
    id: "2",
    author: "<PERSON>",
    content: "Great shot! What camera settings did you use for this? The depth of field is incredible.",
    timestamp: "2024-01-20T11:15:00Z",
    reactions: {
      thumbsUp: 5,
      heart: 3,
    },
  },
  {
    id: "3",
    author: "<PERSON>",
    content:
      "Love the mood and atmosphere in this photo. It really captures the essence of the moment. Looking forward to seeing more of your work!",
    timestamp: "2024-01-20T12:45:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
    reactions: {
      thumbsUp: 15,
      heart: 20,
      brain: 7,
    },
  },
]

export default function DemoPage() {
  const [comments, setComments] = useState(mockComments)

  const handleAddComment = (content: string) => {
    const newComment = {
      id: Date.now().toString(),
      author: "You",
      content,
      timestamp: new Date().toISOString(),
      reactions: {
        thumbsUp: 0,
        heart: 0,
      },
    }
    setComments([...comments, newComment])
  }

  const handleReaction = (type: string) => {
    console.log(`Reacted with: ${type}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Header */}
      <header className="relative border-b border-gray-800/50 bg-gray-900/80 backdrop-blur-xl sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-green-400 hover:bg-green-500/10 transition-all duration-200"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Hub
              </Button>
            </Link>
            <div className="h-6 w-px bg-gray-700" />
            <h1 className="text-xl font-bold text-white">Thread with Photo Demo</h1>
          </div>
        </div>
      </header>

      <main className="relative container mx-auto px-4 py-8">
        <ThreadWithPhoto
          title="Capturing the Golden Hour: A Photography Journey"
          content={`Just finished an amazing photography session during golden hour at the local park. The lighting was absolutely perfect, creating this magical atmosphere that I've been trying to capture for weeks.

This shot represents hours of patience and planning. I arrived early to scout the location, waited for the perfect moment when the sun was at just the right angle, and finally got this composition that tells the story I wanted to share.

Photography has taught me so much about patience, observation, and finding beauty in everyday moments. Sometimes the best shots come when you least expect them, but preparation and persistence always pay off.

What do you think? I'd love to hear your thoughts and any photography tips you might have!`}
          author="Alex Thompson"
          timestamp="2024-01-20T09:15:00Z"
          photoUrl="/placeholder.svg?height=600&width=800"
          photoAlt="Golden hour photography in a park with beautiful lighting"
          authorAvatar="/placeholder.svg?height=48&width=48"
          reactions={{
            thumbsUp: 42,
            heart: 38,
            brain: 15,
          }}
          comments={comments}
          onAddComment={handleAddComment}
          onReaction={handleReaction}
          className="mb-8"
        />

        {/* Example with missing photo */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-white mb-6">Example with Error Handling</h2>
          <ThreadWithPhoto
            title="Thread with Missing Photo"
            content="This thread demonstrates how the component handles missing or broken image URLs gracefully. The error state provides clear feedback to users while maintaining the overall design integrity."
            author="Demo User"
            timestamp="2024-01-20T14:30:00Z"
            photoUrl="https://invalid-url-that-will-fail.jpg"
            photoAlt="This image will fail to load"
            reactions={{
              thumbsUp: 5,
              heart: 2,
              brain: 1,
            }}
            comments={[]}
            onAddComment={handleAddComment}
            onReaction={handleReaction}
          />
        </div>
      </main>
    </div>
  )
}
