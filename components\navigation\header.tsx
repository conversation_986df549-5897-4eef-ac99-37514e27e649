"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Terminal, Search, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { APP_CONFIG } from "@/lib/constants"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { LanguageSwitcher } from "@/components/navigation/language-switcher"
import { useTranslations } from '@/contexts/locale-context'

interface HeaderProps {
  showBackButton?: boolean
  title?: string
  subtitle?: string
  onSearchClick?: () => void
  showActions?: boolean
}

export function Header({
  showBackButton = false,
  title,
  subtitle,
  onSearchClick,
  showActions = true
}: HeaderProps) {
  const t = useTranslations()

  return (
    <header className="sticky top-0 z-50 border-b border-gray-200/50 bg-white/80 backdrop-blur-xl dark:border-gray-800/50 dark:bg-gray-900/80 theme-transition">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {showBackButton && (
              <>
                <Button variant="ghost" size="sm" asChild className="text-gray-600 hover:text-green-500 dark:text-gray-400 dark:hover:text-green-400 theme-transition">
                  <Link href="/">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Home
                  </Link>
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-700 theme-transition"></div>
              </>
            )}

            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                <Terminal className="w-5 h-5 text-black" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">
                  {title || APP_CONFIG.name}
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400 theme-transition">
                  {subtitle || t('header.tagline')}
                </p>
              </div>
            </div>

            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 bg-gray-100/50 dark:border-gray-600 dark:text-gray-400 dark:bg-gray-800/50 theme-transition">
              {t('header.version')}
            </Badge>
          </div>

          {showActions && (
            <div className="flex items-center gap-3">
              {onSearchClick && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onSearchClick}
                  className="border-gray-300 text-gray-600 hover:text-green-500 hover:border-green-500 bg-gray-100/50 backdrop-blur-sm transition-all duration-200 dark:border-gray-700 dark:text-gray-400 dark:hover:text-green-400 dark:bg-gray-800/50 theme-transition"
                >
                  <Search className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">{t('header.searchPlaceholder')}</span>
                  <kbd className="ml-2 px-1.5 py-0.5 text-xs bg-gray-200 rounded dark:bg-gray-700 theme-transition">⌘K</kbd>
                </Button>
              )}

              <LanguageSwitcher />
              <ThemeSwitcher />
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
