"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var posts_1 = require("../lib/data/posts");
var constants_1 = require("../lib/constants");
var prisma = new client_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var postTypes, _loop_1, _i, postsData_1, postData, postTypeCount, postCount, imageCount, featuredCount;
        var _this = this;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('🌱 Starting database seeding...');
                    // Clear existing data
                    console.log('🧹 Clearing existing data...');
                    return [4 /*yield*/, prisma.postImage.deleteMany()];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, prisma.post.deleteMany()];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, prisma.postType.deleteMany()
                        // Seed post types first
                    ];
                case 3:
                    _a.sent();
                    // Seed post types first
                    console.log('📋 Seeding post types...');
                    return [4 /*yield*/, Promise.all(Object.entries(constants_1.POST_TYPE_CONFIG).map(function (_a) { return __awaiter(_this, [_a], void 0, function (_b) {
                            var key = _b[0], config = _b[1];
                            return __generator(this, function (_c) {
                                switch (_c.label) {
                                    case 0: return [4 /*yield*/, prisma.postType.create({
                                            data: {
                                                name: config.label,
                                                color: config.color, // Now using hex colors
                                                slug: config.slug,
                                            },
                                        })];
                                    case 1: return [2 /*return*/, _c.sent()];
                                }
                            });
                        }); }))];
                case 4:
                    postTypes = _a.sent();
                    console.log("\u2705 Created ".concat(postTypes.length, " post types"));
                    console.log('📝 Seeding posts...');
                    _loop_1 = function (postData) {
                        var postType, post, i, imageData;
                        return __generator(this, function (_b) {
                            switch (_b.label) {
                                case 0:
                                    postType = postTypes.find(function (pt) { return pt.slug === postData.type; });
                                    if (!postType) {
                                        console.error("\u274C Post type not found for slug: ".concat(postData.type));
                                        return [2 /*return*/, "continue"];
                                    }
                                    return [4 /*yield*/, prisma.post.create({
                                            data: {
                                                // Don't specify ID, let database generate UUID
                                                title: postData.title,
                                                content: postData.content,
                                                version: postData.version,
                                                typeId: postType.id,
                                                author: postData.author,
                                                createdAt: new Date(postData.createdAt),
                                                updatedAt: new Date(postData.updatedAt),
                                                reactionsThumbsUp: postData.reactions.thumbsUp,
                                                reactionsHeart: postData.reactions.heart,
                                                reactionsBrain: postData.reactions.brain,
                                                commentsCount: postData.comments,
                                                tags: postData.tags,
                                                featured: postData.featured,
                                                readTime: postData.readTime,
                                            },
                                        })
                                        // Seed images if they exist
                                    ];
                                case 1:
                                    post = _b.sent();
                                    if (!(postData.images && postData.images.length > 0)) return [3 /*break*/, 5];
                                    console.log("\uD83D\uDCF8 Seeding ".concat(postData.images.length, " images for post: ").concat(post.title));
                                    i = 0;
                                    _b.label = 2;
                                case 2:
                                    if (!(i < postData.images.length)) return [3 /*break*/, 5];
                                    imageData = postData.images[i];
                                    return [4 /*yield*/, prisma.postImage.create({
                                            data: {
                                                // Don't specify ID, let database generate UUID
                                                postId: post.id,
                                                url: imageData.url,
                                                altText: imageData.alt,
                                                caption: imageData.caption,
                                                width: imageData.width,
                                                height: imageData.height,
                                                displayOrder: i,
                                            },
                                        })];
                                case 3:
                                    _b.sent();
                                    _b.label = 4;
                                case 4:
                                    i++;
                                    return [3 /*break*/, 2];
                                case 5:
                                    console.log("\u2705 Seeded post: ".concat(post.title));
                                    return [2 /*return*/];
                            }
                        });
                    };
                    _i = 0, postsData_1 = posts_1.postsData;
                    _a.label = 5;
                case 5:
                    if (!(_i < postsData_1.length)) return [3 /*break*/, 8];
                    postData = postsData_1[_i];
                    return [5 /*yield**/, _loop_1(postData)];
                case 6:
                    _a.sent();
                    _a.label = 7;
                case 7:
                    _i++;
                    return [3 /*break*/, 5];
                case 8: return [4 /*yield*/, prisma.postType.count()];
                case 9:
                    postTypeCount = _a.sent();
                    return [4 /*yield*/, prisma.post.count()];
                case 10:
                    postCount = _a.sent();
                    return [4 /*yield*/, prisma.postImage.count()];
                case 11:
                    imageCount = _a.sent();
                    return [4 /*yield*/, prisma.post.count({ where: { featured: true } })];
                case 12:
                    featuredCount = _a.sent();
                    console.log('\n📊 Seeding Summary:');
                    console.log("   Post Types: ".concat(postTypeCount));
                    console.log("   Posts: ".concat(postCount));
                    console.log("   Images: ".concat(imageCount));
                    console.log("   Featured: ".concat(featuredCount));
                    console.log('🎉 Database seeding completed successfully!');
                    return [2 /*return*/];
            }
        });
    });
}
main()
    .catch(function (e) {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                return [2 /*return*/];
        }
    });
}); });
