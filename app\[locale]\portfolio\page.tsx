import { PortfolioPageClient } from "@/components/portfolio/portfolio-page-client"
import { DataService } from "@/lib/services/data-service"
import { getMessages, createTranslator, type Locale } from '@/lib/i18n'
import { getPortfolioData } from '@/lib/data/portfolio-bilingual'
import { Metadata } from 'next'

interface PortfolioPageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata({ params }: PortfolioPageProps): Promise<Metadata> {
  const { locale } = await params
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)

  return {
    title: `${t('navigation.portfolio')} | ${t('meta.title')}`,
    description: locale === 'id' 
      ? 'Koleksi proyek dan karya terbaik saya dalam pengembangan web dan aplikasi.'
      : 'A collection of my best projects and work in web development and applications.',
    keywords: locale === 'id'
      ? 'portofolio, proyek, pengembangan web, aplikasi, full-stack developer'
      : 'portfolio, projects, web development, applications, full-stack developer',
  }
}

export default async function PortfolioPage({ params }: PortfolioPageProps) {
  const { locale } = await params
  
  // Get bilingual portfolio data
  const portfolio = getPortfolioData(locale as 'en' | 'id')

  // Get translations
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)
  
  const translations = {
    navigation: {
      backToHome: t('common.backToHome'),
      portfolio: t('navigation.portfolio')
    },
    header: {
      title: t('portfolio.title'),
      subtitle: t('portfolio.subtitle'),
      description: t('portfolio.description')
    },
    filters: {
      all: t('common.all'),
      categories: t('portfolio.categories'),
      search: t('common.search'),
      searchPlaceholder: t('portfolio.searchPlaceholder')
    },
    project: {
      viewProject: t('portfolio.viewProject'),
      viewCode: t('portfolio.viewCode'),
      caseStudy: t('portfolio.caseStudy'),
      technologies: t('portfolio.technologies'),
      features: t('portfolio.features'),
      challenges: t('portfolio.challenges'),
      solutions: t('portfolio.solutions'),
      metrics: t('portfolio.metrics'),
      status: t('portfolio.status'),
      duration: t('portfolio.duration'),
      team: t('portfolio.team'),
      role: t('portfolio.role')
    },
    stats: {
      projects: t('portfolio.stats.projects'),
      users: t('portfolio.stats.users'),
      rating: t('portfolio.stats.rating')
    },
    cta: {
      startProject: t('portfolio.cta.startProject'),
      viewExperience: t('portfolio.cta.viewExperience')
    }
  }

  return (
    <PortfolioPageClient
      portfolio={portfolio}
      locale={locale}
      translations={translations}
    />
  )
}
