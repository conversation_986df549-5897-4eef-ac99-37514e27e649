"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { StatsCard } from '@/components/dashboard/stats-card'
import { FilterBar } from '@/components/dashboard/filter-bar'
import { DataTable } from '@/components/dashboard/data-table'
import { PostItem } from '@/components/dashboard/post-item'
import { Pagination } from '@/components/dashboard/pagination'
import { Button } from '@/components/ui/button'
import { usePostsApi, usePostStats } from '@/hooks/use-posts-api'
import {
  Plus,
  Tag,
  FileText,
  TrendingUp,
  Edit,
  Eye
} from 'lucide-react'

// Filter options for status
const statusFilterOptions = [
  { label: 'Published', value: 'published' },
  { label: 'Draft', value: 'draft' },
  { label: 'Archived', value: 'archived' }
]

export default function PostsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  // Use API hooks
  const { stats, loading: statsLoading } = usePostStats()
  const {
    posts,
    pagination,
    loading: postsLoading,
    error,
    updateFilters,
    updatePagination
  } = usePostsApi({
    filters: {
      search: searchQuery,
      status: statusFilter as any
    },
    page: currentPage,
    limit: itemsPerPage
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    updateFilters({ search: query })
  }

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status)
    updateFilters({ status: status as any })
  }

  const handleEditPost = (id: string) => {
    router.push(`/dashboard/posts/${id}/edit`)
  }

  const handleDeletePost = (id: string) => {
    // TODO: Show delete confirmation dialog
    console.log('Delete post:', id)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    updatePagination(page, itemsPerPage)
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1) // Reset to first page
    updatePagination(1, newItemsPerPage)
  }

  return (
    <DashboardLayout
      title="Posts Management"
      description="Create, edit, and manage your blog posts"
    >
      <div className="space-y-6">
        {/* Filter Bar */}
        <FilterBar
          searchValue={searchQuery}
          onSearchChange={handleSearch}
          searchPlaceholder="Search posts..."
          filterValue={statusFilter}
          onFilterChange={handleStatusFilter}
          filterOptions={statusFilterOptions}
          filterLabel="All Status"
          actions={
            <>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/posts/types')}
                className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500"
              >
                <Tag className="h-4 w-4 mr-2" />
                Manage Types
              </Button>
              <Button
                onClick={() => router.push('/dashboard/posts/new')}
                className="bg-green-600 hover:bg-green-700 text-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Post
              </Button>
            </>
          }
        />

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatsCard
            title="Total Posts"
            value={stats?.totalPosts || 0}
            icon={FileText}
            iconColor="blue"
            loading={statsLoading}
          />
          <StatsCard
            title="Published"
            value={stats?.publishedPosts || 0}
            icon={TrendingUp}
            iconColor="green"
            loading={statsLoading}
          />
          <StatsCard
            title="Drafts"
            value={stats?.draftPosts || 0}
            icon={Edit}
            iconColor="yellow"
            loading={statsLoading}
          />
          <StatsCard
            title="Total Views"
            value={stats?.totalViews || 0}
            icon={Eye}
            iconColor="purple"
            loading={statsLoading}
          />
        </div>

        {/* Posts Table */}
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden theme-transition">
          <DataTable
            title="All Posts"
            data={posts}
            loading={postsLoading}
            error={error}
            emptyMessage="No posts found"
            onRetry={() => window.location.reload()}
            renderItem={(post) => (
              <PostItem
                key={post.id}
                post={{
                  id: post.id,
                  title: post.title,
                  author: typeof post.author === 'string' ? post.author : post.author?.name || 'Unknown Author',
                  publishedAt: post.publishedAt,
                  viewCount: post.viewCount,
                  reactions: {
                    heart: post.reactionHart || 0,
                    thumbsUp: 0,
                    brain: 0
                  },
                  comments: post.comments || 0,
                  status: post.status,
                  featured: post.featured,
                  type: {
                    name: post.type?.name || 'Article',
                    color: post.type?.color || '#10b981'
                  }
                }}
                onEdit={handleEditPost}
                onDelete={handleDeletePost}
              />
            )}
          />

          {/* Pagination */}
          {pagination && pagination.total > 0 && (
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              totalItems={pagination.total}
              itemsPerPage={pagination.limit}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              loading={postsLoading}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
