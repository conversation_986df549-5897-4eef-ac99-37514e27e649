import { NextRequest } from 'next/server'
import { PostInteractionsService } from '@/services/post-interactions.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

interface RouteParams {
  params: Promise<{
    postId: string
  }>
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params
    console.log('👁️ Recording view for post:', postId)

    // Get user ID from auth (if authenticated)
    const userId = undefined // TODO: Get from auth when implemented

    const interactionsService = new PostInteractionsService()
    const result = await interactionsService.viewPost(postId, userId)

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || 'Failed to record view')
    }

    console.log('✅ View recorded:', result.data)

    return ApiResponseBuilder.success(
      result.data,
      result.data?.message || 'View recorded successfully'
    )

  } catch (error) {
    console.error('💥 Error in view endpoint:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to record view'
    )
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params
    console.log('📊 Getting view status for post:', postId)

    // Get user ID from auth (if authenticated)
    const userId = undefined // TODO: Get from auth when implemented

    const interactionsService = new PostInteractionsService()
    const result = await interactionsService.getViewStatus(postId, userId)

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || 'Failed to get view status')
    }

    return ApiResponseBuilder.success(
      result.data,
      'View status retrieved successfully'
    )

  } catch (error) {
    console.error('💥 Error in view status endpoint:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to get view status'
    )
  }
}
