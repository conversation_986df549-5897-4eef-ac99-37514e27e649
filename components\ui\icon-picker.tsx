"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Icon, IconName, iconMap } from "./icon"
import { cn } from "@/lib/utils"
import { ScrollArea } from "@/components/ui/scroll-area"

interface IconPickerProps {
  value?: IconName
  onChange?: (icon: IconName) => void
  trigger?: React.ReactNode
  className?: string
}

interface IconPickerContentProps {
  value?: IconName
  onChange?: (icon: IconName) => void
  className?: string
}

const INITIAL_LOAD = 48 // 6 columns x 8 rows
const LOAD_MORE_SIZE = 24 // 6 columns x 4 rows

// Content component without dialog wrapper
export function IconPickerContent({ value, onChange, className }: IconPickerContentProps) {
  const [search, setSearch] = React.useState("")
  const [visibleCount, setVisibleCount] = React.useState(INITIAL_LOAD)
  const [isLoading, setIsLoading] = React.useState(false)
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)
  
  // Get all icon names from the iconMap
  const allIcons = React.useMemo(() => Object.keys(iconMap) as IconName[], [])
  
  // Filtered icons based on search (search all icons, not just visible ones)
  const filteredIcons = React.useMemo(() => {
    let icons = allIcons
    if (search) {
      icons = icons.filter((icon) => icon.toLowerCase().includes(search.toLowerCase()))
    }
    return icons
  }, [search, allIcons])

  // Icons to display (limited by visibleCount, but when searching show all results)
  const visibleIcons = React.useMemo(() => {
    // If searching, show all filtered results immediately
    if (search && search.trim() !== '') {
      return filteredIcons
    }
    // Otherwise, use pagination
    return filteredIcons.slice(0, visibleCount)
  }, [filteredIcons, visibleCount, search])

  // Load more icons function
  const loadMoreIcons = React.useCallback(() => {
    if (isLoading || visibleCount >= filteredIcons.length) return
    
    setIsLoading(true)
    // Simulate loading delay for better UX
    setTimeout(() => {
      setVisibleCount(prev => Math.min(prev + LOAD_MORE_SIZE, filteredIcons.length))
      setIsLoading(false)
    }, 100)
  }, [isLoading, visibleCount, filteredIcons.length])

  // Handle scroll event for infinite loading
  const handleScroll = React.useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
    const threshold = 100 // Load more when 100px from bottom
    
    if (scrollHeight - scrollTop - clientHeight < threshold) {
      loadMoreIcons()
    }
  }, [loadMoreIcons])

  // Reset visible count when search changes
  React.useEffect(() => {
    setVisibleCount(INITIAL_LOAD)
    setIsLoading(false)
  }, [search])

  return (
    <div className={cn("py-4", className)}>
      <Input
        placeholder="Cari icon..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="mb-4"
      />
      <div className="text-sm text-gray-500 mb-2">
        Menampilkan {visibleIcons.length} dari {filteredIcons.length} icon
      </div>
      <ScrollArea 
        className="h-[400px] w-full border rounded-md p-2"
        onScrollCapture={handleScroll}
        ref={scrollAreaRef}
      >
        <div className="grid grid-cols-8 gap-x-2 gap-y-4">
          {visibleIcons.map((iconName) => (
            <div 
              key={iconName} 
              className={`flex flex-col items-center gap-2 p-2 border rounded-lg cursor-pointer hover:scale-105 transition-transform ${
                value === iconName 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => {
                onChange?.(iconName)
              }}
              title={iconName}
            >
              <Icon name={iconName} className="h-6 w-6" />
              <span className="text-xs text-gray-600 dark:text-gray-400 text-center truncate w-full">
                {iconName}
              </span>
            </div>
          ))}
          
          {/* Loading skeleton - only show when not searching */}
           {!search && isLoading && Array.from({ length: 24 }).map((_, index) => (
             <div key={`skeleton-${index}`} className="flex flex-col items-center gap-2 p-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
               <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
               <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
             </div>
           ))}
           
           {/* Load more indicator - only show when not searching */}
           {!search && !isLoading && visibleCount < filteredIcons.length && (
             <div className="col-span-8 flex justify-center py-4">
               <Button
                 variant="ghost"
                 onClick={loadMoreIcons}
                 className="text-sm text-gray-500 hover:text-gray-700"
               >
                 Muat lebih banyak... ({filteredIcons.length - visibleCount} tersisa)
               </Button>
             </div>
           )}
           
           {/* End indicator - only show when not searching */}
           {!search && visibleCount >= filteredIcons.length && filteredIcons.length > INITIAL_LOAD && (
             <div className="col-span-8 flex justify-center py-4">
               <div className="text-sm text-gray-400">
                 Semua icon telah dimuat
               </div>
             </div>
           )}
           
           {/* Search results indicator */}
           {search && filteredIcons.length === 0 && (
             <div className="col-span-8 flex justify-center py-8">
               <div className="text-sm text-gray-400">
                 Tidak ada icon yang ditemukan untuk "{search}"
               </div>
             </div>
           )}
        </div>
      </ScrollArea>
    </div>
  )
}

// Original IconPicker with dialog wrapper (for backward compatibility)
export function IconPicker({ value, onChange, trigger, className }: IconPickerProps) {
  const [open, setOpen] = React.useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            variant="outline"
            className={cn("w-full justify-start", className)}
          >
            {value ? (
              <>
                <Icon name={value} className="mr-2 h-4 w-4" />
                {value}
              </>
            ) : (
              "Pilih Icon..."
            )}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Pilih Icon</DialogTitle>
        </DialogHeader>
        <IconPickerContent
          value={value}
          onChange={(icon) => {
            onChange?.(icon)
            setOpen(false)
          }}
        />
      </DialogContent>
    </Dialog>
  )
}