"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogClose } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { X, Plus, ImageIcon } from "lucide-react"
import { ImageUpload } from "@/components/forms/image-upload"

interface UploadedImage {
  id: string
  file?: File // Optional for existing images
  url: string
  name: string
  size: number
  altText?: string
  caption?: string
  isExisting?: boolean // Flag to identify existing vs new images
}

interface CreatePostDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreatePostDialog({ open, onOpenChange }: CreatePostDialogProps) {
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("# New Post\n\nStart writing your thoughts here...")
  const [type, setType] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [images, setImages] = useState<UploadedImage[]>([])
  const [activeTab, setActiveTab] = useState("content")

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove))
  }

  const handleImagesChange = (newImages: UploadedImage[]) => {
    setImages(newImages)
  }

  const handleSubmit = () => {
    // Here you would typically save the thread with images
    const threadData = {
      title,
      content,
      type,
      tags,
      images: images.map((img) => ({
        id: img.id,
        name: img.name,
        size: img.size,
        url: img.url, // In a real app, you'd upload to a server and get back URLs
      })),
    }

    console.log("Creating thread with data:", threadData)
    onOpenChange(false)

    // Reset form
    setTitle("")
    setContent("# New Thread\n\nStart writing your thoughts here...")
    setType("")
    setTags([])
    setImages([])
    setActiveTab("content")
  }

  const resetForm = () => {
    setTitle("")
    setContent("# New Thread\n\nStart writing your thoughts here...")
    setType("")
    setTags([])
    setImages([])
    setActiveTab("content")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl p-0">
        <div className="px-6 pt-6 pb-4 border-b border-gray-100 dark:border-gray-800 flex items-center justify-between">
          <DialogTitle className="flex items-center text-gray-900 dark:text-white">
            Create New Post
          </DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-900 dark:hover:text-white">
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </div>

        <div className="p-6 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-2 bg-gray-200 dark:bg-gray-800 theme-transition">
              <TabsTrigger value="content" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                Content
              </TabsTrigger>
              <TabsTrigger value="media" className="data-[state=active]:bg-green-600 data-[state=active]:text-white">
                <ImageIcon className="w-4 h-4 mr-2" />
                Media ({images.length})
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto">
              <TabsContent value="content" className="space-y-6 mt-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 theme-transition">Title</label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter post title..."
                    className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 focus:ring-inset transition-colors theme-transition"
                  />
                </div>

                {/* Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 theme-transition">Type</label>
                  <Select value={type} onValueChange={setType}>
                    <SelectTrigger className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 focus:ring-inset transition-colors theme-transition">
                      <SelectValue placeholder="Select post type" />
                    </SelectTrigger>
                    <SelectContent className="bg-white/95 dark:bg-gray-900/95 border border-gray-300 dark:border-gray-600 rounded-md shadow-xl backdrop-blur-md theme-transition">
                      <SelectItem value="learning" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        📚 Learning
                      </SelectItem>
                      <SelectItem value="error-log" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        🐛 Error Log
                      </SelectItem>
                      <SelectItem value="opinion" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        💭 Opinion
                      </SelectItem>
                      <SelectItem value="tip" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        💡 Tip
                      </SelectItem>
                      <SelectItem value="showcase" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        🎨 Showcase
                      </SelectItem>
                      <SelectItem value="photo" className="text-sm px-3 py-2.5 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 focus:bg-green-50 dark:focus:bg-green-900/20 focus:text-green-600 dark:focus:text-green-400 transition-all duration-150 cursor-pointer rounded-md mx-1">
                        📸 Photo
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 theme-transition">Tags</label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag..."
                      className="flex-1 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 focus:ring-inset transition-colors theme-transition"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddTag()
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={handleAddTag}
                      variant="outline"
                      size="sm"
                      className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-green-50 hover:border-green-500 hover:text-green-600 dark:hover:bg-green-900/20 dark:hover:border-green-500 dark:hover:text-green-400 transition-colors theme-transition"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="border-gray-300 text-gray-700 dark:border-gray-700 dark:text-gray-300 theme-transition">
                        #{tag}
                        <button onClick={() => handleRemoveTag(tag)} className="ml-1 hover:text-red-400">
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 theme-transition">Content (Markdown)</label>
                  <div className="relative">
                    <textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Write your post content in markdown..."
                      className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-3 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm font-mono leading-relaxed min-h-[280px] resize-y focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 focus:ring-inset transition-colors theme-transition"
                    />
                    <div className="absolute top-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded border border-gray-200 dark:border-gray-700 theme-transition">
                      Markdown
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="media" className="mt-6">
                <ImageUpload onImagesChange={handleImagesChange} maxImages={10} maxSizePerImage={10} />
              </TabsContent>
            </div>
          </Tabs>

          {/* Actions */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-800 theme-transition">
            <div className="text-sm text-gray-600 dark:text-gray-500 theme-transition">
              {images.length > 0 && (
                <span className="flex items-center gap-2">
                  <ImageIcon className="w-4 h-4" />
                  {images.length} image{images.length !== 1 ? "s" : ""} attached
                </span>
              )}
            </div>
            <div className="flex gap-3">
              <DialogClose asChild>
                <Button variant="outline">
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleSubmit}
                className="bg-green-600 hover:bg-green-700 text-white"
                disabled={!title.trim() || !type}
              >
                Create Post
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
