-- Create post_likes table for tracking likes
CREATE TABLE IF NOT EXISTS post_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL, -- For anonymous users (IP + User Agent hash)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Index for performance
    INDEX idx_post_likes_post_id (post_id),
    INDEX idx_post_likes_user_id (user_id),
    INDEX idx_post_likes_identifier (user_identifier),
    INDEX idx_post_likes_post_user (post_id, user_identifier)
);

-- Create post_views table for tracking views (1x per 24h)
CREATE TABLE IF NOT EXISTS post_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL, -- For anonymous users (IP + User Agent hash)
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate views within 24h
    UNIQUE(post_id, user_identifier, DATE(viewed_at)),
    
    -- Index for performance
    INDEX idx_post_views_post_id (post_id),
    INDEX idx_post_views_user_id (user_id),
    INDEX idx_post_views_identifier (user_identifier),
    INDEX idx_post_views_date (viewed_at)
);

-- Create function to update post reaction count
CREATE OR REPLACE FUNCTION update_post_reaction_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET reactions_heart = (
        SELECT COUNT(*) 
        FROM post_likes 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to update post view count
CREATE OR REPLACE FUNCTION update_post_view_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET view_count = (
        SELECT COUNT(DISTINCT user_identifier) 
        FROM post_views 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update reaction count
CREATE OR REPLACE FUNCTION trigger_update_reaction_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_reaction_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_reaction_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update view count
CREATE OR REPLACE FUNCTION trigger_update_view_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_view_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_view_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Attach triggers
DROP TRIGGER IF EXISTS trigger_post_likes_count ON post_likes;
CREATE TRIGGER trigger_post_likes_count
    AFTER INSERT OR DELETE ON post_likes
    FOR EACH ROW EXECUTE FUNCTION trigger_update_reaction_count();

DROP TRIGGER IF EXISTS trigger_post_views_count ON post_views;
CREATE TRIGGER trigger_post_views_count
    AFTER INSERT OR DELETE ON post_views
    FOR EACH ROW EXECUTE FUNCTION trigger_update_view_count();

-- Add RLS (Row Level Security) policies
ALTER TABLE post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_views ENABLE ROW LEVEL SECURITY;

-- Allow all users to read likes and views
CREATE POLICY "Allow read access to post_likes" ON post_likes
    FOR SELECT USING (true);

CREATE POLICY "Allow read access to post_views" ON post_views
    FOR SELECT USING (true);

-- Allow all users to insert likes and views
CREATE POLICY "Allow insert access to post_likes" ON post_likes
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow insert access to post_views" ON post_views
    FOR INSERT WITH CHECK (true);

-- Allow users to delete their own likes
CREATE POLICY "Allow delete own post_likes" ON post_likes
    FOR DELETE USING (
        user_identifier = (
            SELECT COALESCE(
                auth.uid()::text,
                current_setting('request.headers')::json->>'x-user-identifier'
            )
        )
    );

-- Comments for documentation
COMMENT ON TABLE post_likes IS 'Tracks user likes on posts - allows multiple likes per user';
COMMENT ON TABLE post_views IS 'Tracks post views - limited to 1 per user per 24 hours';
COMMENT ON COLUMN post_likes.user_identifier IS 'Hash of IP + User Agent for anonymous users, or user_id for authenticated users';
COMMENT ON COLUMN post_views.user_identifier IS 'Hash of IP + User Agent for anonymous users, or user_id for authenticated users';
