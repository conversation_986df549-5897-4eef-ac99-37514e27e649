import { PortfolioProject } from '@/types'

// English Portfolio Data
export const portfolioDataEn: PortfolioProject[] = [
  {
    id: "1",
    title: "E-commerce Platform",
    description: "A comprehensive e-commerce solution built with React and Node.js, featuring real-time inventory management, payment processing, and advanced analytics.",
    longDescription: "This full-stack e-commerce platform was designed to handle high-traffic scenarios with a focus on performance and user experience. The platform includes features like real-time inventory tracking, multiple payment gateways, order management, customer analytics, and a comprehensive admin dashboard.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "Node.js", "MongoDB", "Stripe", "Redis", "AWS"],
    category: "Full Stack",
    status: "Live",
    year: "2023",
    duration: "6 months",
    team: "4 developers",
    role: "Lead Developer",
    links: {
      live: "https://ecommerce-demo.com",
      github: "https://github.com/rijaldev/ecommerce",
      case_study: "/case-studies/ecommerce",
    },
    metrics: {
      users: "50K+",
      transactions: "$2M+",
      uptime: "99.9%",
      performance: "95/100",
    },
    features: [
      "Real-time inventory management",
      "Multi-vendor marketplace",
      "Advanced search and filtering",
      "Payment gateway integration",
      "Order tracking system",
      "Analytics dashboard",
    ],
    challenges: [
      "Handling high concurrent users during flash sales",
      "Implementing real-time inventory updates",
      "Optimizing database queries for complex searches",
    ],
    solutions: [
      "Implemented Redis caching for session management",
      "Used WebSocket for real-time inventory updates",
      "Optimized database with proper indexing and query optimization",
    ],
  },
  {
    id: "2",
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, team collaboration features, and advanced project tracking capabilities.",
    longDescription: "Built for teams who need efficient project management, this application provides real-time collaboration, drag-and-drop task management, time tracking, and comprehensive reporting. The app supports multiple project views and integrates with popular productivity tools.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Vue.js", "Express.js", "PostgreSQL", "Socket.io", "Docker"],
    category: "Web App",
    status: "Live",
    year: "2023",
    duration: "4 months",
    team: "3 developers",
    role: "Full Stack Developer",
    links: {
      live: "https://taskapp-demo.com",
      github: "https://github.com/rijaldev/taskapp",
      case_study: "/case-studies/taskapp",
    },
    metrics: {
      users: "10K+",
      projects: "25K+",
      uptime: "99.8%",
      performance: "92/100",
    },
    features: [
      "Real-time collaboration",
      "Drag-and-drop task management",
      "Time tracking and reporting",
      "Team chat integration",
      "File sharing and comments",
      "Mobile responsive design",
    ],
    challenges: [
      "Implementing real-time collaboration without conflicts",
      "Optimizing performance for large project datasets",
      "Creating intuitive drag-and-drop interfaces",
    ],
    solutions: [
      "Implemented operational transformation for conflict resolution",
      "Used virtual scrolling for large lists",
      "Built custom drag-and-drop components with accessibility",
    ],
  },
  {
    id: "3",
    title: "Weather Analytics Dashboard",
    description: "An interactive weather data visualization platform that provides detailed analytics, forecasting, and historical weather data analysis.",
    longDescription: "This data visualization platform aggregates weather data from multiple sources to provide comprehensive analytics and forecasting. Features include interactive maps, detailed charts, historical data analysis, and predictive modeling for weather patterns.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "D3.js", "Python", "FastAPI", "PostgreSQL", "Redis"],
    category: "Data Visualization",
    status: "Live",
    year: "2022",
    duration: "5 months",
    team: "2 developers",
    role: "Frontend Lead",
    links: {
      live: "https://weather-analytics.com",
      github: "https://github.com/rijaldev/weather",
      case_study: "/case-studies/weather",
    },
    metrics: {
      users: "15K+",
      api_calls: "1M+/month",
      accuracy: "95%",
      performance: "98/100",
    },
    features: [
      "Interactive weather maps",
      "7-day detailed forecasts",
      "Historical data analysis",
      "Weather alerts and notifications",
      "Location-based recommendations",
      "Data export capabilities",
    ],
    challenges: [
      "Processing large amounts of weather data efficiently",
      "Creating smooth animations for data transitions",
      "Handling multiple API rate limits",
    ],
    solutions: [
      "Implemented data caching and pagination strategies",
      "Used requestAnimationFrame for smooth animations",
      "Built API request queue with intelligent rate limiting",
    ],
  },
  {
    id: "4",
    title: "Social Media Analytics",
    description: "A comprehensive social media analytics platform that tracks engagement, analyzes trends, and provides actionable insights for content creators.",
    longDescription: "This analytics platform helps content creators and businesses understand their social media performance across multiple platforms. It provides detailed engagement metrics, trend analysis, competitor insights, and automated reporting features.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Next.js", "Python", "Django", "PostgreSQL", "Celery", "Redis"],
    category: "Analytics",
    status: "Completed",
    year: "2022",
    duration: "8 months",
    team: "5 developers",
    role: "Backend Developer",
    links: {
      live: "https://social-analytics.com",
      github: "https://github.com/rijaldev/social-analytics",
      case_study: "/case-studies/social-analytics",
    },
    metrics: {
      accounts: "5K+",
      data_points: "10M+",
      reports: "50K+",
      accuracy: "97%",
    },
    features: [
      "Multi-platform data aggregation",
      "Real-time engagement tracking",
      "Automated report generation",
      "Competitor analysis",
      "Content performance insights",
      "Custom dashboard creation",
    ],
    challenges: [
      "Handling rate limits across multiple social media APIs",
      "Processing and analyzing large datasets efficiently",
      "Creating meaningful insights from complex data",
    ],
    solutions: [
      "Built robust API management system with retry logic",
      "Implemented distributed data processing with Celery",
      "Used machine learning for trend detection and insights",
    ],
  },
]

// Indonesian Portfolio Data
export const portfolioDataId: PortfolioProject[] = [
  {
    id: "1",
    title: "Platform E-commerce",
    description: "Solusi e-commerce komprehensif yang dibangun dengan React dan Node.js, menampilkan manajemen inventori real-time, pemrosesan pembayaran, dan analitik canggih.",
    longDescription: "Platform e-commerce full-stack ini dirancang untuk menangani skenario lalu lintas tinggi dengan fokus pada performa dan pengalaman pengguna. Platform ini mencakup fitur seperti pelacakan inventori real-time, multiple payment gateway, manajemen pesanan, analitik pelanggan, dan dashboard admin yang komprehensif.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "Node.js", "MongoDB", "Stripe", "Redis", "AWS"],
    category: "Full Stack",
    status: "Live",
    year: "2023",
    duration: "6 bulan",
    team: "4 developer",
    role: "Lead Developer",
    links: {
      live: "https://ecommerce-demo.com",
      github: "https://github.com/rijaldev/ecommerce",
      case_study: "/case-studies/ecommerce",
    },
    metrics: {
      users: "50K+",
      transactions: "$2M+",
      uptime: "99.9%",
      performance: "95/100",
    },
    features: [
      "Manajemen inventori real-time",
      "Marketplace multi-vendor",
      "Pencarian dan filter canggih",
      "Integrasi payment gateway",
      "Sistem pelacakan pesanan",
      "Dashboard analitik",
    ],
    challenges: [
      "Menangani pengguna bersamaan tinggi saat flash sale",
      "Mengimplementasikan update inventori real-time",
      "Mengoptimalkan query database untuk pencarian kompleks",
    ],
    solutions: [
      "Mengimplementasikan Redis caching untuk manajemen sesi",
      "Menggunakan WebSocket untuk update inventori real-time",
      "Mengoptimalkan database dengan indexing dan optimisasi query yang tepat",
    ],
  },
  {
    id: "2",
    title: "Aplikasi Manajemen Tugas",
    description: "Aplikasi manajemen tugas kolaboratif dengan update real-time, fitur kolaborasi tim, dan kemampuan pelacakan proyek canggih.",
    longDescription: "Dibangun untuk tim yang membutuhkan manajemen proyek yang efisien, aplikasi ini menyediakan kolaborasi real-time, manajemen tugas drag-and-drop, pelacakan waktu, dan pelaporan komprehensif. Aplikasi mendukung multiple tampilan proyek dan terintegrasi dengan tools produktivitas populer.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Vue.js", "Express.js", "PostgreSQL", "Socket.io", "Docker"],
    category: "Web App",
    status: "Live",
    year: "2023",
    duration: "4 bulan",
    team: "3 developer",
    role: "Full Stack Developer",
    links: {
      live: "https://taskapp-demo.com",
      github: "https://github.com/rijaldev/taskapp",
      case_study: "/case-studies/taskapp",
    },
    metrics: {
      users: "10K+",
      projects: "25K+",
      uptime: "99.8%",
      performance: "92/100",
    },
    features: [
      "Kolaborasi real-time",
      "Manajemen tugas drag-and-drop",
      "Pelacakan waktu dan pelaporan",
      "Integrasi chat tim",
      "Berbagi file dan komentar",
      "Desain responsif mobile",
    ],
    challenges: [
      "Mengimplementasikan kolaborasi real-time tanpa konflik",
      "Mengoptimalkan performa untuk dataset proyek besar",
      "Membuat interface drag-and-drop yang intuitif",
    ],
    solutions: [
      "Mengimplementasikan operational transformation untuk resolusi konflik",
      "Menggunakan virtual scrolling untuk list besar",
      "Membangun komponen drag-and-drop kustom dengan aksesibilitas",
    ],
  },
  {
    id: "3",
    title: "Dashboard Analitik Cuaca",
    description: "Platform visualisasi data cuaca interaktif yang menyediakan analitik detail, peramalan, dan analisis data cuaca historis.",
    longDescription: "Platform visualisasi data ini mengagregasi data cuaca dari multiple sumber untuk menyediakan analitik dan peramalan komprehensif. Fitur termasuk peta interaktif, chart detail, analisis data historis, dan pemodelan prediktif untuk pola cuaca.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "D3.js", "Python", "FastAPI", "PostgreSQL", "Redis"],
    category: "Data Visualization",
    status: "Live",
    year: "2022",
    duration: "5 bulan",
    team: "2 developer",
    role: "Frontend Lead",
    links: {
      live: "https://weather-analytics.com",
      github: "https://github.com/rijaldev/weather",
      case_study: "/case-studies/weather",
    },
    metrics: {
      users: "15K+",
      api_calls: "1M+/bulan",
      accuracy: "95%",
      performance: "98/100",
    },
    features: [
      "Peta cuaca interaktif",
      "Ramalan detail 7 hari",
      "Analisis data historis",
      "Peringatan dan notifikasi cuaca",
      "Rekomendasi berbasis lokasi",
      "Kemampuan ekspor data",
    ],
    challenges: [
      "Memproses data cuaca dalam jumlah besar secara efisien",
      "Membuat animasi halus untuk transisi data",
      "Menangani multiple rate limit API",
    ],
    solutions: [
      "Mengimplementasikan strategi caching dan pagination data",
      "Menggunakan requestAnimationFrame untuk animasi halus",
      "Membangun queue request API dengan rate limiting cerdas",
    ],
  },
  {
    id: "4",
    title: "Analitik Media Sosial",
    description: "Platform analitik media sosial komprehensif yang melacak engagement, menganalisis tren, dan menyediakan insight actionable untuk content creator.",
    longDescription: "Platform analitik ini membantu content creator dan bisnis memahami performa media sosial mereka di multiple platform. Menyediakan metrik engagement detail, analisis tren, insight kompetitor, dan fitur pelaporan otomatis.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Next.js", "Python", "Django", "PostgreSQL", "Celery", "Redis"],
    category: "Analytics",
    status: "Completed",
    year: "2022",
    duration: "8 bulan",
    team: "5 developer",
    role: "Backend Developer",
    links: {
      live: "https://social-analytics.com",
      github: "https://github.com/rijaldev/social-analytics",
      case_study: "/case-studies/social-analytics",
    },
    metrics: {
      accounts: "5K+",
      data_points: "10M+",
      reports: "50K+",
      accuracy: "97%",
    },
    features: [
      "Agregasi data multi-platform",
      "Pelacakan engagement real-time",
      "Generasi laporan otomatis",
      "Analisis kompetitor",
      "Insight performa konten",
      "Pembuatan dashboard kustom",
    ],
    challenges: [
      "Menangani rate limit di multiple API media sosial",
      "Memproses dan menganalisis dataset besar secara efisien",
      "Membuat insight bermakna dari data kompleks",
    ],
    solutions: [
      "Membangun sistem manajemen API robust dengan retry logic",
      "Mengimplementasikan pemrosesan data terdistribusi dengan Celery",
      "Menggunakan machine learning untuk deteksi tren dan insight",
    ],
  },
]

// Function to get portfolio data based on locale
export function getPortfolioData(locale: 'en' | 'id'): PortfolioProject[] {
  return locale === 'id' ? portfolioDataId : portfolioDataEn
}
