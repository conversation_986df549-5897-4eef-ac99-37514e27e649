"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { PostSlideshow } from "@/components/gallery/post-slideshow"
import {
  Calendar,
  Eye,
  GitBranch,
  User,
  Tag
} from "lucide-react"
import { Post } from "@/types"
import { cn } from "@/lib/utils"
import { getPostTypeIcon, getIconByName, getPostTypeClasses } from "@/lib/constants"
interface EnhancedPostContentProps {
  post: Post
  viewCount?: number
  className?: string
  locale?: string
  translations?: {
    publishedOn: string
    sharePost: string
    views: string
  }
}

export function EnhancedPostContent({
  post,
  viewCount = post.viewCount || 0,
  className,
  locale,
  translations
}: EnhancedPostContentProps) {
  const IconComponent = getIconByName(post.type.icon) || getPostTypeIcon(post.type.slug)
  const typeStyles = getPostTypeClasses(post.type.color)

  return (
    <article className={cn("max-w-4xl mx-auto", className)}>
      {/* Hero Section */}
      <div className="mb-12">
        {/* Post Type Badge */}
        <div className="flex items-center gap-2 mb-6">
          <Badge
            variant="outline"
            className="px-3 py-1 text-sm font-medium border-2 transition-all duration-300"
            style={{
              borderColor: typeStyles.borderColor,
              color: typeStyles.color,
              background: typeStyles.background,
            }}
          >
            <IconComponent className="w-4 h-4 mr-2" />
            {post.type.name}
          </Badge>
        </div>

        {/* Title */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white leading-tight mb-8 theme-transition">
          {post.title}
        </h1>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-gray-600 dark:text-gray-400 text-sm mb-8">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4" />
            <span className="font-medium">{post.author?.name || post.author}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>{new Date(post.updatedAt).toISOString().split('T')[0]}</span>
          </div>

          <div className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            <span>{viewCount} {translations?.views || 'views'}</span>
          </div>
          <div className="flex items-center gap-2">
            <GitBranch className="w-4 h-4" />
            <span>v{post.version}</span>
          </div>
        </div>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-8">
            {post.tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Featured Image/Gallery */}
      {post.images && post.images.length > 0 && (
        <div className="mb-12 rounded-2xl overflow-hidden shadow-2xl">
          <PostSlideshow
            images={post.images}
            autoPlay={false}
            showThumbnails={post.images.length > 1}
            className="aspect-video"
          />
        </div>
      )}

      {/* Main Content */}
      <div className="prose prose-lg prose-gray dark:prose-invert max-w-none">
        <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 md:p-12 shadow-sm border border-gray-200 dark:border-gray-800 theme-transition">
          <div className="text-gray-800 dark:text-gray-200 leading-relaxed text-lg">
            {post.content.split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-6 last:mb-0">
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      </div>

      {/* Article Footer */}
      <div className="mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* Author Info */}
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
              {(post.author?.name || post.author || 'A').charAt(0)}
            </div>
            <div>
              <p className="font-semibold text-gray-900 dark:text-white">{post.author?.name || post.author}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {translations?.publishedOn || 'Published on'} {new Date(post.updatedAt).toISOString().split('T')[0]}
              </p>
            </div>
          </div>

          {/* Share Actions */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">{translations?.sharePost || 'Share this article'}</span>
            {/* Share buttons can be added here */}
          </div>
        </div>
      </div>
    </article>
  )
}
