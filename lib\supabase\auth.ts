import { createServerSupabaseAdminClient, createServerSupabaseClient } from './server'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'

export interface SupabaseUser {
  id: string
  email: string
  name: string
  createdAt: string
  updatedAt: string
}

/**
 * Get authenticated user from Supabase session
 */
export async function getAuthenticatedUser(): Promise<SupabaseUser | null> {
  try {
    const supabase = createServerSupabaseClient()

    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession()

    if (sessionError || !session?.user) {
      return null
    }

    // Get user profile from our user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', session.user.id)
      .single()

    if (profileError || !profile) {
      // If no profile exists, create one
      const { data: newProfile, error: createError } = await supabase
        .from('user_profiles')
        .insert({
          id: session.user.id,
          name: session.user.user_metadata?.name || session.user.email?.split('@')[0] || 'User',
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating user profile:', createError)
        return null
      }

      return {
        id: session.user.id,
        email: session.user.email!,
        name: newProfile.name,
        createdAt: newProfile.created_at,
        updatedAt: newProfile.updated_at,
      }
    }

    return {
      id: session.user.id,
      email: session.user.email!,
      name: profile.name,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at,
    }
  } catch (error) {
    console.error('Error getting authenticated user:', error)
    return null
  }
}

/**
 * Get authenticated user from request (for API routes)
 * Uses cookies for authentication (dashboard) or Bearer token (external API)
 */
export async function getAuthenticatedUserFromRequest(request: NextRequest): Promise<SupabaseUser | null> {
  try {
    // Try cookie-based authentication first (for dashboard)
    const cookieAuth = await getAuthenticatedUserFromCookies(request)
    if (cookieAuth) {
      return cookieAuth
    }

    // Fallback to Bearer token authentication (for external API)
    const supabase = createServerSupabaseClient()

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return null
    }

    const token = authHeader.substring(7)

    // Verify the JWT token with Supabase
    const {
      data: { user },
      error
    } = await supabase.auth.getUser(token)

    if (error || !user) {
      return null
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return null
    }

    return {
      id: user.id,
      email: user.email!,
      name: profile.name,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at,
    }
  } catch (error) {
    console.error('Error getting authenticated user from request:', error)
    return null
  }
}

/**
 * Get authenticated user from cookies (for dashboard API routes)
 */
export async function getAuthenticatedUserFromCookies(request: NextRequest): Promise<SupabaseUser | null> {
  try {
    // Create Supabase client with cookies from request
    const supabase = createServerSupabaseClient()

    // Get session from cookies
    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession()

    if (sessionError || !session?.user) {
      return null
    }

    // Get user profile from our user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', session.user.id)
      .single()

    if (profileError || !profile) {
      // If no profile exists, create one
      const { data: newProfile, error: createError } = await supabase
        .from('user_profiles')
        .insert({
          id: session.user.id,
          name: session.user.user_metadata?.name || session.user.email?.split('@')[0] || 'User',
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating user profile:', createError)
        return null
      }

      return {
        id: session.user.id,
        email: session.user.email!,
        name: newProfile.name,
        createdAt: newProfile.created_at,
        updatedAt: newProfile.updated_at,
      }
    }

    return {
      id: session.user.id,
      email: session.user.email!,
      name: profile.name,
      createdAt: profile.created_at,
      updatedAt: profile.updated_at,
    }
  } catch (error) {
    console.error('Error getting authenticated user from cookies:', error)
    return null
  }
}

/**
 * Sign in user with email and password
 */
export async function signInWithEmailPassword(email: string, password: string) {
  try {
    const supabase = createServerSupabaseClient()

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      return { success: false, error: error.message }
    }

    if (!data.user || !data.session) {
      return { success: false, error: 'Authentication failed' }
    }

    return {
      success: true,
      user: data.user,
      session: data.session,
    }
  } catch (error) {
    console.error('Error signing in:', error)
    return { success: false, error: 'Authentication failed' }
  }
}

/**
 * Sign up user with email and password
 */
export async function signUpWithEmailPassword(email: string, password: string, name?: string) {
  try {
    const supabase = createServerSupabaseClient()

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name || email.split('@')[0],
        },
      },
    })

    if (error) {
      return { success: false, error: error.message }
    }

    if (!data.user) {
      return { success: false, error: 'Registration failed' }
    }

    return {
      success: true,
      user: data.user,
      session: data.session,
    }
  } catch (error) {
    console.error('Error signing up:', error)
    return { success: false, error: 'Registration failed' }
  }
}

/**
 * Sign out user
 */
export async function signOut() {
  try {
    const supabase = createServerSupabaseClient()

    const { error } = await supabase.auth.signOut()

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error signing out:', error)
    return { success: false, error: 'Sign out failed' }
  }
}

/**
 * Check if user is authenticated (for middleware)
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const user = await getAuthenticatedUser()
    return user !== null
  } catch (error) {
    return false
  }
}

/**
 * Create admin user (for setup)
 */
export async function createAdminUser(email: string, password: string, name: string) {
  try {
    const supabase = createServerSupabaseAdminClient()

    // Create user with admin client
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        name,
      },
      email_confirm: true, // Auto-confirm email for admin
    })

    if (error) {
      return { success: false, error: error.message }
    }

    if (!data.user) {
      return { success: false, error: 'Failed to create admin user' }
    }

    // Create user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: data.user.id,
        name,
      })

    if (profileError) {
      console.error('Error creating admin profile:', profileError)
      // Don't fail the whole operation for this
    }

    return {
      success: true,
      user: data.user,
    }
  } catch (error) {
    console.error('Error creating admin user:', error)
    return { success: false, error: 'Failed to create admin user' }
  }
}
