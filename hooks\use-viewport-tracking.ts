"use client"

import { useEffect, useRef, useCallback } from 'react'

interface UseViewportTrackingOptions {
  onEnterViewport?: () => void
  onExitViewport?: () => void
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  enabled?: boolean
}

interface UseViewportTrackingReturn {
  ref: React.RefObject<HTMLElement>
  isInViewport: boolean
}

/**
 * Hook for tracking when an element enters/exits the viewport
 * Uses Intersection Observer API for efficient viewport detection
 */
export function useViewportTracking({
  onEnterViewport,
  onExitViewport,
  threshold = 0.5, // 50% of element must be visible
  rootMargin = '0px',
  triggerOnce = true,
  enabled = true
}: UseViewportTrackingOptions = {}): UseViewportTrackingReturn {
  const elementRef = useRef<HTMLElement>(null)
  const isInViewportRef = useRef(false)
  const hasTriggeredRef = useRef(false)
  const observerRef = useRef<IntersectionObserver | null>(null)

  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries
    const isCurrentlyInViewport = entry.isIntersecting

    // Only trigger if state actually changed
    if (isCurrentlyInViewport !== isInViewportRef.current) {
      isInViewportRef.current = isCurrentlyInViewport

      if (isCurrentlyInViewport) {
        // Element entered viewport
        if (!hasTriggeredRef.current || !triggerOnce) {
          onEnterViewport?.()
          if (triggerOnce) {
            hasTriggeredRef.current = true
          }
        }
      } else {
        // Element exited viewport
        onExitViewport?.()
      }
    }
  }, [onEnterViewport, onExitViewport, triggerOnce])

  useEffect(() => {
    const element = elementRef.current
    
    if (!element || !enabled) {
      return
    }

    // Check if Intersection Observer is supported
    if (!window.IntersectionObserver) {
      console.warn('IntersectionObserver not supported, falling back to immediate trigger')
      // Fallback: trigger immediately if not supported
      onEnterViewport?.()
      return
    }

    // Create observer
    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold,
      rootMargin
    })

    // Start observing
    observerRef.current.observe(element)

    // Cleanup function
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
        observerRef.current = null
      }
    }
  }, [handleIntersection, threshold, rootMargin, enabled])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  return {
    ref: elementRef,
    isInViewport: isInViewportRef.current
  }
}
