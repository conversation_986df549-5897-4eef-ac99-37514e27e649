import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Calendar,
  User,
  Eye,
  Heart,
  MessageCircle,
  Edit,
  Trash2,
  Star
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PostItemProps {
  post: {
    id: string
    title: string
    author: string
    publishedAt?: string
    viewCount: number
    reactions: {
      heart: number
      thumbsUp: number
      brain: number
    }
    comments: number
    status: string
    featured: boolean
    type: {
      name: string
      color?: string
    }
  }
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  className?: string
}

function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case 'published':
      return 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20'
    case 'draft':
      return 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 border-yellow-500/20'
    case 'archived':
      return 'bg-gray-500/10 text-gray-600 dark:text-gray-400 border-gray-500/20'
    default:
      return 'bg-gray-500/10 text-gray-600 dark:text-gray-400 border-gray-500/20'
  }
}

function getTypeColor(type: string, color?: string) {
  if (color) {
    return `bg-[${color}]/10 text-[${color}] border-[${color}]/20`
  }

  switch (type.toLowerCase()) {
    case 'learning':
      return 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/20'
    case 'error log':
      return 'bg-red-500/10 text-red-600 dark:text-red-400 border-red-500/20'
    case 'opinion':
      return 'bg-purple-500/10 text-purple-600 dark:text-purple-400 border-purple-500/20'
    case 'tip':
      return 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20'
    default:
      return 'bg-gray-500/10 text-gray-600 dark:text-gray-400 border-gray-500/20'
  }
}

export function PostItem({
  post,
  onEdit,
  onDelete,
  className
}: PostItemProps) {
  const totalReactions = post.reactions?.heart || 0

  return (
    <div className={cn(
      "flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 border border-gray-200 dark:border-gray-600 group",
      className
    )}>
      <div className="flex-1 min-w-0">
        {/* Title and Featured Badge */}
        <div className="flex items-center space-x-3 mb-3">
          <h3 className="text-gray-900 dark:text-white font-medium truncate group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
            {post.title}
          </h3>
          {post.featured && (
            <Badge variant="outline" className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 border-yellow-300 dark:border-yellow-600 text-xs flex items-center space-x-1">
              <Star className="h-3 w-3" />
              <span>Featured</span>
            </Badge>
          )}
        </div>

        {/* Meta Information */}
        <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-1">
            <User className="h-3 w-3" />
            <span>{post.author}</span>
          </div>
          {post.publishedAt && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
            </div>
          )}
          <div className="flex items-center space-x-1">
            <Eye className="h-3 w-3" />
            <span>{post.viewCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Heart className="h-3 w-3" />
            <span>{totalReactions}</span>
          </div>
          <div className="flex items-center space-x-1">
            <MessageCircle className="h-3 w-3" />
            <span>{post.comments || 0}</span>
          </div>
        </div>
      </div>

      {/* Status, Type, and Actions */}
      <div className="flex items-center space-x-3 ml-4">
        <Badge variant="outline" className={getTypeColor(post.type.name, post.type.color)}>
          {post.type.name}
        </Badge>
        <Badge variant="outline" className={getStatusColor(post.status)}>
          {post.status}
        </Badge>

        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(post.id)}
              className="text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(post.id)}
              className="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
