import { Post } from '../types'

// Temporary static data with string types for seeding
export const postsData: any[] = [
  {
    id: "1",
    title: "Building a Real-time Chat App with Next.js and Socket.io",
    slug: "building-a-real-time-chat-app-with-nextjs-and-socketio",
    content:
      "# Real-time Chat Application\n\nToday I built a real-time chat application using Next.js and Socket.io. Here's what I learned about implementing WebSocket connections, handling real-time events, and optimizing performance for concurrent users...",
    version: "v2.1",
    type: "learning",
    author: "<PERSON>",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    reactions: { thumbsUp: 24, heart: 18, brain: 12 },
    comments: 8,
    tags: ["nextjs", "socketio", "realtime", "chat"],
    featured: true,
    readTime: "8 min read",
    images: [
      {
        id: "img1",
        url: "/placeholder.svg?height=400&width=600",
        alt: "Chat application interface",
        caption: "The main chat interface with real-time messaging",
      },
      {
        id: "img2",
        url: "/placeholder.svg?height=400&width=600",
        alt: "Socket.io connection diagram",
        caption: "WebSocket connection architecture",
      },
      {
        id: "img3",
        url: "/placeholder.svg?height=400&width=600",
        alt: "Performance metrics",
        caption: "Real-time performance monitoring dashboard",
      },
    ],
  },
  {
    id: "2",
    title: "My Photography Journey: Capturing Urban Landscapes",
    slug: "my-photography-journey-capturing-urban-landscapes",
    content:
      "# Urban Photography Adventures\n\nSpent the weekend exploring the city with my camera, capturing the interplay between architecture and natural light. Here are some of my favorite shots and the stories behind them...",
    version: "v1.0",
    type: "photo",
    author: "Alex Thompson",
    createdAt: "2024-01-18",
    updatedAt: "2024-01-18",
    reactions: { thumbsUp: 45, heart: 62, brain: 8 },
    comments: 15,
    tags: ["photography", "urban", "architecture", "art"],
    featured: true,
    readTime: "5 min read",
    images: [
      {
        id: "img4",
        url: "/placeholder.svg?height=600&width=800",
        alt: "Urban landscape photo",
        caption: "Golden hour reflections on glass buildings",
      },
      {
        id: "img5",
        url: "/placeholder.svg?height=600&width=800",
        alt: "Street photography",
        caption: "Street life in the downtown area",
      },
      {
        id: "img6",
        url: "/placeholder.svg?height=600&width=800",
        alt: "Architectural detail",
        caption: "Geometric patterns in modern architecture",
      },
      {
        id: "img7",
        url: "/placeholder.svg?height=600&width=800",
        alt: "City skyline",
        caption: "City skyline during blue hour",
      },
      {
        id: "img8",
        url: "/placeholder.svg?height=600&width=800",
        alt: "Urban reflections",
        caption: "Reflections in modern glass facades",
      },
    ],
  },
  {
    id: "3",
    title: "Common TypeScript Errors and How to Fix Them",
    slug: "common-typescript-errors-and-how-to-fix-them",
    content:
      "# TypeScript Error Solutions\n\nA comprehensive guide to the most common TypeScript errors developers encounter and practical solutions to resolve them quickly...",
    version: "v1.0",
    type: "error-log",
    author: "Alex Thompson",
    createdAt: "2024-01-14",
    updatedAt: "2024-01-14",
    reactions: { thumbsUp: 16, heart: 8, brain: 5 },
    comments: 4,
    tags: ["typescript", "error", "debugging"],
    featured: true,
    readTime: "5 min read",
  },
  {
    id: "4",
    title: "My Journey from Junior to Senior Developer",
    slug: "my-journey-from-junior-to-senior-developer",
    content:
      "# Career Growth Reflections\n\nAfter 5 years in the industry, here are the key lessons I've learned about growing as a developer, building technical leadership skills, and navigating career advancement...",
    version: "v1.2",
    type: "opinion",
    author: "Alex Thompson",
    createdAt: "2024-01-12",
    updatedAt: "2024-01-18",
    reactions: { thumbsUp: 45, heart: 32, brain: 28 },
    comments: 15,
    tags: ["career", "growth", "advice"],
    featured: false,
    readTime: "12 min read",
  },
]
