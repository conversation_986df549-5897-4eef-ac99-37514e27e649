import { ExperiencePageClient } from "@/components/experience/experience-page-client"
import { getMessages, createTranslator, type Locale } from '@/lib/i18n'
import { getProfileData } from '@/lib/data/profile-bilingual'
import { Metadata } from 'next'

interface ExperiencePageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata({ params }: ExperiencePageProps): Promise<Metadata> {
  const { locale } = await params
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)

  return {
    title: `${t('navigation.experience')} | ${t('meta.title')}`,
    description: locale === 'id' 
      ? 'Perjalanan karir profesional dan pencapaian dalam pengembangan software.'
      : 'Professional career journey and achievements in software development.',
    keywords: locale === 'id'
      ? 'pengal<PERSON> kerja, karir, software engineer, full-stack developer, pencapaian'
      : 'work experience, career, software engineer, full-stack developer, achievements',
  }
}

export default async function ExperiencePage({ params }: ExperiencePageProps) {
  const { locale } = await params
  
  // Get bilingual profile data (which includes experience)
  const profile = getProfileData(locale as 'en' | 'id')

  // Get translations
  const messages = await getMessages(locale as Locale)
  const t = createTranslator(messages)
  
  const translations = {
    navigation: {
      backToHome: t('common.backToHome'),
      experience: t('navigation.experience')
    },
    header: {
      title: t('experience.title'),
      subtitle: t('experience.subtitle'),
      description: t('experience.description')
    },
    overview: {
      professionalJourney: t('experience.overview.professionalJourney'),
      experienceOverview: t('experience.overview.title'),
      description: t('experience.overview.description')
    },
    stats: {
      yearsExperience: t('experience.stats.yearsExperience'),
      projectsDelivered: t('experience.stats.projectsDelivered'),
      companies: t('experience.stats.companies'),
      roles: t('experience.stats.roles')
    },
    experience: {
      professionalExperience: t('experience.section.title'),
      detailedJourney: t('experience.section.description'),
      duration: t('experience.details.duration'),
      teamSize: t('experience.details.teamSize'),
      projects: t('experience.details.projects'),
      delivered: t('experience.details.delivered'),
      keyHighlights: t('experience.details.keyHighlights'),
      coreTechnologies: t('experience.details.coreTechnologies'),
      showDetails: t('experience.details.showDetails'),
      hideDetails: t('experience.details.hideDetails'),
      keyAchievements: t('experience.details.keyAchievements'),
      notableProjects: t('experience.details.notableProjects'),
      technologiesUsed: t('experience.details.technologiesUsed'),
      members: t('experience.details.members'),
      now: t('experience.details.now')
    },
    skills: {
      title: t('experience.skills.title'),
      description: t('experience.skills.description'),
      frontend: t('experience.skills.frontend'),
      backend: t('experience.skills.backend'),
      devops: t('experience.skills.devops'),
      tools: t('experience.skills.tools')
    }
  }

  return (
    <ExperiencePageClient
      profile={profile}
      locale={locale}
      translations={translations}
    />
  )
}
