import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Common button style variants for consistent theming
 */
export const buttonVariants = {
  reaction: {
    thumbsUp: "border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 hover:border-blue-500/50 dark:hover:border-blue-500/70 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 theme-transition",
    heart: "border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 hover:border-red-500/50 dark:hover:border-red-500/70 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 theme-transition",
    brain: "border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 hover:border-purple-500/50 dark:hover:border-purple-500/70 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 theme-transition"
  },
  action: {
    primary: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white transition-all duration-300",
    secondary: "border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:border-green-500/50 dark:hover:border-green-500/70 hover:bg-green-50 dark:hover:bg-green-900/20 bg-white/50 dark:bg-gray-800/50 transition-all duration-300 theme-transition",
    ghost: "text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 theme-transition"
  }
}

/**
 * Common card styles for consistent theming
 */
export const cardStyles = {
  base: "bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition",
  hover: "hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20",
  interactive: "group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20 theme-transition"
}

/**
 * Common input styles for consistent theming
 */
export const inputStyles = {
  base: "bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 focus:ring-inset transition-colors theme-transition",
  textarea: "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 theme-transition",
  codeEditor: "font-mono leading-relaxed min-h-[280px] resize-y"
}

/**
 * Common text styles for consistent theming
 */
export const textStyles = {
  heading: {
    h1: "text-3xl md:text-4xl font-bold text-gray-900 dark:text-white leading-tight theme-transition",
    h2: "text-2xl font-bold text-gray-900 dark:text-white theme-transition",
    h3: "text-xl font-semibold text-gray-900 dark:text-white theme-transition"
  },
  body: {
    primary: "text-gray-900 dark:text-white theme-transition",
    secondary: "text-gray-600 dark:text-gray-400 theme-transition",
    muted: "text-gray-500 dark:text-gray-500 theme-transition"
  },
  interactive: {
    link: "group-hover:text-green-500 dark:group-hover:text-green-400 transition-colors duration-200",
    hover: "hover:text-green-500 dark:hover:text-green-400"
  }
}

/**
 * Animation and transition utilities
 */
export const animations = {
  transition: "transition-all duration-300",
  themeTransition: "theme-transition",
  hover: "transition-colors duration-200",
  smooth: "transition-all duration-300 ease-in-out"
}

/**
 * Layout utilities
 */
export const layouts = {
  container: "container mx-auto px-4",
  centerContent: "flex items-center justify-center",
  spaceBetween: "flex items-center justify-between",
  flexCol: "flex flex-col",
  grid: {
    responsive: "grid lg:grid-cols-4 gap-8",
    cards: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
  }
}

/**
 * Responsive utilities
 */
export const responsive = {
  hideOnMobile: "hidden md:block",
  showOnMobile: "block md:hidden",
  mobileFirst: "w-full md:w-auto",
  textResponsive: "text-responsive"
}
