"use client"

import { useState, useCallback } from 'react'

interface UseFormOptions<T> {
  initialValues: T
  onSubmit?: (values: T) => Promise<void> | void
  validate?: (values: T) => Record<string, string>
  resetOnSubmit?: boolean
}

interface UseFormReturn<T> {
  values: T
  errors: Record<string, string>
  isSubmitting: boolean
  isDirty: boolean
  setValue: (field: keyof T, value: any) => void
  setValues: (values: Partial<T>) => void
  setError: (field: keyof T, error: string) => void
  clearError: (field: keyof T) => void
  clearErrors: () => void
  handleSubmit: () => Promise<void>
  reset: () => void
  isValid: boolean
}

/**
 * Custom hook for form state management with validation
 */
export function useForm<T extends Record<string, any>>({
  initialValues,
  onSubmit,
  validate,
  resetOnSubmit = false
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValuesState] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDirty, setIsDirty] = useState(false)

  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }))
    setIsDirty(true)
    
    // Clear error when field is updated
    if (errors[field as string]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field as string]
        return newErrors
      })
    }
  }, [errors])

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }))
    setIsDirty(true)
  }, [])

  const setError = useCallback((field: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [field as string]: error }))
  }, [])

  const clearError = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field as string]
      return newErrors
    })
  }, [])

  const clearErrors = useCallback(() => {
    setErrors({})
  }, [])

  const reset = useCallback(() => {
    setValuesState(initialValues)
    setErrors({})
    setIsDirty(false)
    setIsSubmitting(false)
  }, [initialValues])

  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return

    try {
      setIsSubmitting(true)
      clearErrors()

      // Run validation if provided
      if (validate) {
        const validationErrors = validate(values)
        if (Object.keys(validationErrors).length > 0) {
          setErrors(validationErrors)
          return
        }
      }

      // Submit form
      if (onSubmit) {
        await onSubmit(values)
      }

      // Reset form if requested
      if (resetOnSubmit) {
        reset()
      } else {
        setIsDirty(false)
      }
    } catch (error) {
      if (error instanceof Error) {
        setError('_form' as keyof T, error.message)
      }
    } finally {
      setIsSubmitting(false)
    }
  }, [values, isSubmitting, validate, onSubmit, resetOnSubmit, reset, clearErrors, setError])

  const isValid = Object.keys(errors).length === 0

  return {
    values,
    errors,
    isSubmitting,
    isDirty,
    setValue,
    setValues,
    setError,
    clearError,
    clearErrors,
    handleSubmit,
    reset,
    isValid
  }
}
