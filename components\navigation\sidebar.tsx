import { Profile, Post, PortfolioProject } from "@/types"
import { ProfileCard } from "@/components/cards/profile-card"
import { ExperienceCard } from "@/components/cards/experience-card"
import { PortfolioCard } from "@/components/cards/portfolio-card"
import { StatsCard } from "@/components/cards/stats-card"

interface SidebarProps {
  profile: Profile
  posts: Post[]
  portfolio: PortfolioProject[]
  locale: string
  translations: {
    profile: {
      title: string
      status: string
      contact: string
    }
    experience: {
      title: string
      current: string
      years: string
      viewAll: string
    }
    portfolio: {
      title: string
      projects: string
      viewAll: string
    }
    stats: {
      title: string
      posts: string
      views: string
      reactions: string
    }
  }
}

export function Sidebar({
  profile,
  posts,
  portfolio,
  locale,
  translations
}: SidebarProps) {
  return (
    <div className="lg:col-span-1">
      <div className="sticky top-24 space-y-6">
        <ProfileCard profile={profile} locale={locale} translations={translations.profile} />
        <ExperienceCard experience={profile.experience} locale={locale} translations={translations.experience} />
        <PortfolioCard portfolio={portfolio} locale={locale} translations={translations.portfolio} />
        <StatsCard posts={posts} locale={locale} translations={translations.stats} />
      </div>
    </div>
  )
}
