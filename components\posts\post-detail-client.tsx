"use client"

import { useState, useEffect } from "react"
import React from "react"
import { Post } from "@/types"
import { PostHeader } from "./post-header"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { usePostInteractionsFallback } from "@/hooks/use-post-interactions-fallback"
import { useUnifiedViewTracking } from "@/hooks/use-unified-view-tracking"
import { FloatingReactions } from "./floating-reactions"
import { EnhancedPostContent } from "./enhanced-post-content"

interface PostDetailClientProps {
  post: Post
  locale: string
  translations: {
    header: {
      backToPosts: string
      share: string
      edit: string
      copyLink: string
      report: string
    }
    content: {
      publishedOn: string
      sharePost: string
      views: string
    }
  }
}

/**
 * Enhanced post detail client component with floating reactions and improved reading experience
 */
export function PostDetailClient({ post, locale, translations }: PostDetailClientProps) {
  // Use unified view tracking (prevents duplicate views from list + detail)
  const { recordView: recordUnifiedView } = useUnifiedViewTracking({
    postId: post.id,
    threshold: 0.1, // Lower threshold for detail page
    delay: 500, // Faster recording on detail page
    enabled: true
  })

  // Use post interactions hook for likes
  const {
    likeCount,
    viewCount,
    hasLiked,
    userLikeCount,
    isLiking,
    likePost,
    error
  } = usePostInteractionsFallback({
    postId: post.id,
    initialLikeCount: post.reactionHart || 0,
    initialViewCount: post.viewCount || 0,
    autoRecordView: false // Disable auto-record, use unified tracking instead
  })

  // Record view on detail page mount (unified)
  useEffect(() => {
    const timer = setTimeout(() => {
      recordUnifiedView()
    }, 1000) // 1 second delay for detail page view

    return () => clearTimeout(timer)
  }, [recordUnifiedView])

  // Event handlers
  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share post')
  }

  const handleEdit = () => {
    // TODO: Implement edit functionality
    console.log('Edit post')
  }

  const handleCopyLink = () => {
    // TODO: Implement copy link functionality
    console.log('Copy link')
  }

  const handleReport = () => {
    // TODO: Implement report functionality
    console.log('Report post')
  }

  const handleFloatingReaction = () => {
    likePost()
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 theme-transition">
        <PostHeader
          title={post.title}
          locale={locale}
          translations={translations.header}
          onShare={handleShare}
          onEdit={handleEdit}
          onCopyLink={handleCopyLink}
          onReport={handleReport}
        />

        {/* Floating Reactions - Medium Style */}
        <FloatingReactions
          reactions={{
            heart: likeCount,
            impressions: viewCount
          }}
          onReaction={handleFloatingReaction}
          hasLiked={hasLiked}
          isLiking={isLiking}
        />

        <main className="relative container mx-auto px-4 py-8 pb-32">
          <EnhancedPostContent
            post={post}
            viewCount={viewCount}
            locale={locale}
            translations={translations.content}
          />
        </main>
      </div>
    </ErrorBoundary>
  )
}
