"use client"

import { useState, useEffect, useCallback } from 'react'

export interface PostInteractionStatus {
  postId: string
  likeCount: number
  viewCount: number
  userStatus: {
    hasLiked: boolean
    userLikeCount: number
    hasViewedToday: boolean
    lastViewedAt?: string
  } | null
}

interface UsePostInteractionsOptions {
  postId: string
  initialLikeCount?: number
  initialViewCount?: number
  autoRecordView?: boolean
}

interface UsePostInteractionsReturn {
  likeCount: number
  viewCount: number
  hasLiked: boolean
  userLikeCount: number
  hasViewedToday: boolean
  isLiking: boolean
  isRecordingView: boolean
  error: string | null
  likePost: () => Promise<void>
  recordView: () => Promise<void>
  refreshStatus: () => Promise<void>
}

/**
 * Custom hook for managing post interactions (likes and views)
 */
export function usePostInteractions({
  postId,
  initialLikeCount = 0,
  initialViewCount = 0,
  autoRecordView = true
}: UsePostInteractionsOptions): UsePostInteractionsReturn {
  const [likeCount, setLikeCount] = useState(initialLikeCount)
  const [viewCount, setViewCount] = useState(initialViewCount)
  const [hasLiked, setHasLiked] = useState(false)
  const [userLikeCount, setUserLikeCount] = useState(0)
  const [hasViewedToday, setHasViewedToday] = useState(false)
  const [isLiking, setIsLiking] = useState(false)
  const [isRecordingView, setIsRecordingView] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Fetch current interaction status
   */
  const refreshStatus = useCallback(async () => {
    try {
      setError(null)
      
      const response = await fetch(`/api/posts/${postId}/interactions`)
      const result = await response.json()

      if (!response.ok || !result.success) {
        const errorMsg = typeof result.error === 'string'
          ? result.error
          : result.error?.message || 'Failed to get interaction status'
        throw new Error(errorMsg)
      }

      const data: PostInteractionStatus = result.data
      
      setLikeCount(data.likeCount)
      setViewCount(data.viewCount)
      
      if (data.userStatus) {
        setHasLiked(data.userStatus.hasLiked)
        setUserLikeCount(data.userStatus.userLikeCount)
        setHasViewedToday(data.userStatus.hasViewedToday)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh status'
      setError(errorMessage)
      console.error('Error refreshing interaction status:', err)
    }
  }, [postId])

  /**
   * Like a post
   */
  const likePost = useCallback(async () => {
    if (isLiking) return

    try {
      setIsLiking(true)
      setError(null)

      // Optimistic update
      setLikeCount(prev => prev + 1)
      setUserLikeCount(prev => prev + 1)
      setHasLiked(true)

      const response = await fetch(`/api/posts/${postId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        const errorMsg = typeof result.error === 'string'
          ? result.error
          : result.error?.message || 'Failed to like post'
        throw new Error(errorMsg)
      }

      // Update with actual count from server
      setLikeCount(result.data.likeCount)
      
      console.log('✅ Post liked successfully')

    } catch (err) {
      // Revert optimistic update on error
      setLikeCount(prev => Math.max(0, prev - 1))
      setUserLikeCount(prev => Math.max(0, prev - 1))
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to like post'
      setError(errorMessage)
      console.error('Error liking post:', err)
    } finally {
      setIsLiking(false)
    }
  }, [postId, isLiking])

  /**
   * Record a view
   */
  const recordView = useCallback(async () => {
    if (isRecordingView || hasViewedToday) return

    try {
      setIsRecordingView(true)
      setError(null)

      const response = await fetch(`/api/posts/${postId}/view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        const errorMsg = typeof result.error === 'string'
          ? result.error
          : result.error?.message || 'Failed to record view'
        throw new Error(errorMsg)
      }

      // Update view count and status
      setViewCount(result.data.viewCount)
      setHasViewedToday(true)
      
      console.log('✅ View recorded:', result.data.message)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to record view'
      setError(errorMessage)
      console.error('Error recording view:', err)
    } finally {
      setIsRecordingView(false)
    }
  }, [postId, isRecordingView, hasViewedToday])

  // Load initial status and auto-record view
  useEffect(() => {
    let mounted = true

    const initialize = async () => {
      await refreshStatus()
      
      // Auto-record view if enabled and user hasn't viewed today
      if (mounted && autoRecordView && !hasViewedToday) {
        // Small delay to ensure status is loaded
        setTimeout(() => {
          if (mounted && !hasViewedToday) {
            recordView()
          }
        }, 1000)
      }
    }

    initialize()

    return () => {
      mounted = false
    }
  }, [postId, autoRecordView, refreshStatus, recordView, hasViewedToday])

  return {
    likeCount,
    viewCount,
    hasLiked,
    userLikeCount,
    hasViewedToday,
    isLiking,
    isRecordingView,
    error,
    likePost,
    recordView,
    refreshStatus
  }
}
