"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Upload, X, ImageIcon, AlertCircle, Loader2, Edit3, Type, MessageSquare } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"

interface UploadedImage {
  id: string
  file?: File // Optional for existing images
  url: string
  name: string
  size: number
  altText?: string
  caption?: string
  isExisting?: boolean // Flag to identify existing vs new images
}

interface ImageUploadProps {
  onImagesChange: (images: UploadedImage[]) => void
  initialImages?: UploadedImage[] // For existing images in edit mode
  maxImages?: number
  maxSizePerImage?: number // in MB
  acceptedTypes?: string[]
  className?: string
}

export function ImageUpload({
  onImagesChange,
  initialImages = [],
  maxImages = 5,
  maxSizePerImage = 10,
  acceptedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  className = "",
}: ImageUploadProps) {
  const [images, setImages] = useState<UploadedImage[]>(initialImages)
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [editingImage, setEditingImage] = useState<UploadedImage | null>(null)
  const [editForm, setEditForm] = useState({ altText: '', caption: '' })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use JPEG, PNG, GIF, or WebP.`
    }
    if (file.size > maxSizePerImage * 1024 * 1024) {
      return `File size must be less than ${maxSizePerImage}MB.`
    }
    return null
  }

  const processFiles = async (files: FileList) => {
    setError(null)
    setUploading(true)

    const newImages: UploadedImage[] = []
    const errors: string[] = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      if (images.length + newImages.length >= maxImages) {
        errors.push(`Maximum ${maxImages} images allowed.`)
        break
      }

      const validationError = validateFile(file)
      if (validationError) {
        errors.push(validationError)
        continue
      }

      try {
        // Create object URL for preview
        const url = URL.createObjectURL(file)

        const uploadedImage: UploadedImage = {
          id: `${Date.now()}-${i}`,
          file,
          url,
          name: file.name,
          size: file.size,
        }

        newImages.push(uploadedImage)
      } catch (err) {
        errors.push(`Failed to process ${file.name}`)
      }
    }

    if (errors.length > 0) {
      setError(errors.join(" "))
    }

    const updatedImages = [...images, ...newImages]
    setImages(updatedImages)
    onImagesChange(updatedImages)
    setUploading(false)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files)
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files)
    }
  }

  const removeImage = (id: string) => {
    const imageToRemove = images.find((img) => img.id === id)
    if (imageToRemove && !imageToRemove.isExisting) {
      // Only revoke object URL for new images, not existing ones
      URL.revokeObjectURL(imageToRemove.url)
    }

    const updatedImages = images.filter((img) => img.id !== id)
    setImages(updatedImages)
    onImagesChange(updatedImages)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const openEditModal = (image: UploadedImage) => {
    setEditingImage(image)
    setEditForm({
      altText: image.altText || '',
      caption: image.caption || ''
    })
  }

  const closeEditModal = () => {
    setEditingImage(null)
    setEditForm({ altText: '', caption: '' })
  }

  const saveImageEdit = () => {
    if (!editingImage) return

    const updatedImages = images.map(img =>
      img.id === editingImage.id
        ? { ...img, altText: editForm.altText.trim(), caption: editForm.caption.trim() }
        : img
    )

    setImages(updatedImages)
    onImagesChange(updatedImages)
    closeEditModal()
  }

  const hasMetadata = (image: UploadedImage) => {
    return !!(image.altText?.trim() || image.caption?.trim())
  }

  return (
    <div className={className}>
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-12 transition-all duration-200 theme-transition ${
          dragActive
            ? "border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20"
            : "border-gray-300 dark:border-gray-600 hover:border-green-400 dark:hover:border-green-500 bg-gray-50 dark:bg-gray-800/50"
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(",")}
          onChange={handleFileInput}
          className="hidden"
        />

        <div className="text-center">
          <div className="flex justify-center mb-6">
            {uploading ? (
              <Loader2 className="w-16 h-16 text-green-500 dark:text-green-400 animate-spin" />
            ) : (
              <div className="p-4 bg-gray-200 dark:bg-gray-700/50 rounded-full">
                <ImageIcon className="w-12 h-12 text-green-500 dark:text-green-400" />
              </div>
            )}
          </div>

          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            {uploading ? "Uploading images..." : "Add photos to your post"}
          </h3>

          <p className="text-gray-600 dark:text-gray-400 mb-6">Drag and drop images here, or click to browse</p>

          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading || images.length >= maxImages}
            className="bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 text-base"
            size="lg"
          >
            <Upload className="w-5 h-5 mr-2" />
            Choose Images
          </Button>

          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Support for JPEG, PNG, GIF, WebP up to {maxSizePerImage}MB each. Max {maxImages} images.
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-500/20 border border-red-200 dark:border-red-500/30 rounded-lg flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-red-500 dark:text-red-400 shrink-0 mt-0.5" />
          <div>
            <p className="text-red-600 dark:text-red-400 text-sm font-medium">Upload Error</p>
            <p className="text-red-500 dark:text-red-300 text-sm">{error}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 ml-auto"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Image Previews */}
      {images.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Uploaded Images ({images.length}/{maxImages})
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image) => (
              <Card key={image.id} className="bg-white dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 overflow-hidden group">
                <CardContent className="p-0">
                  <div className="relative aspect-square">
                    <Image
                      src={image.url || "/placeholder.svg"}
                      alt={image.altText || image.name}
                      fill
                      className="object-cover"
                    />

                    {/* Action Buttons */}
                    <div className="absolute top-2 right-2 flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditModal(image)}
                        className="bg-black/50 hover:bg-black/70 text-white w-8 h-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        title="Edit alt text and caption"
                      >
                        <Edit3 className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeImage(image.id)}
                        className="bg-black/50 hover:bg-black/70 text-white w-8 h-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        title="Remove image"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>

                    {/* Metadata Indicators */}
                    {hasMetadata(image) && (
                      <div className="absolute bottom-2 left-2 flex gap-1">
                        {image.altText?.trim() && (
                          <div className="bg-green-600 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1">
                            <Type className="w-3 h-3" />
                            Alt
                          </div>
                        )}
                        {image.caption?.trim() && (
                          <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1">
                            <MessageSquare className="w-3 h-3" />
                            Caption
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="p-3">
                    <p className="text-xs text-gray-700 dark:text-gray-300 truncate font-medium" title={image.name}>
                      {image.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">{formatFileSize(image.size)}</p>

                    {/* Preview of metadata */}
                    {image.altText?.trim() && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate" title={`Alt: ${image.altText}`}>
                        <Type className="w-3 h-3 inline mr-1" />
                        {image.altText}
                      </p>
                    )}
                    {image.caption?.trim() && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate mt-1" title={`Caption: ${image.caption}`}>
                        <MessageSquare className="w-3 h-3 inline mr-1" />
                        {image.caption}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Edit Image Modal */}
      <Dialog open={!!editingImage} onOpenChange={(open) => !open && closeEditModal()}>
        <DialogContent className="sm:max-w-md bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-gray-900 dark:text-white flex items-center gap-2">
              <Edit3 className="w-5 h-5" />
              Edit Image Details
            </DialogTitle>
          </DialogHeader>

          {editingImage && (
            <div className="space-y-6">
              {/* Image Preview */}
              <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                <Image
                  src={editingImage.url}
                  alt={editingImage.altText || editingImage.name}
                  fill
                  className="object-cover"
                />
              </div>

              {/* File Info */}
              <div className="text-center">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate" title={editingImage.name}>
                  {editingImage.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(editingImage.size)}
                </p>
              </div>

              {/* Alt Text Field */}
              <div className="space-y-2">
                <Label htmlFor="altText" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <Type className="w-4 h-4" />
                  Alt Text
                  <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">(for accessibility)</span>
                </Label>
                <Input
                  id="altText"
                  value={editForm.altText}
                  onChange={(e) => setEditForm(prev => ({ ...prev, altText: e.target.value }))}
                  placeholder="Describe what's in this image..."
                  maxLength={125}
                  className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400"
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Brief description for screen readers</span>
                  <span>{editForm.altText.length}/125</span>
                </div>
              </div>

              {/* Caption Field */}
              <div className="space-y-2">
                <Label htmlFor="caption" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <MessageSquare className="w-4 h-4" />
                  Caption
                  <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">(optional)</span>
                </Label>
                <Textarea
                  id="caption"
                  value={editForm.caption}
                  onChange={(e) => setEditForm(prev => ({ ...prev, caption: e.target.value }))}
                  placeholder="Add a descriptive caption that will be displayed with the image..."
                  rows={3}
                  maxLength={300}
                  className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 resize-none"
                />
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Descriptive text displayed with the image</span>
                  <span>{editForm.caption.length}/300</span>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex gap-2 sm:gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={closeEditModal}
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={saveImageEdit}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
