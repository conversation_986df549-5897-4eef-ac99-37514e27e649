"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  TrendingUp,
  Tag,
  Mail,
  Star
} from "lucide-react"
import Link from "next/link"
import { Post, Profile } from "@/types"

interface RightSidebarProps {
  posts: Post[]
  profile: Profile
}

export function RightSidebar({ posts, profile }: RightSidebarProps) {
  // Get popular tags (top 6 by frequency)
  const tagCounts = posts.reduce((acc, post) => {
    post.tags.forEach(tag => {
      acc[tag] = (acc[tag] || 0) + 1
    })
    return acc
  }, {} as Record<string, number>)
  
  const popularTags = Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 6)
    .map(([tag, count]) => ({ tag, count }))

  // Get trending posts (by reactions)
  const trendingPosts = [...posts]
    .sort((a, b) => (b.reactions.thumbsUp + b.reactions.heart + b.reactions.brain) - 
                    (a.reactions.thumbsUp + a.reactions.heart + a.reactions.brain))
    .slice(0, 3)

  return (
    <div className="space-y-5 sticky top-8">
      {/* Trending Posts - Compact */}
      <Card className="bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition">
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-semibold text-gray-900 dark:text-white theme-transition flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-green-500" />
            Trending
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {trendingPosts.map((post, index) => (
            <Link key={post.id} href={`/posts/${post.slug}`}>
              <div className="group p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200 theme-transition">
                <div className="flex items-start gap-2">
                  <div className="flex-shrink-0 w-5 h-5 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-xs font-medium text-gray-900 dark:text-white line-clamp-2 group-hover:text-green-500 dark:group-hover:text-green-400 transition-colors duration-200 theme-transition leading-tight">
                      {post.title}
                    </h4>
                    <div className="flex items-center gap-1 mt-1 text-xs text-gray-500 dark:text-gray-600 theme-transition">
                      <Star className="w-2.5 h-2.5" />
                      <span>{post.reactions.thumbsUp + post.reactions.heart + post.reactions.brain}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </CardContent>
      </Card>

      {/* Popular Tags - Compact */}
      <Card className="bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition">
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-semibold text-gray-900 dark:text-white theme-transition flex items-center gap-2">
            <Tag className="w-4 h-4 text-green-500" />
            Popular Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-1.5">
            {popularTags.slice(0, 6).map(({ tag, count }) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200 cursor-pointer theme-transition px-2 py-1"
              >
                #{tag}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Newsletter/Contact CTA */}
      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800/50 theme-transition">
        <CardContent className="p-4 text-center">
          <div className="mb-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <Mail className="w-4 h-4 text-white" />
            </div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white theme-transition">Stay Updated</h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 theme-transition mt-1">
              Get notified about new posts and updates
            </p>
          </div>
          <Button 
            size="sm" 
            className="w-full bg-green-500 hover:bg-green-600 text-white text-xs"
          >
            Subscribe
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
