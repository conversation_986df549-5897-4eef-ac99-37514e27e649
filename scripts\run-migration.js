const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  try {
    console.log('🚀 Running post interactions migration...')
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../database/migrations/create-post-interactions.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`\n⏳ Executing statement ${i + 1}/${statements.length}...`)
      console.log(`📄 ${statement.substring(0, 100)}${statement.length > 100 ? '...' : ''}`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          // Try direct execution for some statements
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0)
          
          if (directError && directError.message.includes('does not exist')) {
            console.log('⚠️  Using alternative execution method...')
            // For statements that can't be executed via RPC, we'll log them
            console.log('📋 Statement logged for manual execution:', statement)
          } else if (error) {
            throw error
          }
        }
        
        console.log('✅ Statement executed successfully')
        
      } catch (statementError) {
        console.error(`❌ Error executing statement ${i + 1}:`, statementError.message)
        
        // Continue with other statements for non-critical errors
        if (statementError.message.includes('already exists') || 
            statementError.message.includes('does not exist')) {
          console.log('⚠️  Non-critical error, continuing...')
        } else {
          throw statementError
        }
      }
    }
    
    console.log('\n🎉 Migration completed successfully!')
    console.log('\n📋 Manual steps required:')
    console.log('1. Go to Supabase Dashboard > SQL Editor')
    console.log('2. Execute the migration SQL manually if any statements failed')
    console.log('3. Verify tables were created: post_likes, post_views')
    console.log('4. Check that triggers and functions are working')
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message)
    console.error('\n🔧 Troubleshooting:')
    console.error('1. Check your Supabase credentials')
    console.error('2. Ensure you have admin access')
    console.error('3. Run the SQL manually in Supabase Dashboard')
    process.exit(1)
  }
}

// Run the migration
runMigration()
