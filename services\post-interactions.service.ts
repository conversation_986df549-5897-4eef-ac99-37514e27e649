import { PostInteractionsRepository, PostInteractionData } from '@/repositories/post-interactions.repository'
import { headers } from 'next/headers'

export class PostInteractionsService {
  private repository = new PostInteractionsRepository()

  /**
   * Get user identifier from request headers
   */
  private async getUserIdentifier(userId?: string): Promise<string> {
    if (userId) {
      return userId
    }

    // For anonymous users, use IP + User Agent
    const headersList = await headers()
    const forwarded = headersList.get('x-forwarded-for')
    const realIp = headersList.get('x-real-ip')
    const userAgent = headersList.get('user-agent') || 'unknown'
    
    // Get IP address (handle proxy scenarios)
    const ipAddress = forwarded?.split(',')[0]?.trim() || realIp || 'unknown'
    
    return this.repository.generateUserIdentifier(ipAddress, userAgent)
  }

  /**
   * Add a like to a post
   */
  async likePost(postId: string, userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      
      const interactionData: PostInteractionData = {
        postId,
        userId,
        userIdentifier
      }

      const result = await this.repository.addLike(interactionData)
      
      return {
        success: true,
        data: {
          likeCount: result.likeCount,
          message: 'Post liked successfully'
        }
      }

    } catch (error) {
      console.error('Error in likePost service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to like post'
      }
    }
  }

  /**
   * Add a view to a post
   */
  async viewPost(postId: string, userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      
      const interactionData: PostInteractionData = {
        postId,
        userId,
        userIdentifier
      }

      const result = await this.repository.addView(interactionData)
      
      return {
        success: true,
        data: {
          viewCount: result.viewCount,
          wasNewView: result.wasNewView,
          message: result.wasNewView ? 'View recorded' : 'Already viewed today'
        }
      }

    } catch (error) {
      console.error('Error in viewPost service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to record view'
      }
    }
  }

  /**
   * Get like status for a post
   */
  async getLikeStatus(postId: string, userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      const status = await this.repository.getLikeStatus(postId, userIdentifier)
      
      return {
        success: true,
        data: status
      }

    } catch (error) {
      console.error('Error in getLikeStatus service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get like status'
      }
    }
  }

  /**
   * Get view status for a post
   */
  async getViewStatus(postId: string, userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      const status = await this.repository.getViewStatus(postId, userIdentifier)
      
      return {
        success: true,
        data: status
      }

    } catch (error) {
      console.error('Error in getViewStatus service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get view status'
      }
    }
  }

  /**
   * Get complete interaction status for a post
   */
  async getPostInteractionStatus(postId: string, userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      const summary = await this.repository.getPostInteractionSummary(postId, userIdentifier)
      
      return {
        success: true,
        data: {
          postId,
          likeCount: summary.likeCount,
          viewCount: summary.viewCount,
          userStatus: summary.userStatus
        }
      }

    } catch (error) {
      console.error('Error in getPostInteractionStatus service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get interaction status'
      }
    }
  }

  /**
   * Get interaction status for multiple posts (for homepage/list views)
   */
  async getMultiplePostsInteractionStatus(postIds: string[], userId?: string) {
    try {
      const userIdentifier = await this.getUserIdentifier(userId)
      
      const results = await Promise.all(
        postIds.map(async (postId) => {
          try {
            const summary = await this.repository.getPostInteractionSummary(postId, userIdentifier)
            return {
              postId,
              likeCount: summary.likeCount,
              viewCount: summary.viewCount,
              userStatus: summary.userStatus
            }
          } catch (error) {
            console.error(`Error getting status for post ${postId}:`, error)
            return {
              postId,
              likeCount: 0,
              viewCount: 0,
              userStatus: null,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        })
      )

      return {
        success: true,
        data: results
      }

    } catch (error) {
      console.error('Error in getMultiplePostsInteractionStatus service:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get interaction statuses'
      }
    }
  }
}
