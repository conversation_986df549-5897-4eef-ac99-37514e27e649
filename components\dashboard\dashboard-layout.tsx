"use client"

import { useState } from 'react'
import { Sidebar } from './sidebar'
import { DashboardHeader } from './dashboard-header'
import { cn } from '@/lib/utils'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
}

export function DashboardLayout({
  children,
  title = "Dashboard",
  description = "Manage your personal website",
  className
}: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className={cn(
        "transition-all duration-300",
        sidebarCollapsed ? "ml-16" : "ml-64"
      )}>
        {/* Header */}
        <DashboardHeader
          title={title}
          description={description}
        />

        {/* Page Content */}
        <main className={cn(
          "p-6 space-y-6",
          className
        )}>
          {children}
        </main>
      </div>
    </div>
  )
}
