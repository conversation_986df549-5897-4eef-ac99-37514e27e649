#!/usr/bin/env tsx

import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

const API_BASE = 'http://localhost:3000'

async function testAuthAPI() {
  console.log('🧪 Testing Admin-Only Authentication API...')

  try {
    // Use admin credentials from environment or defaults
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456'

    console.log(`👤 Testing with admin: ${adminEmail}`)

    // Test 1: Test registration endpoint (should not exist)
    console.log('\n1️⃣ Testing registration endpoint (should be 404)...')
    try {
      const registerResponse = await fetch(`${API_BASE}/api/auth/supabase/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'test123456',
          name: 'Test User',
        }),
      })

      console.log(`Status: ${registerResponse.status}`)

      if (registerResponse.status === 404) {
        console.log('✅ Registration endpoint properly disabled')
      } else {
        console.log('⚠️ Registration endpoint still exists (should be removed)')
      }
    } catch (error) {
      console.log('✅ Registration endpoint not found (expected)')
    }

    // Test 2: Login with credentials
    console.log('\n2️⃣ Testing user login...')
    let accessToken = null

    try {
      const loginResponse = await fetch(`${API_BASE}/api/auth/supabase/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: adminEmail,
          password: adminPassword,
        }),
      })

      const loginData = await loginResponse.json()
      console.log(`Status: ${loginResponse.status}`)
      console.log('Response:', JSON.stringify(loginData, null, 2))

      if (loginResponse.ok && loginData.data?.session?.access_token) {
        console.log('✅ Login successful')
        accessToken = loginData.data.session.access_token
        console.log(`🔑 Access token: ${accessToken.substring(0, 20)}...`)
      } else {
        console.log('❌ Login failed')
      }
    } catch (error) {
      console.error('❌ Login test failed:', error)
    }

    // Test 3: Get user info with token
    if (accessToken) {
      console.log('\n3️⃣ Testing authenticated user info...')
      try {
        const meResponse = await fetch(`${API_BASE}/api/auth/supabase/me`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        })

        const meData = await meResponse.json()
        console.log(`Status: ${meResponse.status}`)
        console.log('Response:', JSON.stringify(meData, null, 2))

        if (meResponse.ok) {
          console.log('✅ User info retrieved successfully')
        } else {
          console.log('❌ Failed to get user info')
        }
      } catch (error) {
        console.error('❌ User info test failed:', error)
      }

      // Test 4: Test dashboard API with authentication
      console.log('\n4️⃣ Testing dashboard API with authentication...')
      try {
        const dashboardResponse = await fetch(`${API_BASE}/api/dashboard/post-types`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        })

        const dashboardData = await dashboardResponse.json()
        console.log(`Status: ${dashboardResponse.status}`)
        console.log('Response:', JSON.stringify(dashboardData, null, 2))

        if (dashboardResponse.ok) {
          console.log('✅ Dashboard API access successful')
        } else {
          console.log('❌ Dashboard API access failed')
        }
      } catch (error) {
        console.error('❌ Dashboard API test failed:', error)
      }

      // Test 5: Logout
      console.log('\n5️⃣ Testing user logout...')
      try {
        const logoutResponse = await fetch(`${API_BASE}/api/auth/supabase/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        })

        const logoutData = await logoutResponse.json()
        console.log(`Status: ${logoutResponse.status}`)
        console.log('Response:', JSON.stringify(logoutData, null, 2))

        if (logoutResponse.ok) {
          console.log('✅ Logout successful')
        } else {
          console.log('❌ Logout failed')
        }
      } catch (error) {
        console.error('❌ Logout test failed:', error)
      }
    }

    // Test 6: Test dashboard API without authentication (should fail)
    console.log('\n6️⃣ Testing dashboard API without authentication...')
    try {
      const unauthResponse = await fetch(`${API_BASE}/api/dashboard/post-types`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const unauthData = await unauthResponse.json()
      console.log(`Status: ${unauthResponse.status}`)
      console.log('Response:', JSON.stringify(unauthData, null, 2))

      if (unauthResponse.status === 401) {
        console.log('✅ Unauthorized access properly blocked')
      } else {
        console.log('⚠️ Expected 401 status for unauthorized access')
      }
    } catch (error) {
      console.error('❌ Unauthorized test failed:', error)
    }

    console.log('\n🎉 Authentication API tests completed!')

  } catch (error) {
    console.error('❌ Test error:', error)
    process.exit(1)
  }
}

// Run the test
testAuthAPI()
