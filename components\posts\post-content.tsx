import { Card, CardContent } from "@/components/ui/card"
import { PostSlideshow } from "@/components/gallery/post-slideshow"
import { PostMeta } from "@/components/ui/post-meta"
import { AuthorInfo } from "@/components/ui/author-info"
import { ReactionGroup } from "@/components/ui/reaction-button"
import { textStyles, cardStyles } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"
import { Post } from "@/types"
import { ReactionType } from "@/hooks/use-reactions"

interface PostContentProps {
  post: Post
  viewCount?: number
  reactions: {
    thumbsUp: number
    heart: number
    brain: number
  }
  onReaction: (type: ReactionType) => void
}

/**
 * Main content area for post detail pages
 */
export function PostContent({
  post,
  viewCount,
  reactions,
  onReaction
}: PostContentProps) {
  return (
    <div className="space-y-8">
      {/* Post Header */}
      <div>
        {/* Post Meta Information */}
        <PostMeta
          type={post.type}
          version={post.version}
          readTime={post.readTime}
          viewCount={viewCount}
          updatedAt={post.updatedAt}
          className="mb-4"
        />

        <h1 className={cn(textStyles.heading.h1, "mb-6")}>
          {post.title}
        </h1>

        {/* Author & Engagement Section */}
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
          <AuthorInfo
            author={post.author}
            date={post.updatedAt}
            dateLabel="Updated"
          />

          {/* Reaction Buttons */}
          <ReactionGroup
            reactions={reactions}
            onReaction={onReaction}
          />
        </div>
      </div>

      {/* Post Content */}
      <Card className={cn(cardStyles.base)}>
        <CardContent className="p-8">
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 leading-relaxed font-sans theme-transition text-base">
              {post.content}
            </pre>
          </div>
          
          {post.images && post.images.length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg theme-transition mt-4">
              <PostSlideshow
                images={post.images}
                autoPlay={false}
                showThumbnails={true}
                className="rounded-lg overflow-hidden"
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
