import { NextRequest } from 'next/server'
import { ValidationError } from '@/types/api'

export interface ValidationRule {
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'array' | 'object'
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
  enum?: any[]
}

export interface ValidationSchema {
  [key: string]: ValidationRule
}

export class RequestValidator {
  /**
   * Validate request body against schema
   */
  static async validateBody(
    request: NextRequest,
    schema: ValidationSchema
  ): Promise<{ isValid: boolean; errors: ValidationError[]; data?: any }> {
    try {
      const body = await request.json()
      return this.validate(body, schema)
    } catch (error) {
      return {
        isValid: false,
        errors: [{
          field: 'body',
          message: 'Invalid JSON format',
          code: 'INVALID_JSON'
        }]
      }
    }
  }

  /**
   * Validate query parameters against schema
   */
  static validateQuery(
    request: NextRequest,
    schema: ValidationSchema
  ): { isValid: boolean; errors: ValidationError[]; data?: any } {
    const { searchParams } = new URL(request.url)
    const query: Record<string, any> = {}

    searchParams.forEach((value, key) => {
      // Pre-transform query parameters based on expected type
      const rule = schema[key]
      if (rule?.type === 'number') {
        const numValue = parseFloat(value)
        query[key] = isNaN(numValue) ? value : numValue
      } else if (rule?.type === 'boolean') {
        query[key] = value.toLowerCase() === 'true'
      } else {
        query[key] = value
      }
    })

    return this.validate(query, schema)
  }

  /**
   * Validate data against schema
   */
  static validate(
    data: any,
    schema: ValidationSchema
  ): { isValid: boolean; errors: ValidationError[]; data?: any } {
    const errors: ValidationError[] = []
    const validatedData: any = {}

    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field]
      const fieldErrors = this.validateField(field, value, rule)

      if (fieldErrors.length > 0) {
        errors.push(...fieldErrors)
      } else {
        validatedData[field] = this.transformValue(value, rule)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      data: errors.length === 0 ? validatedData : undefined
    }
  }

  /**
   * Validate single field
   */
  private static validateField(
    field: string,
    value: any,
    rule: ValidationRule
  ): ValidationError[] {
    const errors: ValidationError[] = []

    // Required check
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        code: 'REQUIRED'
      })
      return errors
    }

    // Skip other validations if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return errors
    }

    // Type validation
    if (rule.type) {
      const typeError = this.validateType(field, value, rule.type)
      if (typeError) {
        errors.push(typeError)
        return errors // Stop validation if type is wrong
      }
    }

    // Length validation for strings
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push({
          field,
          message: `${field} must be at least ${rule.minLength} characters`,
          code: 'MIN_LENGTH'
        })
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push({
          field,
          message: `${field} must not exceed ${rule.maxLength} characters`,
          code: 'MAX_LENGTH'
        })
      }
    }

    // Numeric validation
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push({
          field,
          message: `${field} must be at least ${rule.min}`,
          code: 'MIN_VALUE'
        })
      }

      if (rule.max !== undefined && value > rule.max) {
        errors.push({
          field,
          message: `${field} must not exceed ${rule.max}`,
          code: 'MAX_VALUE'
        })
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        errors.push({
          field,
          message: `${field} format is invalid`,
          code: 'INVALID_FORMAT'
        })
      }
    }

    // Enum validation
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push({
        field,
        message: `${field} must be one of: ${rule.enum.join(', ')}`,
        code: 'INVALID_ENUM'
      })
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(value)
      if (customResult !== true) {
        errors.push({
          field,
          message: typeof customResult === 'string' ? customResult : `${field} is invalid`,
          code: 'CUSTOM_VALIDATION'
        })
      }
    }

    return errors
  }

  /**
   * Validate field type
   */
  private static validateType(
    field: string,
    value: any,
    type: ValidationRule['type']
  ): ValidationError | null {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          return { field, message: `${field} must be a string`, code: 'INVALID_TYPE' }
        }
        break

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return { field, message: `${field} must be a number`, code: 'INVALID_TYPE' }
        }
        break

      case 'boolean':
        if (typeof value !== 'boolean') {
          return { field, message: `${field} must be a boolean`, code: 'INVALID_TYPE' }
        }
        break

      case 'email':
        if (typeof value !== 'string' || !this.isValidEmail(value)) {
          return { field, message: `${field} must be a valid email`, code: 'INVALID_EMAIL' }
        }
        break

      case 'url':
        if (typeof value !== 'string' || !this.isValidUrl(value)) {
          return { field, message: `${field} must be a valid URL`, code: 'INVALID_URL' }
        }
        break

      case 'array':
        if (!Array.isArray(value)) {
          return { field, message: `${field} must be an array`, code: 'INVALID_TYPE' }
        }
        break

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value) || value === null) {
          return { field, message: `${field} must be an object`, code: 'INVALID_TYPE' }
        }
        break
    }

    return null
  }

  /**
   * Transform value based on type
   */
  private static transformValue(value: any, rule: ValidationRule): any {
    if (value === undefined || value === null) {
      return value
    }

    switch (rule.type) {
      case 'number':
        return typeof value === 'string' ? parseFloat(value) : value

      case 'boolean':
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true'
        }
        return Boolean(value)

      default:
        return value
    }
  }

  /**
   * Email validation
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * URL validation
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
}
