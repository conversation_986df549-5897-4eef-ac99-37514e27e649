import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { AlertCircle } from 'lucide-react'

interface InputFieldProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'password' | 'url' | 'textarea'
  placeholder?: string
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  rows?: number
  maxLength?: number
  className?: string
  description?: string
}

export function InputField({
  label,
  name,
  type = 'text',
  placeholder,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  rows = 4,
  maxLength,
  className,
  description
}: InputFieldProps) {
  const hasError = !!error

  const inputClasses = cn(
    "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
    "focus:outline-none focus:border-green-500 dark:focus:border-green-400 focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
    hasError && "border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500 dark:focus:ring-red-400 focus-visible:ring-red-500 dark:focus-visible:ring-red-400",
    disabled && "opacity-50 cursor-not-allowed",
    className
  )

  return (
    <div className="space-y-1">
      <Label
        htmlFor={name}
        className="text-sm font-medium text-gray-700 dark:text-gray-300"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      {description && (
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}

      {type === 'textarea' ? (
        <Textarea
          id={name}
          name={name}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          className={inputClasses}
        />
      ) : (
        <Input
          id={name}
          name={name}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          maxLength={maxLength}
          className={inputClasses}
        />
      )}

      <div className="flex items-start justify-between">
        {hasError && (
          <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {maxLength && (
          <span className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
            {value.length}/{maxLength}
          </span>
        )}
      </div>
    </div>
  )
}
