import { NextRequest } from 'next/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { createServerSupabaseAdminClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing post interactions system (simple)...')

    const supabase = createServerSupabaseAdminClient()
    
    // Test basic Supabase connection
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id, title, reactions_heart, view_count')
      .limit(3)

    if (postsError) {
      return ApiResponseBuilder.internalError(`Supabase connection failed: ${postsError.message}`)
    }

    // Check if interaction tables exist
    const { error: likesTableError } = await supabase
      .from('post_likes')
      .select('id')
      .limit(1)

    const { error: viewsTableError } = await supabase
      .from('post_views')
      .select('id')
      .limit(1)

    const tablesExist = {
      post_likes: !likesTableError || !likesTableError.message.includes('does not exist'),
      post_views: !viewsTableError || !viewsTableError.message.includes('does not exist')
    }

    return ApiResponseBuilder.success(
      {
        supabaseConnection: 'OK',
        postsFound: posts?.length || 0,
        samplePosts: posts?.map(p => ({
          id: p.id,
          title: p.title,
          currentLikes: p.reactions_heart || 0,
          currentViews: p.view_count || 0
        })) || [],
        tablesStatus: {
          post_likes: tablesExist.post_likes ? 'EXISTS' : 'MISSING',
          post_views: tablesExist.post_views ? 'EXISTS' : 'MISSING'
        },
        systemReady: tablesExist.post_likes && tablesExist.post_views,
        setupInstructions: !tablesExist.post_likes || !tablesExist.post_views ? {
          message: 'Database tables need to be created',
          setupUrl: '/scripts/setup-post-interactions.html',
          requiredTables: ['post_likes', 'post_views']
        } : null
      },
      'Post interactions system status check completed'
    )

  } catch (error) {
    console.error('💥 Error testing post interactions:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to test post interactions'
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, postId } = body

    if (!action || !postId) {
      return ApiResponseBuilder.badRequest('Missing action or postId')
    }

    console.log(`🧪 Testing ${action} for post:`, postId)

    const supabase = createServerSupabaseAdminClient()

    switch (action) {
      case 'simulate-like':
        // Simulate like by directly updating post
        const { data: likeResult, error: likeError } = await supabase
          .from('posts')
          .update({ 
            reactions_heart: supabase.rpc('increment_reactions_heart', { post_id: postId })
          })
          .eq('id', postId)
          .select('reactions_heart')
          .single()

        if (likeError) {
          // Fallback: get current count and increment
          const { data: currentPost } = await supabase
            .from('posts')
            .select('reactions_heart')
            .eq('id', postId)
            .single()

          const newCount = (currentPost?.reactions_heart || 0) + 1
          
          const { error: updateError } = await supabase
            .from('posts')
            .update({ reactions_heart: newCount })
            .eq('id', postId)

          if (updateError) {
            throw new Error(`Failed to simulate like: ${updateError.message}`)
          }

          return ApiResponseBuilder.success(
            { newLikeCount: newCount, method: 'fallback' },
            'Like simulated successfully (fallback method)'
          )
        }

        return ApiResponseBuilder.success(
          { newLikeCount: likeResult.reactions_heart, method: 'direct' },
          'Like simulated successfully'
        )

      case 'simulate-view':
        // Simulate view by directly updating post
        const { data: currentPost } = await supabase
          .from('posts')
          .select('view_count')
          .eq('id', postId)
          .single()

        const newViewCount = (currentPost?.view_count || 0) + 1
        
        const { error: viewUpdateError } = await supabase
          .from('posts')
          .update({ view_count: newViewCount })
          .eq('id', postId)

        if (viewUpdateError) {
          throw new Error(`Failed to simulate view: ${viewUpdateError.message}`)
        }

        return ApiResponseBuilder.success(
          { newViewCount, method: 'direct' },
          'View simulated successfully'
        )

      case 'get-status':
        const { data: statusPost, error: statusError } = await supabase
          .from('posts')
          .select('id, title, reactions_heart, view_count')
          .eq('id', postId)
          .single()

        if (statusError) {
          throw new Error(`Failed to get status: ${statusError.message}`)
        }

        return ApiResponseBuilder.success(
          {
            postId: statusPost.id,
            title: statusPost.title,
            likeCount: statusPost.reactions_heart || 0,
            viewCount: statusPost.view_count || 0
          },
          'Status retrieved successfully'
        )

      default:
        return ApiResponseBuilder.badRequest(`Unknown action: ${action}`)
    }

  } catch (error) {
    console.error('💥 Error in post interactions test:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to test interaction'
    )
  }
}
