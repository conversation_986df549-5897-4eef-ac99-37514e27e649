import { NextRequest } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get all post images
    const { data: images, error, count } = await supabase
      .from('post_images')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    if (error) {
      return Response.json({
        error: 'Failed to fetch post images',
        details: error
      }, { status: 500 })
    }

    // Also get recent posts to see if they have images
    const { data: recentPosts, error: postsError } = await supabase
      .from('posts')
      .select('id, title, created_at')
      .order('created_at', { ascending: false })
      .limit(5)

    return Response.json({
      success: true,
      postImages: {
        data: images || [],
        count: count || 0
      },
      recentPosts: recentPosts || [],
      message: `Found ${count || 0} post images`
    })

  } catch (error) {
    console.error('Error fetching post images:', error)
    return Response.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Test endpoint to manually insert post image
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const testImageData = {
      post_id: '17b3fa87-450b-4bed-81e0-bd383fd8b709', // Use the post ID from previous test
      url: 'https://picsum.photos/800/600?random=test',
      alt_text: 'Test manual insert alt text',
      caption: 'Test manual insert caption',
      display_order: 1
      // Note: file_name and file_size are not in the current schema
    }

    console.log('🧪 Manually inserting test image:', testImageData)

    const { data: insertedImage, error } = await supabase
      .from('post_images')
      .insert(testImageData)
      .select()
      .single()

    if (error) {
      console.error('❌ Error inserting test image:', error)
      return Response.json({
        error: 'Failed to insert test image',
        details: error
      }, { status: 500 })
    }

    console.log('✅ Test image inserted successfully:', insertedImage)

    return Response.json({
      success: true,
      data: insertedImage,
      message: 'Test image inserted successfully'
    })

  } catch (error) {
    console.error('💥 Error in manual insert:', error)
    return Response.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
