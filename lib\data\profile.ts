import { Profile } from '../types'

export const profileData: Profile = {
  name: "<PERSON><PERSON><PERSON>",
  title: "Software Enginering",
  bio: "Passionate about building scalable web applications and sharing knowledge through code. Currently working with <PERSON>vel,  React, Next.js, and Node.js to create innovative solutions.",
  location: "Pur<PERSON>inggga, ID",
  joinDate: "August 2018",
  avatar: "/profile.png",
  social: {
    github: "rijaldev",
    instagram: "rijaldev",
    facebook: "rijaldev",
    linkedin: "rijal-solahudin",
    email: "<EMAIL>",
  },
  experience: [
    {
      id: "1",
      company: "TechCorp Inc.",
      position: "Senior Full Stack Developer",
      duration: "2022 - Present",
      location: "San Francisco, CA",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Leading development of scalable web applications using React, Next.js, and Node.js. Architecting microservices and implementing CI/CD pipelines.",
      achievements: [
        "Led a team of 5 developers in building a customer portal serving 100K+ users",
        "Reduced application load times by 40% through performance optimization",
        "Implemented automated testing reducing bugs by 60%",
        "Mentored 3 junior developers and conducted technical interviews",
      ],
      technologies: ["React", "Next.js", "Node.js", "TypeScript", "AWS", "Docker", "PostgreSQL", "Redis"],
      projects: [
        {
          name: "Customer Portal Redesign",
          description: "Complete overhaul of customer-facing portal with modern UI/UX",
          impact: "Increased user engagement by 35%",
        },
        {
          name: "Microservices Architecture",
          description: "Migrated monolithic application to microservices",
          impact: "Improved system scalability and reduced deployment time by 50%",
        },
      ],
    },
    {
      id: "2",
      company: "StartupXYZ",
      position: "Full Stack Developer",
      duration: "2020 - 2022",
      location: "Remote",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Built and maintained multiple client applications with modern web technologies. Worked closely with product team to deliver features.",
      achievements: [
        "Developed 8 client applications from scratch using React and Node.js",
        "Implemented real-time features using WebSocket connections",
        "Optimized database queries reducing response time by 30%",
        "Collaborated with design team to implement pixel-perfect UIs",
      ],
      technologies: ["React", "Vue.js", "Node.js", "Express", "MongoDB", "Socket.io", "Stripe API"],
      projects: [
        {
          name: "E-commerce Platform",
          description: "Full-featured e-commerce solution with payment integration",
          impact: "Processed $2M+ in transactions",
        },
        {
          name: "Real-time Chat Application",
          description: "Multi-room chat application with file sharing",
          impact: "Supported 10K+ concurrent users",
        },
      ],
    },
    {
      id: "3",
      company: "WebSolutions",
      position: "Frontend Developer",
      duration: "2019 - 2020",
      location: "New York, NY",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Specialized in creating responsive and accessible user interfaces. Worked on various client projects ranging from corporate websites to web applications.",
      achievements: [
        "Delivered 12+ responsive websites with 100% accessibility compliance",
        "Reduced page load times by 50% through optimization techniques",
        "Implemented design systems improving development efficiency by 25%",
        "Trained team on modern CSS techniques and best practices",
      ],
      technologies: ["HTML5", "CSS3", "JavaScript", "React", "Sass", "Webpack", "Figma"],
      projects: [
        {
          name: "Corporate Website Redesign",
          description: "Modern, responsive website for Fortune 500 company",
          impact: "Increased conversion rate by 25%",
        },
        {
          name: "Design System Implementation",
          description: "Comprehensive design system for consistent UI components",
          impact: "Reduced development time by 30%",
        },
      ],
    },
  ],
  portfolio: [], // Will be populated from portfolio data
}
