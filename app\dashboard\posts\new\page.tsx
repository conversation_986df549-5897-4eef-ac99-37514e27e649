'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { PostForm } from '@/components/dashboard/forms/post-form'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function NewPostPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { isAuthenticated, token, isLoading } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Redirect to login if not authenticated (but wait for auth loading to complete)
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !token) {
      router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
    }
  }, [isAuthenticated, token, isLoading, router])

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true)

      // Transform images from UploadedImage format to API format
      const transformedImages = formData.images?.map((img: any) => ({
        id: img.id,
        url: img.url,
        altText: img.altText || '',
        caption: img.caption || '',
        name: img.name || '',
        size: img.size || 0,
        order: 0
      })) || []

      const apiData = {
        ...formData,
        images: transformedImages
      }

      console.log('Creating post:', apiData)

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      // Add Authorization header if token is available
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/dashboard/posts', {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify(apiData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create post')
      }

      const result = await response.json()
      
      toast({
        title: "Success",
        description: `Post ${formData.status === 'PUBLISHED' ? 'published' : 'saved as draft'} successfully`,
      })

      // Redirect to posts list
      router.push('/dashboard/posts')
      
    } catch (error) {
      console.error('Error creating post:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to create post',
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <DashboardLayout
        title="Create New Post"
        description="Loading..."
      >
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-gray-600 dark:text-gray-400">
            <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            <span>Checking authentication...</span>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Create New Post"
      description="Write and publish a new blog post"
    >
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            asChild
            className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
          >
            <Link href="/dashboard/posts">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Posts
            </Link>
          </Button>
        </div>

        {/* Post Form */}
        <PostForm
          mode="create"
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
        />
      </div>
    </DashboardLayout>
  )
}
