#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createAdminUser } from '../lib/supabase/auth'
import { createServerSupabaseAdminClient } from '../lib/supabase/server'

// Load environment variables
config({ path: '.env.local' })

async function createSupabaseAdmin() {
  console.log('👤 Creating Supabase admin user...')
  
  try {
    // Get admin details from environment or prompt
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456'
    const adminName = process.env.ADMIN_NAME || 'Admin User'
    
    console.log(`📧 Email: ${adminEmail}`)
    console.log(`👤 Name: ${adminName}`)
    console.log(`🔑 Password: ${'*'.repeat(adminPassword.length)}`)
    
    // Check if user already exists
    console.log('\n🔍 Checking if admin user already exists...')
    const supabase = createServerSupabaseAdminClient()
    
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers()
    
    if (listError) {
      console.error('❌ Error checking existing users:', listError)
      return
    }
    
    const existingUser = existingUsers.users.find(user => user.email === adminEmail)
    
    if (existingUser) {
      console.log('✅ Admin user already exists!')
      console.log(`   - ID: ${existingUser.id}`)
      console.log(`   - Email: ${existingUser.email}`)
      console.log(`   - Created: ${existingUser.created_at}`)
      
      // Check if user profile exists
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', existingUser.id)
        .single()
      
      if (profileError || !profile) {
        console.log('⚠️ User profile missing, creating...')
        
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({
            id: existingUser.id,
            name: adminName,
          })
        
        if (createProfileError) {
          console.error('❌ Error creating user profile:', createProfileError)
        } else {
          console.log('✅ User profile created successfully!')
        }
      } else {
        console.log('✅ User profile exists!')
        console.log(`   - Name: ${profile.name}`)
      }
      
      return
    }
    
    // Create new admin user
    console.log('\n🚀 Creating new admin user...')
    const result = await createAdminUser(adminEmail, adminPassword, adminName)
    
    if (!result.success) {
      console.error('❌ Failed to create admin user:', result.error)
      return
    }
    
    console.log('✅ Admin user created successfully!')
    console.log(`   - ID: ${result.user?.id}`)
    console.log(`   - Email: ${result.user?.email}`)
    console.log(`   - Name: ${adminName}`)
    
    // Verify user profile was created
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', result.user?.id)
      .single()
    
    if (profileError || !profile) {
      console.log('⚠️ User profile not found, this might be expected for new users')
    } else {
      console.log('✅ User profile verified!')
      console.log(`   - Profile Name: ${profile.name}`)
    }
    
    console.log('\n🎉 Admin setup completed!')
    console.log('\n📝 Next steps:')
    console.log('1. Use these credentials to login to the dashboard')
    console.log('2. Test the authentication endpoints')
    console.log('3. Create some test posts')
    
  } catch (error) {
    console.error('❌ Admin creation error:', error)
    process.exit(1)
  }
}

// Run the admin creation
createSupabaseAdmin()
