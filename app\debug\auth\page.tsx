'use client'

import { useAuth } from '@/contexts/auth-context'
import { useState } from 'react'

export default function DebugAuthPage() {
  const { user, token, isAuthenticated, login, logout } = useAuth()
  const [testResult, setTestResult] = useState<any>(null)

  const testDashboardAPI = async () => {
    try {
      const response = await fetch('/api/dashboard/posts/da8cadac-5531-4c34-9330-42ed134dd7a3', {
        credentials: 'include'
      })
      const result = await response.json()
      setTestResult({ status: response.status, data: result })
    } catch (error) {
      setTestResult({ error: error.message })
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Authentication</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Auth State:</h2>
          <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
          <p>User ID: {user?.id || 'None'}</p>
          <p>User Email: {user?.email || 'None'}</p>
          <p>Has Token: {token ? 'Yes' : 'No'}</p>
          <p>Token Preview: {token ? token.substring(0, 20) + '...' : 'None'}</p>
        </div>

        <div className="space-x-4">
          <button 
            onClick={testDashboardAPI}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Test Dashboard API
          </button>
          
          <button 
            onClick={() => window.location.href = '/admin-access'}
            className="bg-green-500 text-white px-4 py-2 rounded"
          >
            Go to Login
          </button>
          
          {isAuthenticated && (
            <button 
              onClick={logout}
              className="bg-red-500 text-white px-4 py-2 rounded"
            >
              Logout
            </button>
          )}
        </div>

        {testResult && (
          <div className="bg-gray-100 p-4 rounded">
            <h2 className="font-bold mb-2">API Test Result:</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
