"use client"

import { useState } from "react"
import { <PERSON>alog, DialogPortal, DialogOverlay } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { Badge } from "@/components/ui/badge"
import { Search, Terminal, GitBranch, Clock } from "lucide-react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"

interface PostType {
  id: string
  name: string
  color: string
  slug: string
  createdAt: string
  updatedAt: string
}

interface Post {
  id: string
  title: string
  content: string
  version: string
  type: PostType
  createdAt: string
  updatedAt: string
  tags: string[]
}

interface CommandPaletteProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  posts: Post[]
}

export function CommandPalette({ open, onOpenChange, posts }: CommandPaletteProps) {
  const [query, setQuery] = useState("")
  const router = useRouter()

  const filteredPosts = posts.filter(
    (post) =>
      post.title.toLowerCase().includes(query.toLowerCase()) ||
      post.tags.some((tag) => tag.toLowerCase().includes(query.toLowerCase())) ||
      post.content.toLowerCase().includes(query.toLowerCase()),
  )

  const handleSelect = (postSlug: string) => {
    router.push(`/posts/${postSlug}`)
    onOpenChange(false)
    setQuery("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogPortal>
        <DialogOverlay />
        <DialogPrimitive.Content
          className={cn(
            "fixed left-[50%] top-[50%] z-50 grid w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800 text-gray-900 dark:text-gray-100 p-0 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg theme-transition"
          )}
        >
        <div className="flex items-center border-b border-gray-200 dark:border-gray-600 px-4 py-3 theme-transition">
          <Search className="w-4 h-4 text-gray-400 dark:text-gray-500 mr-3 flex-shrink-0" />
          <input
            type="text"
            placeholder="Search posts, tags, or content..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 outline-none text-sm py-1 theme-transition"
            autoFocus
          />
          <div className="hidden sm:flex items-center gap-1 text-xs text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded border border-gray-200 dark:border-gray-600 theme-transition">
            <span>ESC</span>
          </div>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {filteredPosts.length === 0 ? (
            <div className="p-12 text-center text-gray-600 dark:text-gray-500 theme-transition">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center theme-transition">
                <Terminal className="w-8 h-8" />
              </div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2 theme-transition">
                {query ? "No posts found" : "Start typing to search"}
              </h3>
              <p className="text-sm">
                {query
                  ? "Try searching with different keywords or tags"
                  : "Search through posts, tags, and content"
                }
              </p>
            </div>
          ) : (
            <div className="p-3">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-3 px-3 theme-transition">
                {filteredPosts.length} result{filteredPosts.length !== 1 ? 's' : ''} found
              </div>
              {filteredPosts.map((post) => (
                <div
                  key={post.id}
                  onClick={() => handleSelect(post.slug)}
                  className="p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-all duration-200 theme-transition group border border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                >
                  <div className="flex items-start justify-between gap-3 mb-3">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100 line-clamp-2 group-hover:text-green-500 dark:group-hover:text-green-400 transition-colors theme-transition leading-snug">
                      {post.title}
                    </h3>
                    <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-md flex-shrink-0 theme-transition">
                      <GitBranch className="w-3 h-3" />
                      <span>{post.version}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 mb-3 theme-transition">
                    <div className="flex items-center gap-1.5">
                      <Clock className="w-3 h-3" />
                      <span>Updated {post.updatedAt}</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span className="capitalize">{post.type.name}</span>
                    </div>
                  </div>

                  {post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1.5">
                      {post.tags.slice(0, 4).map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="text-xs border-gray-300 text-gray-600 dark:border-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 hover:border-green-500/50 hover:text-green-500 dark:hover:text-green-400 transition-colors theme-transition"
                        >
                          #{tag}
                        </Badge>
                      ))}
                      {post.tags.length > 4 && (
                        <Badge
                          variant="outline"
                          className="text-xs border-gray-300 text-gray-500 dark:border-gray-600 dark:text-gray-500 bg-gray-50 dark:bg-gray-800/50 theme-transition"
                        >
                          +{post.tags.length - 4}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        </DialogPrimitive.Content>
      </DialogPortal>
    </Dialog>
  )
}
