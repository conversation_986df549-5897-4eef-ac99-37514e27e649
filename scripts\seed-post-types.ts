#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createServerSupabaseAdminClient } from '../lib/supabase/server'

// Load environment variables
config({ path: '.env.local' })

const defaultPostTypes = [
  { name: 'Learning', color: '#3B82F6', slug: 'learning', icon: 'BookOpen' },
  { name: 'Error Log', color: '#EF4444', slug: 'error-log', icon: 'AlertCircle' },
  { name: 'Opinion', color: '#8B5CF6', slug: 'opinion', icon: 'MessageCircle' },
  { name: 'Tip', color: '#10B981', slug: 'tip', icon: 'Lightbulb' },
  { name: 'Showcase', color: '#F59E0B', slug: 'showcase', icon: 'Star' },
  { name: 'Photo', color: '#EC4899', slug: 'photo', icon: 'Camera' }
]

async function seedPostTypes() {
  console.log('🌱 Seeding post types...')
  
  try {
    const supabase = createServerSupabaseAdminClient()
    
    // Check existing post types
    const { data: existing, error: fetchError } = await supabase
      .from('post_types')
      .select('slug')
    
    if (fetchError) {
      console.error('❌ Error fetching existing post types:', fetchError)
      return
    }
    
    const existingSlugs = existing?.map(pt => pt.slug) || []
    console.log(`📋 Found ${existingSlugs.length} existing post types`)
    
    // Filter out existing post types
    const newPostTypes = defaultPostTypes.filter(pt => !existingSlugs.includes(pt.slug))
    
    if (newPostTypes.length === 0) {
      console.log('✅ All post types already exist!')
      return
    }
    
    console.log(`📝 Inserting ${newPostTypes.length} new post types...`)
    
    // Insert new post types
    const { data, error } = await supabase
      .from('post_types')
      .insert(newPostTypes)
      .select()
    
    if (error) {
      console.error('❌ Error inserting post types:', error)
      return
    }
    
    console.log('✅ Successfully inserted post types:')
    newPostTypes.forEach(pt => {
      console.log(`   - ${pt.name} (${pt.color})`)
    })
    
    // Show all post types
    const { data: allPostTypes } = await supabase
      .from('post_types')
      .select('*')
      .order('name')
    
    console.log('\n📊 All post types:')
    allPostTypes?.forEach(pt => {
      console.log(`   - ${pt.name}: ${pt.color} (${pt.slug})`)
    })
    
    console.log('\n🎉 Post types seeding completed!')
    
  } catch (error) {
    console.error('❌ Seeding error:', error)
    process.exit(1)
  }
}

// Run the seeding
seedPostTypes()
