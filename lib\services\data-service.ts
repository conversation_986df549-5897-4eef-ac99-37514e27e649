import { Profile, PortfolioProject } from '../types'
import { PostApiSimpleService } from '../../services/post-api-simple.service'
import { profileData } from '../data/profile'
import { portfolioData } from '../data/portfolio'

// Post service - now using Supabase
export const PostService = new PostApiSimpleService()

// Profile service
export class ProfileService {
  static getProfile(): Profile {
    return profileData
  }

  static getProfileSync(): Profile {
    return profileData
  }

  static getExperience() {
    return profileData.experience
  }

  static getAllExperienceSync() {
    return profileData.experience
  }

  static getExperienceStats() {
    const yearsOfExperience = profileData.experience.length > 0 ? 5 : 0 // Could be calculated from dates
    const totalProjects = 15 // Could be calculated from experience projects
    const companiesWorked = profileData.experience.length

    return {
      yearsOfExperience,
      totalProjects,
      companiesWorked
    }
  }
}

// Portfolio service
export class PortfolioService {
  static getAllProjects(): PortfolioProject[] {
    return portfolioData
  }

  static getAllProjectsSync(): PortfolioProject[] {
    return portfolioData
  }

  static getProjectById(id: string): PortfolioProject | undefined {
    return portfolioData.find(project => project.id === id)
  }

  static getProjectsByCategory(category: string): PortfolioProject[] {
    if (category === 'All') return portfolioData
    return portfolioData.filter(project => project.category === category)
  }

  static searchProjects(query: string): PortfolioProject[] {
    const lowercaseQuery = query.toLowerCase()
    return portfolioData.filter(project =>
      project.title.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery) ||
      project.technologies.some(tech => tech.toLowerCase().includes(lowercaseQuery))
    )
  }

  static getPortfolioStats() {
    const totalProjects = portfolioData.length
    const totalUsers = portfolioData.reduce((acc, project) => {
      const users = project.metrics.users?.replace(/[^\d]/g, '') || '0'
      return acc + parseInt(users)
    }, 0)

    const avgUptime = portfolioData
      .filter(project => project.metrics.uptime)
      .reduce((acc, project) => {
        const uptime = parseFloat(project.metrics.uptime?.replace('%', '') || '0')
        return acc + uptime
      }, 0) / portfolioData.filter(project => project.metrics.uptime).length

    const avgPerformance = portfolioData
      .filter(project => project.metrics.performance)
      .reduce((acc, project) => {
        const performance = parseInt(project.metrics.performance?.split('/')[0] || '0')
        return acc + performance
      }, 0) / portfolioData.filter(project => project.metrics.performance).length

    return {
      totalProjects,
      totalUsers: `${Math.floor(totalUsers / 1000)}K+`,
      avgUptime: `${avgUptime.toFixed(1)}%`,
      avgPerformance: `${Math.floor(avgPerformance)}+`
    }
  }

  static getUniqueCategories(): string[] {
    const categories = portfolioData.map(project => project.category)
    return ['All', ...Array.from(new Set(categories))]
  }

  static getUniqueTechnologies(): string[] {
    const technologies = portfolioData.flatMap(project => project.technologies)
    return Array.from(new Set(technologies))
  }
}

// Combined data service
export class DataService {
  static posts = PostService
  static profile = ProfileService
  static portfolio = PortfolioService
  static experience = ProfileService // Experience is part of profile
}
