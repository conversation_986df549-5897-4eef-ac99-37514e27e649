import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { PaginationOptions } from '@/types/api'
import { PostFilters } from '@/types'

const postService = new PostApiSimpleService()

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing posts API without authentication...')

    // Simple pagination
    const pagination: PaginationOptions = {
      page: 1,
      limit: 5,
      sortBy: 'created_at',
      sortOrder: 'desc',
    }

    // No filters
    const filters: PostFilters = {}

    console.log('📋 Calling postService.getAllPosts...')
    
    // Get posts from service
    const result = await postService.getAllPosts(pagination, filters)

    console.log('✅ Posts retrieved successfully:', {
      count: result.posts.length,
      total: result.total
    })

    return ApiResponseBuilder.success(
      {
        posts: result.posts,
        total: result.total,
        message: 'Test API working'
      },
      `Test: Retrieved ${result.posts.length} posts`,
      200
    )
  } catch (error) {
    console.error('❌ Test API error:', error)
    
    return ApiResponseBuilder.internalError(
      `Test API failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    )
  }
}
