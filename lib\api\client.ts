import { ApiResponse, ApiRequestOptions, PaginationOptions } from '@/types/api'

export class ApiClient {
  private baseUrl: string
  private defaultHeaders: Record<string, string>

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  /**
   * Make HTTP Request
   */
  private async request<T>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = 10000,
      retries = 0,
    } = options

    const url = `${this.baseUrl}${endpoint}`
    const requestHeaders = { ...this.defaultHeaders, ...headers }

    const requestOptions: RequestInit = {
      method,
      headers: requestHeaders,
      credentials: 'include', // Include cookies for authentication
    }

    if (body && method !== 'GET') {
      requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body)
    }

    // Add timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      const data: ApiResponse<T> = await response.json()

      if (!response.ok) {
        throw new ApiError(data.error?.message || 'Request failed', response.status, data)
      }

      return data
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof ApiError) {
        throw error
      }

      if (error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408)
      }

      // Retry logic
      if (retries > 0 && this.shouldRetry(error)) {
        await this.delay(1000) // Wait 1 second before retry
        return this.request(endpoint, { ...options, retries: retries - 1 })
      }

      throw new ApiError('Network error', 0, null, error)
    }
  }

  /**
   * GET Request
   */
  async get<T>(
    endpoint: string,
    params?: Record<string, any>,
    options?: Omit<ApiRequestOptions, 'method' | 'body'>
  ): Promise<ApiResponse<T>> {
    let url = endpoint

    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, { ...options, method: 'GET' })
  }

  /**
   * POST Request
   */
  async post<T>(
    endpoint: string,
    body?: any,
    options?: Omit<ApiRequestOptions, 'method'>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body })
  }

  /**
   * PUT Request
   */
  async put<T>(
    endpoint: string,
    body?: any,
    options?: Omit<ApiRequestOptions, 'method'>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body })
  }

  /**
   * PATCH Request
   */
  async patch<T>(
    endpoint: string,
    body?: any,
    options?: Omit<ApiRequestOptions, 'method'>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body })
  }

  /**
   * DELETE Request
   */
  async delete<T>(
    endpoint: string,
    options?: Omit<ApiRequestOptions, 'method' | 'body'>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }

  /**
   * Paginated GET Request
   */
  async getPaginated<T>(
    endpoint: string,
    pagination: PaginationOptions = {},
    options?: Omit<ApiRequestOptions, 'method' | 'body'>
  ): Promise<ApiResponse<T[]>> {
    const params = {
      page: pagination.page || 1,
      limit: pagination.limit || 10,
      sortBy: pagination.sortBy,
      sortOrder: pagination.sortOrder || 'desc',
      search: pagination.search,
      ...pagination.filters,
    }

    return this.get<T[]>(endpoint, params, options)
  }

  /**
   * Helper Methods
   */
  private shouldRetry(error: any): boolean {
    // Retry on network errors or 5xx server errors
    return !error.status || (error.status >= 500 && error.status < 600)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Set Authorization Header
   */
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`
  }

  /**
   * Remove Authorization Header
   */
  removeAuthToken(): void {
    delete this.defaultHeaders['Authorization']
  }
}

/**
 * Custom API Error Class
 */
export class ApiError extends Error {
  public status: number
  public response?: ApiResponse
  public originalError?: any

  constructor(
    message: string,
    status: number = 0,
    response?: ApiResponse,
    originalError?: any
  ) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.response = response
    this.originalError = originalError
  }
}

// Default API client instance
export const apiClient = new ApiClient()
