<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup: Post Interactions System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .sql-code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <h1>🚀 Setup: Post Interactions System</h1>
    
    <div class="container">
        <h2>📋 Overview</h2>
        <p>This system implements functional like and view tracking for posts with the following features:</p>
        
        <div class="feature-list">
            <div class="feature-item">
                <strong>❤️ Multiple Likes</strong><br>
                Users can like posts multiple times
            </div>
            <div class="feature-item">
                <strong>🔴 Visual Feedback</strong><br>
                Filled red heart when user has liked
            </div>
            <div class="feature-item">
                <strong>👁️ View Tracking</strong><br>
                1 view per user per 24 hours
            </div>
            <div class="feature-item">
                <strong>🔄 Real-time Updates</strong><br>
                Counts update automatically
            </div>
            <div class="feature-item">
                <strong>👤 Anonymous Support</strong><br>
                Works for non-logged-in users
            </div>
            <div class="feature-item">
                <strong>⚡ Optimistic UI</strong><br>
                Instant feedback with error handling
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Setup Steps</h2>
        
        <div class="step">
            <h3>Step 1: Create Database Tables</h3>
            <p>Go to <strong>Supabase Dashboard → SQL Editor</strong> and execute this SQL:</p>
            <div class="sql-code">-- Create post_likes table for tracking likes
CREATE TABLE post_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create post_views table for tracking views (1x per 24h)
CREATE TABLE post_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_post_likes_post_id ON post_likes(post_id);
CREATE INDEX idx_post_likes_user_id ON post_likes(user_id);
CREATE INDEX idx_post_likes_identifier ON post_likes(user_identifier);
CREATE INDEX idx_post_likes_post_user ON post_likes(post_id, user_identifier);

CREATE INDEX idx_post_views_post_id ON post_views(post_id);
CREATE INDEX idx_post_views_user_id ON post_views(user_id);
CREATE INDEX idx_post_views_identifier ON post_views(user_identifier);
CREATE INDEX idx_post_views_date ON post_views(viewed_at);

-- Create unique constraint for views (1 per day per user)
CREATE UNIQUE INDEX idx_post_views_unique_daily 
ON post_views(post_id, user_identifier, DATE(viewed_at));</div>
        </div>

        <div class="step">
            <h3>Step 2: Enable Row Level Security</h3>
            <div class="sql-code">-- Enable RLS
ALTER TABLE post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_views ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Allow read access to post_likes" ON post_likes
    FOR SELECT USING (true);

CREATE POLICY "Allow read access to post_views" ON post_views
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to post_likes" ON post_likes
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow insert access to post_views" ON post_views
    FOR INSERT WITH CHECK (true);</div>
        </div>

        <div class="step">
            <h3>Step 3: Create Update Functions</h3>
            <div class="sql-code">-- Create functions to update post counts
CREATE OR REPLACE FUNCTION update_post_reaction_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET reactions_heart = (
        SELECT COUNT(*) 
        FROM post_likes 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_post_view_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET view_count = (
        SELECT COUNT(DISTINCT user_identifier) 
        FROM post_views 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;</div>
        </div>

        <div class="step">
            <h3>Step 4: Create Triggers</h3>
            <div class="sql-code">-- Create trigger functions
CREATE OR REPLACE FUNCTION trigger_update_reaction_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_reaction_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_reaction_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trigger_update_view_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_view_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_view_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_post_likes_count
    AFTER INSERT OR DELETE ON post_likes
    FOR EACH ROW EXECUTE FUNCTION trigger_update_reaction_count();

CREATE TRIGGER trigger_post_views_count
    AFTER INSERT OR DELETE ON post_views
    FOR EACH ROW EXECUTE FUNCTION trigger_update_view_count();</div>
        </div>

        <div class="step">
            <h3>Step 5: Test the System</h3>
            <p>After creating the tables, test the system:</p>
            <a href="http://localhost:3000/api/test/post-interactions" class="link" target="_blank">Test API</a>
            <a href="http://localhost:3000/posts/getting-started-with-react-hooks" class="link" target="_blank">Test Post Page</a>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Testing Instructions</h2>
        
        <div class="step">
            <h3>Test Like Functionality</h3>
            <ol>
                <li>Open a post page</li>
                <li>Click the floating heart button</li>
                <li>Verify heart fills red and count increases</li>
                <li>Click multiple times to test multiple likes</li>
                <li>Refresh page to verify state persists</li>
            </ol>
        </div>

        <div class="step">
            <h3>Test View Functionality</h3>
            <ol>
                <li>Open a post page (view should auto-record)</li>
                <li>Check view count increases</li>
                <li>Refresh page multiple times</li>
                <li>Verify view count doesn't increase (24h limit)</li>
                <li>Test with different browsers/incognito</li>
            </ol>
        </div>

        <div class="step">
            <h3>API Testing</h3>
            <p>Test individual endpoints:</p>
            <div class="sql-code">// Like a post
POST /api/posts/{postId}/like

// Get like status
GET /api/posts/{postId}/like

// Record a view
POST /api/posts/{postId}/view

// Get view status
GET /api/posts/{postId}/view

// Get complete interaction status
GET /api/posts/{postId}/interactions</div>
        </div>
    </div>

    <div class="container">
        <h2>✅ Success Criteria</h2>
        <ul>
            <li>✅ Tables created successfully in Supabase</li>
            <li>✅ RLS policies working</li>
            <li>✅ Triggers updating post counts automatically</li>
            <li>✅ Like button shows filled red heart when liked</li>
            <li>✅ Multiple likes allowed and counted</li>
            <li>✅ Views recorded only once per 24h per user</li>
            <li>✅ Real-time count updates in UI</li>
            <li>✅ Anonymous user support working</li>
            <li>✅ API endpoints responding correctly</li>
        </ul>
    </div>

    <div class="container">
        <h2>🔍 Troubleshooting</h2>
        
        <div class="warning">
            <strong>⚠️ Common Issues:</strong><br>
            • <strong>Table creation fails:</strong> Check foreign key references exist<br>
            • <strong>RLS errors:</strong> Ensure policies are created correctly<br>
            • <strong>Trigger not working:</strong> Verify functions exist first<br>
            • <strong>API errors:</strong> Check Supabase connection and permissions
        </div>

        <div class="success">
            <strong>✅ Verification Steps:</strong><br>
            1. Check tables exist in Supabase Dashboard<br>
            2. Test API endpoints return success<br>
            3. Verify UI shows like/view counts<br>
            4. Test multiple interactions work correctly
        </div>
    </div>
</body>
</html>
