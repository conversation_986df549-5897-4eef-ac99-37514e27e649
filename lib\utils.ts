import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utility function to format dates consistently
export function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Utility function to truncate text
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength).trim() + '...'
}

// Utility function to extract content preview from markdown
export function extractContentPreview(content: string, maxLength: number = 200): string {
  const textContent = content
    .split('\n')
    .find(line => line.trim() && !line.startsWith('#'))
    ?.substring(0, maxLength) || ''

  return truncateText(textContent, maxLength)
}
