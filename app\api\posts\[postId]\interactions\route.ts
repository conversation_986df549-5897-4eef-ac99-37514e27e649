import { NextRequest } from 'next/server'
import { PostInteractionsService } from '@/services/post-interactions.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

interface RouteParams {
  params: Promise<{
    postId: string
  }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params
    console.log('📊 Getting complete interaction status for post:', postId)

    // Get user ID from auth (if authenticated)
    const userId = undefined // TODO: Get from auth when implemented

    const interactionsService = new PostInteractionsService()
    const result = await interactionsService.getPostInteractionStatus(postId, userId)

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || 'Failed to get interaction status')
    }

    return ApiResponseBuilder.success(
      result.data,
      'Interaction status retrieved successfully'
    )

  } catch (error) {
    console.error('💥 Error in interactions endpoint:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to get interaction status'
    )
  }
}
