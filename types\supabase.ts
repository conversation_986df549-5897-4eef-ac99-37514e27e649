// Supabase Database Types
// These will be generated automatically when we create the database schema

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          name: string
          avatar_url: string | null
          bio: string | null
          website: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name: string
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      post_types: {
        Row: {
          id: string
          name: string
          color: string // Hex color like #3B82F6
          slug: string
          icon: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          color: string // Hex color like #3B82F6
          slug: string
          icon?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          color?: string // Hex color like #3B82F6
          slug?: string
          icon?: string
          created_at?: string
          updated_at?: string
        }
      }
      posts: {
        Row: {
          id: string
          title: string
          slug: string
          content: string
          version: string
          author_id: string
          type_id: string
          status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
          excerpt: boolean
          featured: boolean
          tags: string[]
          reactions_heart: number
          comments_count: number
          view_count: number
          read_time: string
          published_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content: string
          version?: string
          author_id: string
          type_id: string
          status?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
          excerpt?: boolean
          featured?: boolean
          tags?: string[]
          reactions_heart?: number
          comments_count?: number
          view_count?: number
          read_time?: string
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string
          version?: string
          author_id?: string
          type_id?: string
          status?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
          excerpt?: boolean
          featured?: boolean
          tags?: string[]
          reactions_heart?: number
          comments_count?: number
          view_count?: number
          read_time?: string
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      post_images: {
        Row: {
          id: string
          post_id: string
          url: string
          alt_text: string | null
          caption: string | null
          width: number | null
          height: number | null
          display_order: number
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          url: string
          alt_text?: string | null
          caption?: string | null
          width?: number | null
          height?: number | null
          display_order?: number
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          url?: string
          alt_text?: string | null
          caption?: string | null
          width?: number | null
          height?: number | null
          display_order?: number
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      post_status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
