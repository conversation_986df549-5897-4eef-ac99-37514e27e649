<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Migration: Add file_name and file_size</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .step {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        .query-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .link {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🗄️ Supabase Migration: Add file_name and file_size to post_images</h1>
    
    <div class="container">
        <div class="success">
            <h2>✅ Migration Goal</h2>
            <p>Menambahkan field <strong>file_name</strong> dan <strong>file_size</strong> ke tabel <code>post_images</code> untuk menyimpan informasi file yang lebih lengkap.</p>
        </div>
    </div>

    <div class="container">
        <h2>📋 Prerequisites</h2>
        <ul>
            <li>✅ Access ke Supabase Dashboard</li>
            <li>✅ Permission untuk modify database schema</li>
            <li>✅ Backup database (recommended)</li>
        </ul>
    </div>

    <div class="container">
        <h2>🚀 Step-by-Step Migration</h2>
        
        <div class="step">
            <h3>Step 1: Login ke Supabase Dashboard</h3>
            <ol>
                <li>Go to <a href="https://supabase.com/dashboard" target="_blank">Supabase Dashboard</a></li>
                <li>Login dengan akun Anda</li>
                <li>Pilih project yang sesuai</li>
                <li>Go to <strong>SQL Editor</strong> di sidebar kiri</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Check Current Schema</h3>
            <p>Jalankan query ini untuk melihat struktur tabel saat ini:</p>
            <div class="query-box">
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'post_images' 
ORDER BY ordinal_position;</div>
            <p><strong>Expected Result:</strong> Anda akan melihat kolom yang ada tanpa <code>file_name</code> dan <code>file_size</code></p>
        </div>

        <div class="step">
            <h3>Step 3: Check Existing Data</h3>
            <p>Lihat data yang sudah ada:</p>
            <div class="query-box">
SELECT 
    id,
    post_id,
    url,
    alt_text,
    caption,
    width,
    height,
    display_order,
    created_at
FROM post_images 
ORDER BY created_at DESC 
LIMIT 10;</div>
            <p><strong>Note:</strong> Catat berapa banyak records yang ada untuk update nanti</p>
        </div>

        <div class="step">
            <h3>Step 4: Add file_name Column</h3>
            <p>Tambahkan kolom file_name:</p>
            <div class="query-box">
ALTER TABLE post_images 
ADD COLUMN file_name TEXT;</div>
            <div class="success">
                <strong>✅ Expected Result:</strong> "Success. No rows returned"
            </div>
        </div>

        <div class="step">
            <h3>Step 5: Add file_size Column</h3>
            <p>Tambahkan kolom file_size:</p>
            <div class="query-box">
ALTER TABLE post_images 
ADD COLUMN file_size BIGINT;</div>
            <div class="success">
                <strong>✅ Expected Result:</strong> "Success. No rows returned"
            </div>
        </div>

        <div class="step">
            <h3>Step 6: Verify New Schema</h3>
            <p>Pastikan kolom baru sudah ditambahkan:</p>
            <div class="query-box">
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'post_images' 
ORDER BY ordinal_position;</div>
            <div class="success">
                <strong>✅ Expected Result:</strong> Anda akan melihat <code>file_name</code> (text) dan <code>file_size</code> (bigint) di list
            </div>
        </div>

        <div class="step">
            <h3>Step 7: Update Existing Data (Optional)</h3>
            <p>Update records yang sudah ada dengan default values:</p>
            
            <h4>Update file_name:</h4>
            <div class="query-box">
UPDATE post_images 
SET file_name = CONCAT('image-', display_order, '.jpg')
WHERE file_name IS NULL;</div>

            <h4>Update file_size:</h4>
            <div class="query-box">
UPDATE post_images 
SET file_size = 1024
WHERE file_size IS NULL;</div>
            
            <div class="warning">
                <strong>⚠️ Note:</strong> Ini akan update semua existing records dengan default values
            </div>
        </div>

        <div class="step">
            <h3>Step 8: Add Constraint (Optional)</h3>
            <p>Tambahkan constraint untuk memastikan file_size positif:</p>
            <div class="query-box">
ALTER TABLE post_images 
ADD CONSTRAINT check_file_size_positive 
CHECK (file_size > 0);</div>
        </div>

        <div class="step">
            <h3>Step 9: Verify Final Result</h3>
            <p>Check data dengan kolom baru:</p>
            <div class="query-box">
SELECT 
    id,
    post_id,
    url,
    file_name,    -- NEW FIELD
    file_size,    -- NEW FIELD
    alt_text,
    caption,
    display_order,
    created_at
FROM post_images 
ORDER BY created_at DESC 
LIMIT 5;</div>
            <div class="success">
                <strong>✅ Expected Result:</strong> Data dengan kolom file_name dan file_size yang terisi
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Test Migration</h2>
        
        <div class="step">
            <h3>Test Insert New Record</h3>
            <p>Test insert record baru dengan field lengkap:</p>
            <div class="query-box">
INSERT INTO post_images (
    id,
    post_id,
    url,
    alt_text,
    caption,
    file_name,
    file_size,
    width,
    height,
    display_order
) VALUES (
    gen_random_uuid(),
    (SELECT id FROM posts LIMIT 1), -- Use existing post ID
    'https://picsum.photos/800/600?random=999',
    'Test migration image',
    'Test caption for migration',
    'test-migration.jpg',
    2048,
    800,
    600,
    999
);</div>
        </div>

        <div class="step">
            <h3>Test Select with JOIN</h3>
            <p>Test query dengan JOIN ke posts:</p>
            <div class="query-box">
SELECT 
    p.title as post_title,
    pi.file_name,
    pi.file_size,
    pi.url,
    pi.alt_text,
    pi.caption
FROM post_images pi
JOIN posts p ON pi.post_id = p.id
WHERE pi.file_name = 'test-migration.jpg';</div>
        </div>

        <div class="step">
            <h3>Cleanup Test Data</h3>
            <p>Hapus test data setelah selesai:</p>
            <div class="query-box">
DELETE FROM post_images 
WHERE file_name = 'test-migration.jpg';</div>
        </div>
    </div>

    <div class="container">
        <h2>⚠️ Important Notes</h2>
        
        <div class="warning">
            <h3>🔒 Safety Precautions</h3>
            <ul>
                <li><strong>Backup First:</strong> Selalu backup database sebelum migration</li>
                <li><strong>Test Environment:</strong> Test di development environment dulu</li>
                <li><strong>Downtime:</strong> Migration ini minimal downtime</li>
                <li><strong>Rollback Plan:</strong> Siapkan rollback plan jika ada masalah</li>
            </ul>
        </div>

        <div class="danger">
            <h3>🚨 Rollback (Emergency Only)</h3>
            <p>Jika ada masalah dan perlu rollback:</p>
            <div class="query-box">
-- DANGER: This will delete the new columns and data!
ALTER TABLE post_images DROP COLUMN file_name;
ALTER TABLE post_images DROP COLUMN file_size;
ALTER TABLE post_images DROP CONSTRAINT IF EXISTS check_file_size_positive;</div>
            <p><strong>⚠️ WARNING:</strong> Rollback akan menghapus semua data di kolom baru!</p>
        </div>
    </div>

    <div class="container">
        <h2>✅ After Migration</h2>
        
        <div class="success">
            <h3>🎯 What to Expect</h3>
            <ul>
                <li><strong>New Posts:</strong> File name dan size akan tersimpan otomatis</li>
                <li><strong>Existing Posts:</strong> Akan menggunakan default values</li>
                <li><strong>Edit Form:</strong> Akan menampilkan file name dan size yang benar</li>
                <li><strong>API Response:</strong> Akan include file_name dan file_size</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔄 Update Application Code</h3>
            <p>Setelah migration, aplikasi akan otomatis menggunakan field baru karena kode sudah diupdate untuk:</p>
            <ul>
                <li>✅ Menyimpan file_name dan file_size saat create post</li>
                <li>✅ Membaca file_name dan file_size saat fetch post</li>
                <li>✅ Menampilkan informasi yang akurat di edit form</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Ready to Migrate!</h2>
        <p>Setelah migration selesai, images di edit form akan menampilkan file name dan size yang akurat.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="https://supabase.com/dashboard" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">🗄️ Open Supabase Dashboard</a>
        </div>
        
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center; margin: 20px 0;">
            <strong>🎯 Migration Goal:</strong> Add file_name and file_size fields to post_images table
        </div>
    </div>
</body>
</html>
