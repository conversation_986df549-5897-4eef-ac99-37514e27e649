import { NextRequest } from 'next/server'
import { PostInteractionsService } from '@/services/post-interactions.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing post interactions system...')

    const interactionsService = new PostInteractionsService()
    
    // Test with a known post ID (React Hooks post)
    const testPostId = 'da8cadac-5531-4c34-9330-42ed134dd7a3'
    
    console.log('📊 Getting interaction status for test post:', testPostId)
    
    const statusResult = await interactionsService.getPostInteractionStatus(testPostId)
    
    if (!statusResult.success) {
      return ApiResponseBuilder.badRequest(statusResult.error || 'Failed to get interaction status')
    }

    return ApiResponseBuilder.success(
      {
        testPostId,
        interactionStatus: statusResult.data,
        systemStatus: {
          serviceWorking: true,
          repositoryConnected: true,
          supabaseConnected: true
        },
        testInstructions: {
          likeEndpoint: `/api/posts/${testPostId}/like`,
          viewEndpoint: `/api/posts/${testPostId}/view`,
          interactionsEndpoint: `/api/posts/${testPostId}/interactions`,
          testSteps: [
            '1. POST to like endpoint to add a like',
            '2. GET like endpoint to check like status',
            '3. POST to view endpoint to record a view',
            '4. GET interactions endpoint for complete status'
          ]
        }
      },
      'Post interactions system test completed'
    )

  } catch (error) {
    console.error('💥 Error testing post interactions:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to test post interactions'
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, postId } = body

    if (!action || !postId) {
      return ApiResponseBuilder.badRequest('Missing action or postId')
    }

    console.log(`🧪 Testing ${action} for post:`, postId)

    const interactionsService = new PostInteractionsService()
    let result

    switch (action) {
      case 'like':
        result = await interactionsService.likePost(postId)
        break
      case 'view':
        result = await interactionsService.viewPost(postId)
        break
      case 'status':
        result = await interactionsService.getPostInteractionStatus(postId)
        break
      default:
        return ApiResponseBuilder.badRequest(`Unknown action: ${action}`)
    }

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || `Failed to ${action}`)
    }

    return ApiResponseBuilder.success(
      {
        action,
        postId,
        result: result.data,
        timestamp: new Date().toISOString()
      },
      `${action} test completed successfully`
    )

  } catch (error) {
    console.error('💥 Error in post interactions test:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to test interaction'
    )
  }
}
