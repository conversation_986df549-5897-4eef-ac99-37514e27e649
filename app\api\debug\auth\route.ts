import { NextRequest } from 'next/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { getAuthenticatedUserFromRequest, getAuthenticatedUserFromCookies } from '@/lib/supabase/auth'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug authentication...')
    
    // Check cookies
    const cookieHeader = request.headers.get('cookie')
    console.log('Cookies:', cookieHeader ? 'Present' : 'None')
    
    // Check authorization header
    const authHeader = request.headers.get('authorization')
    console.log('Authorization header:', authHeader ? authHeader.substring(0, 20) + '...' : 'None')
    
    // Try to get user from cookies
    console.log('Trying cookie-based auth...')
    const userFromCookies = await getAuthenticatedUserFromCookies(request)
    console.log('User from cookies:', userFromCookies ? `${userFromCookies.email} (${userFromCookies.id})` : 'None')
    
    // Try to get user from request (combined method)
    console.log('Trying combined auth method...')
    const userFromRequest = await getAuthenticatedUserFromRequest(request)
    console.log('User from request:', userFromRequest ? `${userFromRequest.email} (${userFromRequest.id})` : 'None')
    
    // Try direct Supabase session
    console.log('Trying direct Supabase session...')
    const supabase = createServerSupabaseClient()
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    console.log('Supabase session:', session ? `User: ${session.user.email}` : 'None')
    console.log('Session error:', sessionError?.message || 'None')
    
    return ApiResponseBuilder.success({
      cookiesPresent: !!cookieHeader,
      authHeaderPresent: !!authHeader,
      userFromCookies: userFromCookies ? {
        id: userFromCookies.id,
        email: userFromCookies.email,
        name: userFromCookies.name
      } : null,
      userFromRequest: userFromRequest ? {
        id: userFromRequest.id,
        email: userFromRequest.email,
        name: userFromRequest.name
      } : null,
      supabaseSession: session ? {
        userId: session.user.id,
        userEmail: session.user.email,
        expiresAt: session.expires_at
      } : null,
      sessionError: sessionError?.message || null
    }, 'Authentication debug info')
    
  } catch (error) {
    console.error('Debug auth error:', error)
    return ApiResponseBuilder.internalError('Debug failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
