import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { LanguageSwitcher } from "@/components/navigation/language-switcher"
import { ArrowLeft, Share, Edit, MoreHorizontal, BookOpen } from "lucide-react"
import Link from "next/link"
import { buttonVariants } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"

interface PostHeaderProps {
  title: string
  locale: string
  translations: {
    backToPosts: string
    share: string
    edit: string
    copyLink: string
    report: string
  }
  onShare?: () => void
  onEdit?: () => void
  onCopyLink?: () => void
  onReport?: () => void
}

/**
 * Sticky header component for post detail pages
 */
export function PostHeader({
  title,
  locale,
  translations,
  onShare,
  onEdit,
  onCopyLink,
  onReport
}: PostHeaderProps) {
  return (
    <div className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800 theme-transition">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/${locale}`}>
              <Button
                variant="ghost"
                size="sm"
                className={cn(buttonVariants.action.ghost)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {translations.backToPosts}
              </Button>
            </Link>
            <div className="h-6 w-px bg-gray-300 dark:bg-gray-700 theme-transition" />
            <div className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-green-500 dark:text-green-400" />
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-1 theme-transition">
                {title}
              </h1>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <LanguageSwitcher />
            <ThemeSwitcher />

            {onShare && (
              <Button
                variant="outline"
                size="sm"
                onClick={onShare}
                className={cn(buttonVariants.action.secondary)}
              >
                <Share className="w-4 h-4 mr-2" />
                {translations.share}
              </Button>
            )}
            
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className={cn(buttonVariants.action.secondary)}
              >
                <Edit className="w-4 h-4 mr-2" />
                {translations.edit}
              </Button>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(buttonVariants.action.secondary)}
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800 theme-transition">
                {onCopyLink && (
                  <DropdownMenuItem 
                    onClick={onCopyLink}
                    className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 theme-transition"
                  >
                    {translations.copyLink}
                  </DropdownMenuItem>
                )}
                {onReport && (
                  <DropdownMenuItem 
                    onClick={onReport}
                    className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 theme-transition"
                  >
                    {translations.report}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>
  )
}
