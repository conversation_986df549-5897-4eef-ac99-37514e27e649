import { NextRequest } from 'next/server'
import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { withError<PERSON>and<PERSON>, withLogging, withRateLimit } from '@/lib/api/middleware'

async function getPostTypesHandler(_request: NextRequest) {
  try {
    console.log('🧪 Testing post types endpoint...')
    const supabase = createServerSupabaseAdminClient()

    // Get all post types
    const { data: postTypes, error } = await supabase
      .from('post_types')
      .select('*')
      .order('name', { ascending: true })

    if (error) {
      console.error('Supabase error fetching post types:', error)
      return ApiResponseBuilder.databaseError('Failed to fetch post types', { supabaseError: error })
    }

    // Transform to frontend format
    const transformedTypes = postTypes.map(type => ({
      id: type.id,
      name: type.name,
      slug: type.slug,
      color: type.color,
      icon: type.icon,
      createdAt: type.created_at,
      updatedAt: type.updated_at
    }))

    console.log(`✅ Found ${transformedTypes.length} post types`)

    return ApiResponseBuilder.success(
      transformedTypes,
      `Retrieved ${transformedTypes.length} post types`
    )
  } catch (error) {
    console.error('Error fetching post types:', error)
    return ApiResponseBuilder.internalError('Failed to fetch post types', { error: error instanceof Error ? error.message : 'Unknown error' })
  }
}

// Apply middleware without authentication (for testing)
export const GET = withErrorHandler(
  withLogging(
    withRateLimit(100, 15 * 60 * 1000)(getPostTypesHandler) // 100 requests per 15 minutes
  )
)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
