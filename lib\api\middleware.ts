import { NextRequest, NextResponse } from 'next/server'
import { ApiResponseBuilder } from './response-builder'
import { ErrorCode } from '@/types/api'

export type ApiHandler = (request: NextRequest, context?: any) => Promise<NextResponse>

/**
 * API Error Handler Middleware
 */
export function withErrorHandler(handler: ApiHandler): ApiHandler {
  return async (request: NextRequest, context?: any) => {
    try {
      return await handler(request, context)
    } catch (error) {
      console.error('API Error:', error)

      // Handle different types of errors
      if (error instanceof ApiValidationError) {
        return ApiResponseBuilder.validationError(
          error.message,
          error.field,
          error.details
        )
      }

      if (error instanceof ApiAuthError) {
        return ApiResponseBuilder.unauthorized(error.message)
      }

      if (error instanceof ApiNotFoundError) {
        return ApiResponseBuilder.notFound(error.message)
      }

      if (error instanceof ApiConflictError) {
        return ApiResponseBuilder.conflict(error.message)
      }

      if (error instanceof ApiRateLimitError) {
        return ApiResponseBuilder.rateLimitExceeded(error.message)
      }

      // Database errors
      if (error.code === 'P2002') { // Prisma unique constraint
        return ApiResponseBuilder.conflict('Resource already exists')
      }

      if (error.code === 'P2025') { // Prisma record not found
        return ApiResponseBuilder.notFound('Resource not found')
      }

      // Generic database error
      if (error.code?.startsWith('P')) {
        return ApiResponseBuilder.databaseError(
          'Database operation failed',
          { code: error.code, meta: error.meta }
        )
      }

      // Default to internal server error
      return ApiResponseBuilder.internalError(
        process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
        process.env.NODE_ENV === 'development' ? { stack: error.stack } : undefined
      )
    }
  }
}

/**
 * CORS Middleware
 */
export function withCors(handler: ApiHandler): ApiHandler {
  return async (request: NextRequest, context?: any) => {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400',
        },
      })
    }

    const response = await handler(request, context)

    // Add CORS headers to response
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    return response
  }
}

/**
 * Rate Limiting Middleware
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Clear rate limit store periodically to prevent memory leaks
setInterval(() => {
  const now = Date.now()
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000) // Clean up every 5 minutes

export function withRateLimit(
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
) {
  return function (handler: ApiHandler): ApiHandler {
    return async (request: NextRequest, context?: any) => {
      const ip = getClientIP(request)
      const now = Date.now()
      const record = rateLimitStore.get(ip)

      if (!record || now > record.resetTime) {
        rateLimitStore.set(ip, { count: 1, resetTime: now + windowMs })
      } else {
        record.count++
        if (record.count > maxRequests) {
          throw new ApiRateLimitError('Rate limit exceeded')
        }
      }

      return handler(request, context)
    }
  }
}

/**
 * Request Logging Middleware
 */
export function withLogging(handler: ApiHandler): ApiHandler {
  return async (request: NextRequest, context?: any) => {
    const start = Date.now()
    const method = request.method
    const url = request.url
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const ip = getClientIP(request)

    console.log(`[${new Date().toISOString()}] ${method} ${url} - ${ip} - ${userAgent}`)

    try {
      const response = await handler(request, context)
      const duration = Date.now() - start
      const status = response.status

      console.log(`[${new Date().toISOString()}] ${method} ${url} - ${status} - ${duration}ms`)

      return response
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[${new Date().toISOString()}] ${method} ${url} - ERROR - ${duration}ms:`, error)
      throw error
    }
  }
}

/**
 * Combine multiple middlewares
 */
export function withMiddleware(...middlewares: ((handler: ApiHandler) => ApiHandler)[]): (handler: ApiHandler) => ApiHandler {
  return (handler: ApiHandler) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler)
  }
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

/**
 * Custom Error Classes
 */
export class ApiValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'ApiValidationError'
  }
}

export class ApiAuthError extends Error {
  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'ApiAuthError'
  }
}

export class ApiNotFoundError extends Error {
  constructor(message: string = 'Resource not found') {
    super(message)
    this.name = 'ApiNotFoundError'
  }
}

export class ApiConflictError extends Error {
  constructor(message: string = 'Resource conflict') {
    super(message)
    this.name = 'ApiConflictError'
  }
}

export class ApiRateLimitError extends Error {
  constructor(message: string = 'Rate limit exceeded') {
    super(message)
    this.name = 'ApiRateLimitError'
  }
}
