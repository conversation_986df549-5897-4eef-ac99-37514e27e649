// Mock Post Repository for API development
import { Post, PostStatus, PostType, PostReactions, CreatePostData, UpdatePostData, PostFilters } from '@/types'
import { PaginationOptions } from '@/types/api'

export class PostMockRepository {
  private posts: Post[] = []
  private postTypes: PostType[] = []

  constructor() {
    this.initializeMockData()
  }

  /**
   * Find all posts with pagination and filtering
   */
  async findAll(
    pagination: PaginationOptions = {},
    filters: PostFilters = {}
  ): Promise<{ posts: Post[]; total: number }> {
    let filteredPosts = [...this.posts]

    // Apply filters
    if (filters.status) {
      filteredPosts = filteredPosts.filter(post => post.status === filters.status)
    }

    if (filters.type) {
      filteredPosts = filteredPosts.filter(post => post.type.slug === filters.type)
    }

    if (filters.featured !== undefined) {
      filteredPosts = filteredPosts.filter(post => post.featured === filters.featured)
    }

    if (filters.author) {
      filteredPosts = filteredPosts.filter(post => 
        post.author.toLowerCase().includes(filters.author!.toLowerCase())
      )
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(searchTerm) ||
        post.content.toLowerCase().includes(searchTerm) ||
        post.excerpt.toLowerCase().includes(searchTerm) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    // Apply sorting
    const sortBy = pagination.sortBy || 'createdAt'
    const sortOrder = pagination.sortOrder || 'desc'

    filteredPosts.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Post]
      let bValue: any = b[sortBy as keyof Post]

      // Handle nested properties
      if (sortBy === 'views') {
        aValue = a.viewCount
        bValue = b.viewCount
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply pagination
    const page = pagination.page || 1
    const limit = pagination.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    const paginatedPosts = filteredPosts.slice(startIndex, endIndex)

    return {
      posts: paginatedPosts,
      total: filteredPosts.length
    }
  }

  /**
   * Find post by ID
   */
  async findById(id: string): Promise<Post | null> {
    return this.posts.find(post => post.id === id) || null
  }

  /**
   * Find post by slug
   */
  async findBySlug(slug: string): Promise<Post | null> {
    return this.posts.find(post => post.slug === slug) || null
  }

  /**
   * Get posts statistics
   */
  async getStats(): Promise<{
    totalPosts: number
    publishedPosts: number
    draftPosts: number
    archivedPosts: number
    totalViews: number
    totalReactions: number
    totalComments: number
    postsByType: { type: string; count: number }[]
    mostViewedPosts: Pick<Post, 'id' | 'title' | 'viewCount' | 'slug'>[]
    recentPosts: Pick<Post, 'id' | 'title' | 'status' | 'createdAt'>[]
    growthMetrics: { thisMonth: number; lastMonth: number; growth: number }
  }> {
    const now = new Date()
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)

    const publishedPosts = this.posts.filter(post => post.status === PostStatus.PUBLISHED)
    const draftPosts = this.posts.filter(post => post.status === PostStatus.DRAFT)
    const archivedPosts = this.posts.filter(post => post.status === PostStatus.ARCHIVED)

    const totalViews = this.posts.reduce((sum, post) => sum + post.viewCount, 0)
    const totalReactions = this.posts.reduce((sum, post) => 
      sum + post.reactions.thumbsUp + post.reactions.heart + post.reactions.brain, 0
    )
    const totalComments = this.posts.reduce((sum, post) => sum + post.comments, 0)

    // Posts by type
    const typeCount = new Map<string, number>()
    this.posts.forEach(post => {
      const typeName = post.type.name
      typeCount.set(typeName, (typeCount.get(typeName) || 0) + 1)
    })

    const postsByType = Array.from(typeCount.entries()).map(([type, count]) => ({
      type,
      count
    }))

    // Most viewed posts
    const mostViewedPosts = [...this.posts]
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, 5)
      .map(post => ({
        id: post.id,
        title: post.title,
        viewCount: post.viewCount,
        slug: post.slug
      }))

    // Recent posts
    const recentPosts = [...this.posts]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10)
      .map(post => ({
        id: post.id,
        title: post.title,
        status: post.status,
        createdAt: post.createdAt
      }))

    // Growth metrics
    const thisMonthPosts = this.posts.filter(post => 
      new Date(post.createdAt) >= thisMonth
    ).length

    const lastMonthPosts = this.posts.filter(post => {
      const createdAt = new Date(post.createdAt)
      return createdAt >= lastMonth && createdAt < thisMonth
    }).length

    const growth = lastMonthPosts > 0 
      ? ((thisMonthPosts - lastMonthPosts) / lastMonthPosts) * 100 
      : thisMonthPosts > 0 ? 100 : 0

    return {
      totalPosts: this.posts.length,
      publishedPosts: publishedPosts.length,
      draftPosts: draftPosts.length,
      archivedPosts: archivedPosts.length,
      totalViews,
      totalReactions,
      totalComments,
      postsByType,
      mostViewedPosts,
      recentPosts,
      growthMetrics: {
        thisMonth: thisMonthPosts,
        lastMonth: lastMonthPosts,
        growth: Math.round(growth * 100) / 100
      }
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * Initialize with mock data
   */
  private initializeMockData(): void {
    // Initialize post types
    this.postTypes = [
      {
        id: '1',
        name: 'Tutorial',
        color: '#3B82F6',
        icon: '📚',
        slug: 'tutorial',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: 'Article',
        color: '#10B981',
        icon: '📄',
        slug: 'article',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '3',
        name: 'Opinion',
        color: '#8B5CF6',
        icon: '💭',
        slug: 'opinion',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '4',
        name: 'Error Log',
        color: '#EF4444',
        icon: '🐛',
        slug: 'error-log',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ]

    // Initialize posts
    this.posts = [
      {
        id: '1',
        title: 'Getting Started with Next.js 14',
        slug: 'getting-started-nextjs-14',
        content: 'Next.js 14 introduces several new features including Server Components, improved performance, and better developer experience. In this comprehensive tutorial, we\'ll explore how to build modern web applications with Next.js 14.',
        excerpt: 'Learn how to build modern web applications with Next.js 14 and its new features including Server Components and improved performance.',
        version: '1.0.0',
        type: this.postTypes[0],
        author: 'Rijal Solahudin',
        status: PostStatus.PUBLISHED,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        publishedAt: '2024-01-15T10:00:00Z',
        reactions: { thumbsUp: 45, heart: 23, brain: 12 },
        comments: 8,
        viewCount: 1234,
        tags: ['Next.js', 'React', 'Web Development', 'Tutorial'],
        featured: true,
        readTime: '8 min read',
        images: []
      },
      {
        id: '2',
        title: 'Building a Personal Portfolio with React',
        slug: 'building-personal-portfolio-react',
        content: 'Creating a stunning personal portfolio is essential for showcasing your skills and projects. This guide will walk you through building a modern, responsive portfolio using React and modern design principles.',
        excerpt: 'Step-by-step guide to creating a stunning personal portfolio website using React and modern design principles.',
        version: '1.0.0',
        type: this.postTypes[1],
        author: 'Rijal Solahudin',
        status: PostStatus.DRAFT,
        createdAt: '2024-01-10T14:30:00Z',
        updatedAt: '2024-01-12T09:15:00Z',
        reactions: { thumbsUp: 23, heart: 15, brain: 8 },
        comments: 5,
        viewCount: 567,
        tags: ['React', 'Portfolio', 'Design', 'Frontend'],
        featured: false,
        readTime: '12 min read',
        images: []
      }
    ]

    // Add more mock posts
    for (let i = 3; i <= 25; i++) {
      const randomType = this.postTypes[Math.floor(Math.random() * this.postTypes.length)]
      const statuses = [PostStatus.PUBLISHED, PostStatus.DRAFT, PostStatus.ARCHIVED]
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
      
      this.posts.push({
        id: i.toString(),
        title: `Sample Post ${i}: Advanced Web Development Techniques`,
        slug: `sample-post-${i}-advanced-web-development`,
        content: `This is a comprehensive article about advanced web development techniques. It covers various topics including performance optimization, security best practices, and modern development workflows.`,
        excerpt: `Learn about advanced web development techniques and best practices in this detailed article.`,
        version: '1.0.0',
        type: randomType,
        author: 'Rijal Solahudin',
        status: randomStatus,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        publishedAt: randomStatus === PostStatus.PUBLISHED ? new Date(Date.now() - Math.random() * 20 * 24 * 60 * 60 * 1000).toISOString() : undefined,
        reactions: { 
          thumbsUp: Math.floor(Math.random() * 50), 
          heart: Math.floor(Math.random() * 30), 
          brain: Math.floor(Math.random() * 20) 
        },
        comments: Math.floor(Math.random() * 15),
        viewCount: Math.floor(Math.random() * 2000),
        tags: ['Web Development', 'JavaScript', 'React', 'Tutorial'],
        featured: Math.random() > 0.8,
        readTime: `${Math.floor(Math.random() * 15) + 5} min read`,
        images: []
      })
    }
  }
}
