<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload with Alt Text & Caption</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-case h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .expected {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🖼️ Test: Image Upload with Alt Text & Caption</h1>
    
    <div class="instructions">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li><strong>Login first:</strong> Make sure you're logged in to the dashboard</li>
            <li><strong>Open create post page</strong></li>
            <li><strong>Upload images with alt text and caption</strong></li>
            <li><strong>Submit form and check database</strong></li>
            <li><strong>Check network tab</strong> for image data in request</li>
        </ol>
    </div>

    <a href="http://localhost:3001/admin-access" class="link" target="_blank">🔐 Login Page</a>
    <a href="http://localhost:3001/dashboard/posts/new" class="link" target="_blank">📝 Create Post Page</a>

    <div class="test-container">
        <h2>🔍 Test Cases</h2>

        <div class="test-case">
            <h3>Test Case 1: Upload Images with Alt Text & Caption</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Login and go to create post page</li>
                <li>Fill basic post data:
                    <ul>
                        <li>Title: "Test Post with Images"</li>
                        <li>Content: "This post contains images with alt text and captions"</li>
                        <li>Select a post type</li>
                    </ul>
                </li>
                <li>Scroll to Images section</li>
                <li>Upload 2-3 images</li>
                <li>For each image, click edit button and add:
                    <ul>
                        <li>Alt Text: "Description for screen readers"</li>
                        <li>Caption: "Descriptive caption for the image"</li>
                    </ul>
                </li>
                <li>Open browser dev tools → Network tab</li>
                <li>Click "Save Draft" button</li>
                <li>Check the network request payload</li>
            </ol>
            <div class="expected">
                <strong>Expected Request Payload:</strong><br>
                <div class="code">
{
  "title": "Test Post with Images",
  "slug": "test-post-with-images",
  "content": "This post contains images...",
  "typeId": "...",
  "status": "DRAFT",
  "featured": false,
  "showFullContent": false,
  "tags": [],
  "images": [
    {
      "url": "blob:http://localhost:3001/...",
      "altText": "Description for screen readers",
      "caption": "Descriptive caption for the image",
      "name": "image1.jpg",
      "size": 123456
    },
    {
      "url": "blob:http://localhost:3001/...",
      "altText": "Another description",
      "caption": "Another caption",
      "name": "image2.png",
      "size": 234567
    }
  ]
}
                </div>
            </div>
            <div class="success">
                <strong>Expected Success:</strong><br>
                ✅ Status: 200 or 201<br>
                ✅ Toast notification: "Post saved as draft!"<br>
                ✅ Console logs show image processing<br>
                ✅ Database contains post_images records
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 2: Upload Images without Alt Text/Caption</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Upload images but don't add alt text or caption</li>
                <li>Submit the form</li>
                <li>Check request payload</li>
            </ol>
            <div class="expected">
                <strong>Expected Behavior:</strong><br>
                ✅ Images should still be included in request<br>
                ✅ altText and caption should be empty strings<br>
                ✅ Form should submit successfully
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 3: Submit Post without Images</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Fill post data but don't upload any images</li>
                <li>Submit the form</li>
                <li>Check request payload</li>
            </ol>
            <div class="expected">
                <strong>Expected Behavior:</strong><br>
                ✅ images array should be empty: []<br>
                ✅ Form should submit successfully<br>
                ✅ No post_images records created
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 4: Maximum Images (10)</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Try to upload 10 images</li>
                <li>Add alt text and captions to some</li>
                <li>Submit the form</li>
            </ol>
            <div class="expected">
                <strong>Expected Behavior:</strong><br>
                ✅ All 10 images should be included<br>
                ✅ Request should not exceed size limits<br>
                ✅ All images processed correctly
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🔧 How to Check Network Request</h2>
        <ol>
            <li><strong>Open Dev Tools:</strong> Press F12</li>
            <li><strong>Go to Network Tab:</strong> Click "Network"</li>
            <li><strong>Clear requests:</strong> Click clear button</li>
            <li><strong>Submit form:</strong> Click Save Draft or Publish</li>
            <li><strong>Find POST request:</strong> Look for POST /api/dashboard/posts</li>
            <li><strong>Check Request Payload:</strong> Click on request → Request tab</li>
            <li><strong>Verify images array:</strong> Should contain image data with alt/caption</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>🗄️ Database Verification</h2>
        <p>After successful submission, check Supabase dashboard:</p>
        <ol>
            <li><strong>posts table:</strong> Should have new record</li>
            <li><strong>post_images table:</strong> Should have image records with:
                <ul>
                    <li>post_id (foreign key)</li>
                    <li>url (blob URL)</li>
                    <li>alt_text (from form)</li>
                    <li>caption (from form)</li>
                    <li>display_order (1, 2, 3...)</li>
                    <li>file_name (original filename)</li>
                    <li>file_size (in bytes)</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-container">
        <h2>📊 Console Logs to Watch</h2>
        <div class="code">
📤 Submitting post with images: { images: [...] }
📸 Creating post with images: 3
Post created successfully: uuid
📸 Creating post images: 3
✅ Post images created successfully
        </div>
    </div>

    <div class="test-container">
        <h2>🐛 Common Issues</h2>
        <div class="error">
            <strong>Potential Problems:</strong><br>
            ❌ Images array not in request → Frontend not sending data<br>
            ❌ 400 validation error → Image data format wrong<br>
            ❌ Post created but no images → post_images table issue<br>
            ❌ Alt text/caption not saved → Database field mapping wrong
        </div>
    </div>

    <div class="test-container">
        <h2>🎯 Success Criteria</h2>
        <ul>
            <li>✅ Images included in POST request payload</li>
            <li>✅ Alt text and caption data preserved</li>
            <li>✅ Post created successfully in posts table</li>
            <li>✅ Image records created in post_images table</li>
            <li>✅ All image metadata (name, size, order) saved</li>
            <li>✅ Form handles empty alt text/caption gracefully</li>
            <li>✅ Console logs show image processing steps</li>
        </ul>
    </div>
</body>
</html>
