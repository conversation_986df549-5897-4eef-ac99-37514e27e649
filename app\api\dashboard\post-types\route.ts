import { NextRequest } from 'next/server'
import { createServerSupabaseAdminClient } from '@/lib/supabase/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { withSupabaseDashboardMiddleware } from '@/lib/api/supabase-auth-middleware'
import { withError<PERSON>and<PERSON>, withLogging, withRateLimit } from '@/lib/api/middleware'
import { RequestValidator, ValidationSchema } from '@/lib/api/validation'

async function getPostTypesHandler(_request: NextRequest) {
  try {
    const supabase = createServerSupabaseAdminClient()

    // Get all post types with post count
    const { data: postTypes, error } = await supabase
      .from('post_types')
      .select(`
        *,
        posts:posts(count)
      `)
      .order('name', { ascending: true })

    if (error) {
      console.error('Supabase error fetching post types:', error)
      return ApiResponseBuilder.databaseError('Failed to fetch post types', { supabaseError: error })
    }

    // Transform to frontend format with post count
    const transformedTypes = postTypes.map(type => ({
      id: type.id,
      name: type.name,
      slug: type.slug,
      color: type.color,
      icon: type.icon,
      description: type.description || '',
      postCount: type.posts?.[0]?.count || 0,
      createdAt: type.created_at,
      updatedAt: type.updated_at
    }))

    return ApiResponseBuilder.success(
      transformedTypes,
      `Retrieved ${transformedTypes.length} post types`
    )
  } catch (error) {
    console.error('Error fetching post types:', error)
    return ApiResponseBuilder.internalError('Failed to fetch post types', { error: error instanceof Error ? error.message : 'Unknown error' })
  }
}

// Validation schema for create post type request
const createPostTypeSchema: ValidationSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Name cannot be empty or only whitespace'
      if (trimmed.length < 2) return 'Name must be at least 2 characters long'
      return true
    }
  },
  slug: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-z0-9-]+$/,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Slug cannot be empty'
      if (trimmed.startsWith('-') || trimmed.endsWith('-')) {
        return 'Slug cannot start or end with hyphen'
      }
      if (trimmed.includes('--')) {
        return 'Slug cannot contain consecutive hyphens'
      }
      return true
    }
  },
  color: {
    required: true,
    type: 'string',
    pattern: /^#[0-9A-Fa-f]{6}$/,
    custom: (value: string) => {
      if (!value?.trim()) return 'Color is required'
      return true
    }
  },
  icon: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 50,
    custom: (value: string) => {
      const trimmed = value?.trim()
      if (!trimmed) return 'Icon is required'
      return true
    }
  },
  description: {
    required: false,
    type: 'string',
    maxLength: 500
  }
}

async function createPostTypeHandler(request: NextRequest) {
  try {
    // Validate request body
    const validation = await RequestValidator.validateBody(request, createPostTypeSchema)
    
    if (!validation.isValid) {
      console.error('Validation errors:', validation.errors)
      return ApiResponseBuilder.validationError(
        'Validation failed',
        validation.errors[0]?.field,
        { errors: validation.errors }
      )
    }

    const { name, slug, color, icon, description } = validation.data!

    const supabase = createServerSupabaseAdminClient()

    // Check if slug already exists
    const { data: existingType } = await supabase
      .from('post_types')
      .select('id')
      .eq('slug', slug)
      .single()

    if (existingType) {
      return ApiResponseBuilder.badRequest('A post type with this slug already exists')
    }

    // Create new post type
    const { data: newType, error } = await supabase
      .from('post_types')
      .insert({
        name: name.trim(),
        slug: slug.trim(),
        color: color.trim(),
        icon: icon.trim(),
        description: description?.trim() || ''
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error creating post type:', error)
      return ApiResponseBuilder.databaseError('Failed to create post type', { supabaseError: error })
    }

    // Transform to frontend format
    const transformedType = {
      id: newType.id,
      name: newType.name,
      slug: newType.slug,
      color: newType.color,
      icon: newType.icon,
      description: newType.description || '',
      postCount: 0,
      createdAt: newType.created_at,
      updatedAt: newType.updated_at
    }

    return ApiResponseBuilder.success(
      transformedType,
      'Post type created successfully'
    )
  } catch (error) {
    console.error('Error creating post type:', error)
    return ApiResponseBuilder.internalError('Failed to create post type', { error: error instanceof Error ? error.message : 'Unknown error' })
  }
}

// Apply dashboard middleware with authentication
export const GET = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(100, 15 * 60 * 1000) // 100 requests per 15 minutes
)(getPostTypesHandler)

export const POST = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(20, 15 * 60 * 1000) // 20 requests per 15 minutes for create operations
)(createPostTypeHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
