import { notFound } from "next/navigation"
import { DataService } from "@/lib/services/data-service"
import { PostDetailClient } from "@/components/posts/post-detail-client"

interface ThreadPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ThreadPage({ params }: ThreadPageProps) {
  // Await params before using its properties (Next.js 15 requirement)
  const { id } = await params
  // Fetch thread data from database
  const thread = await DataService.threads.getThreadById(id)

  if (!thread) {
    notFound()
  }

  return <PostDetailClient post={thread} />
}
