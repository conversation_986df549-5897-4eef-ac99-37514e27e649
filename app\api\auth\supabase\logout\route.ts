import { NextRequest } from 'next/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { signOut } from '@/lib/supabase/auth'
import { withErrorHandler, withLogging, withRateLimit } from '@/lib/api/middleware'

async function logoutHandler(request: NextRequest) {
  try {
    // Attempt to sign out with Supabase
    const result = await signOut()

    if (!result.success) {
      return ApiResponseBuilder.badRequest(
        result.error || 'Logout failed'
      )
    }

    return ApiResponseBuilder.success(
      null,
      'Logout successful'
    )
  } catch (error) {
    console.error('Logout error:', error)
    return ApiResponseBuilder.internalError('Logout failed')
  }
}

// Apply middleware
export const POST = withErrorHandler(
  withLogging(
    withRateLimit(20, 15 * 60 * 1000)(logoutHandler) // 20 requests per 15 minutes
  )
)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
