"use client"

import { createContext, useContext, ReactNode } from 'react'
import { Locale, createTranslator } from '@/lib/i18n'

interface LocaleContextType {
  locale: Locale
  messages: Record<string, any>
  t: (key: string, params?: Record<string, string | number>) => string
}

const LocaleContext = createContext<LocaleContextType | undefined>(undefined)

interface LocaleProviderProps {
  children: ReactNode
  locale: Locale
  messages: Record<string, any>
}

export function LocaleProvider({ children, locale, messages }: LocaleProviderProps) {
  const t = createTranslator(messages)
  
  return (
    <LocaleContext.Provider value={{ locale, messages, t }}>
      {children}
    </LocaleContext.Provider>
  )
}

export function useLocale() {
  const context = useContext(LocaleContext)
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider')
  }
  return context
}

// Hook for translations
export function useTranslations() {
  const { t } = useLocale()
  return t
}
