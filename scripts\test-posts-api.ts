#!/usr/bin/env tsx

import { config } from 'dotenv'
import { PostApiSimpleService } from '../services/post-api-simple.service'

// Load environment variables
config({ path: '.env.local' })

async function testPostsAPI() {
  console.log('🧪 Testing Posts API with Supabase (Simple Version)...')

  try {
    const postService = new PostApiSimpleService()

    // Test 1: Get all posts (should work even with empty table)
    console.log('\n1️⃣ Testing getAllPosts...')
    try {
      const { posts, total } = await postService.getAllPosts(
        { page: 1, limit: 5 },
        {}
      )
      console.log(`✅ Found ${posts.length} posts out of ${total} total`)

      if (posts.length > 0) {
        console.log(`   First post: "${posts[0].title}"`)
      } else {
        console.log(`   ℹ️ No posts found (empty table)`)
      }
    } catch (error) {
      console.error('❌ getAllPosts failed:', error.message)
    }

    // Test 2: Get post stats (should work even with empty table)
    console.log('\n2️⃣ Testing getPostStats...')
    try {
      const stats = await postService.getPostStats()
      console.log(`✅ Stats:`)
      console.log(`   - Total posts: ${stats.totalPosts}`)
      console.log(`   - Published: ${stats.publishedPosts}`)
      console.log(`   - Draft: ${stats.draftPosts}`)
      console.log(`   - Total views: ${stats.totalViews}`)
      console.log(`   - Total reactions: ${stats.totalReactions}`)
    } catch (error) {
      console.error('❌ getPostStats failed:', error.message)
    }

    // Test 3: Get featured posts
    console.log('\n3️⃣ Testing getFeaturedPosts...')
    try {
      const featuredPosts = await postService.getFeaturedPosts(3)
      console.log(`✅ Found ${featuredPosts.length} featured posts`)
    } catch (error) {
      console.error('❌ getFeaturedPosts failed:', error.message)
    }

    // Test 4: Search posts
    console.log('\n4️⃣ Testing searchPosts...')
    try {
      const searchResults = await postService.searchPosts('test', { limit: 3 })
      console.log(`✅ Search results: ${searchResults.posts.length} posts found`)
    } catch (error) {
      console.error('❌ searchPosts failed:', error.message)
    }

    // Test 5: Get posts by status
    console.log('\n5️⃣ Testing getPostsByStatus...')
    try {
      const publishedPosts = await postService.getPostsByStatus('published', { limit: 3 })
      console.log(`✅ Published posts: ${publishedPosts.posts.length} found`)
    } catch (error) {
      console.error('❌ getPostsByStatus failed:', error.message)
    }

    console.log('\n🎉 Posts API tests completed!')

  } catch (error) {
    console.error('❌ Test error:', error)
    process.exit(1)
  }
}

// Run the test
testPostsAPI()
