"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.APP_CONFIG = exports.SKILLS_DATA = exports.POST_FILTERS = exports.PORTFOLIO_CATEGORIES = exports.NAVIGATION_ITEMS = exports.SOCIAL_LINKS = exports.getPostTypeClasses = exports.getPostTypeIcon = exports.POST_TYPE_CONFIG = void 0;
var lucide_react_1 = require("lucide-react");
// Post type configurations for seeding and UI
exports.POST_TYPE_CONFIG = {
    learning: {
        color: "#3b82f6", // blue-500
        icon: lucide_react_1.BookOpen,
        label: "Learning",
        slug: "learning"
    },
    'error-log': {
        color: "#ef4444", // red-500
        icon: lucide_react_1.AlertCircle,
        label: "Error Log",
        slug: "error-log"
    },
    opinion: {
        color: "#a855f7", // purple-500
        icon: lucide_react_1.Lightbulb,
        label: "Opinion",
        slug: "opinion"
    },
    tip: {
        color: "#10b981", // emerald-500
        icon: lucide_react_1.Code,
        label: "Tip",
        slug: "tip"
    },
    showcase: {
        color: "#f59e0b", // amber-500
        icon: lucide_react_1.Star,
        label: "Showcase",
        slug: "showcase"
    },
    photo: {
        color: "#ec4899", // pink-500
        icon: lucide_react_1.ImageIcon,
        label: "Photo",
        slug: "photo"
    }
};
// Helper function to get icon by slug
var getPostTypeIcon = function (slug) {
    var _a;
    return ((_a = exports.POST_TYPE_CONFIG[slug]) === null || _a === void 0 ? void 0 : _a.icon) || lucide_react_1.BookOpen;
};
exports.getPostTypeIcon = getPostTypeIcon;
// Helper function to generate CSS classes from hex color
var getPostTypeClasses = function (hexColor) {
    // Convert hex to RGB for opacity calculations
    var hex = hexColor.replace('#', '');
    var r = parseInt(hex.substring(0, 2), 16);
    var g = parseInt(hex.substring(2, 4), 16);
    var b = parseInt(hex.substring(4, 6), 16);
    return {
        background: "linear-gradient(to right, rgba(".concat(r, ", ").concat(g, ", ").concat(b, ", 0.1), rgba(").concat(r, ", ").concat(g, ", ").concat(b, ", 0.2))"),
        color: hexColor,
        borderColor: "rgba(".concat(r, ", ").concat(g, ", ").concat(b, ", 0.3)"),
        // For Tailwind classes (fallback)
        className: "bg-gradient-to-r border-opacity-30"
    };
};
exports.getPostTypeClasses = getPostTypeClasses;
// Social media configurations
exports.SOCIAL_LINKS = [
    { icon: lucide_react_1.Github, label: 'GitHub', key: 'github' },
    { icon: lucide_react_1.Twitter, label: 'Twitter', key: 'twitter' },
    { icon: lucide_react_1.Linkedin, label: 'LinkedIn', key: 'linkedin' },
    { icon: lucide_react_1.Mail, label: 'Email', key: 'email' },
];
// Navigation items
exports.NAVIGATION_ITEMS = [
    { label: 'Home', href: '/' },
    { label: 'Experience', href: '/experience' },
    { label: 'Portfolio', href: '/portfolio' },
    { label: 'About', href: '/about' },
];
// Portfolio categories
exports.PORTFOLIO_CATEGORIES = [
    "All",
    "Full Stack",
    "Web App",
    "Data Visualization",
    "Analytics"
];
// Post filter options
exports.POST_FILTERS = [
    "all",
    "learning",
    "opinion",
    "tip",
    "photo",
    "showcase",
    "error-log"
];
// Skills data
exports.SKILLS_DATA = [
    { category: "Frontend", technologies: ["React", "Next.js", "Vue.js", "TypeScript", "Tailwind CSS"] },
    { category: "Backend", technologies: ["Node.js", "Express", "Python", "PostgreSQL", "MongoDB"] },
    { category: "DevOps", technologies: ["AWS", "Docker", "CI/CD", "Kubernetes", "Terraform"] },
    { category: "Tools", technologies: ["Git", "Figma", "Jira", "Slack", "VS Code"] },
];
// Application metadata
exports.APP_CONFIG = {
    name: "Rijal Solahudin",
    tagline: "Personal Portfolio",
    version: "v1.0.0",
    description: "A hybrid platform combining social posts, GitHub repositories, and blogging functionality for organizing and sharing thoughts.",
    author: "Rijal Solahudin"
};
