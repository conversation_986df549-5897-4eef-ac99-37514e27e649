"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { InputField } from '@/components/dashboard/input-field'
import { TextEditor } from '@/components/dashboard/text-editor'
import { PostTypeSelector } from '@/components/dashboard/post-type-selector'
import { StatusSelector } from '@/components/dashboard/status-selector'
import { SlugInput } from '@/components/dashboard/forms/slug-input'
import { ImageUpload } from '@/components/forms/image-upload'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { PostStatus } from '@/types'
import { Save, Send, X, Plus } from 'lucide-react'

interface UploadedImage {
  id: string
  file?: File // Optional for existing images
  url: string
  name: string
  size: number
  altText?: string
  caption?: string
  isExisting?: boolean // Flag to identify existing vs new images
}

export interface PostFormData {
  title: string
  slug: string
  version: string
  content: string
  typeId: string
  status: PostStatus
  featured: boolean
  showFullContent: boolean
  tags: string[]
  images: UploadedImage[]
}

interface PostFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<PostFormData>
  onSubmit: (data: PostFormData) => Promise<void>
  isSubmitting?: boolean
  postId?: string // For edit mode to exclude current post from slug check
}

export function PostForm({
  mode,
  initialData = {},
  onSubmit,
  isSubmitting = false,
  postId
}: PostFormProps) {
  // Form state
  const [title, setTitle] = useState(initialData.title || '')
  const [slug, setSlug] = useState(initialData.slug || '')
  const [version, setVersion] = useState(initialData.version || '1.0.0')
  const [content, setContent] = useState(initialData.content || '# New Post\n\nStart writing your content here...')
  const [typeId, setTypeId] = useState(initialData.typeId || '')
  const [status, setStatus] = useState<PostStatus>(initialData.status || PostStatus.DRAFT)
  const [featured, setFeatured] = useState(initialData.featured || false)
  const [showFullContent, setShowFullContent] = useState(initialData.showFullContent || false)
  const [tags, setTags] = useState<string[]>(initialData.tags || [])
  const [images, setImages] = useState<UploadedImage[]>(initialData.images || [])
  const [newTag, setNewTag] = useState('')

  // Loading states for each button
  const [isDraftLoading, setIsDraftLoading] = useState(false)
  const [isPublishLoading, setIsPublishLoading] = useState(false)

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Update initial data when it changes (for edit mode)
  useEffect(() => {
    if (mode === 'edit' && initialData) {
      setTitle(initialData.title || '')
      setSlug(initialData.slug || '')
      setVersion(initialData.version || '1.0.0')
      setContent(initialData.content || '')
      setTypeId(initialData.typeId || '')
      setStatus(initialData.status || PostStatus.DRAFT)
      setFeatured(initialData.featured || false)
      setShowFullContent(initialData.showFullContent || false)
      setTags(initialData.tags || [])
      setImages(initialData.images || [])
    }
  }, [mode, initialData])

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!title.trim()) {
      newErrors.title = 'Title is required'
    }
    if (!slug.trim()) {
      newErrors.slug = 'Slug is required'
    }
    if (!version.trim()) {
      newErrors.version = 'Version is required'
    }
    if (!content.trim()) {
      newErrors.content = 'Content is required'
    }
    if (!typeId) {
      newErrors.typeId = 'Post type is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (submitStatus: PostStatus) => {
    if (!validateForm()) {
      return
    }

    // Set loading state based on which button was clicked
    if (submitStatus === PostStatus.DRAFT) {
      setIsDraftLoading(true)
    } else {
      setIsPublishLoading(true)
    }

    try {
      const formData: PostFormData = {
        title: title.trim(),
        slug: slug.trim(),
        version: version.trim(),
        content: content.trim(),
        typeId,
        status: submitStatus,
        featured,
        showFullContent,
        tags,
        images
      }

      await onSubmit(formData)
    } finally {
      // Reset loading states
      setIsDraftLoading(false)
      setIsPublishLoading(false)
    }
  }

  // Handle tag management
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {/* Main Content */}
      <div className="lg:col-span-2 xl:col-span-3 space-y-6">
        {/* Post Content Card */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div className="space-y-1">
              <CardTitle className="text-gray-900 dark:text-white text-lg font-semibold">
                Post Content
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <InputField
              label="Title"
              name="title"
              placeholder="Enter post title..."
              value={title}
              onChange={setTitle}
              error={errors.title}
              required
              maxLength={200}
            />

            <SlugInput
              value={slug}
              onChange={setSlug}
              title={title}
              required
              className="space-y-2"
              excludeId={mode === 'edit' ? postId : undefined}
            />

            <InputField
              label="Version"
              name="version"
              placeholder="Enter post version (e.g., 1.0.0)..."
              value={version}
              onChange={setVersion}
              error={errors.version}
              required
              maxLength={20}
              description="Semantic version for this post (e.g., 1.0.0, 2.1.3)"
            />

            <TextEditor
              label="Content"
              name="content"
              placeholder="Start writing your post content..."
              value={content}
              onChange={setContent}
              error={errors.content}
              required
              description="Write your post content using Markdown formatting"
            />

            {/* Images Section */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Images
                <span className="text-xs text-gray-500 dark:text-gray-400 font-normal ml-2">(optional)</span>
              </Label>
              <ImageUpload
                onImagesChange={setImages}
                initialImages={images}
                maxImages={10}
                maxSizePerImage={5}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1 xl:col-span-1 space-y-6">
        {/* Post Settings Card */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div className="space-y-1">
              <CardTitle className="text-gray-900 dark:text-white text-lg font-semibold">
                Post Settings
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <PostTypeSelector
              label="Post Type"
              value={typeId}
              onChange={setTypeId}
              error={errors.typeId}
              required
            />

            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Featured Post
              </Label>
              <Switch
                checked={featured}
                onCheckedChange={setFeatured}
                className="data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:bg-gray-700"
              />
            </div>

            {/* Display Mode Toggle */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Display Mode
                  </Label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    How this post appears in list
                  </p>
                </div>
                <Switch
                  checked={showFullContent}
                  onCheckedChange={setShowFullContent}
                  className="data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:bg-gray-700"
                />
              </div>

              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                <div className="flex items-start space-x-2">
                  <div className={`w-2 h-2 rounded-full mt-1.5 ${showFullContent ? 'bg-green-500' : 'bg-gray-400'}`} />
                  <div className="flex-1">
                    <p className="text-xs font-medium text-gray-900 dark:text-white">
                      {showFullContent ? 'Full Content' : 'Auto Excerpt'}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                      {showFullContent
                        ? 'Complete content in post list'
                        : 'Auto-generated excerpt shown'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags Section */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                Tags
                <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">(optional)</span>
              </Label>

              {/* Tags Display */}
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm border border-green-200 dark:border-green-700"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:bg-green-200 dark:hover:bg-green-800 rounded-full p-0.5 transition-colors"
                        title={`Remove ${tag} tag`}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Tag Input */}
              <div className="space-y-3">
                <div className="relative">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={handleTagKeyPress}
                    placeholder="Type a tag and press Enter..."
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:border-green-500 dark:focus:border-green-400 focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0"
                    maxLength={50}
                  />
                  {newTag && (
                    <Button
                      type="button"
                      onClick={addTag}
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 bg-green-600 hover:bg-green-700 text-white text-xs"
                    >
                      Add
                    </Button>
                  )}
                </div>

                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Press Enter or comma to add tags. Use lowercase letters, numbers, and hyphens only.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => handleSubmit(PostStatus.DRAFT)}
            disabled={isDraftLoading || isPublishLoading}
            className="border-green-600 dark:border-green-500 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
          >
            {isDraftLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </>
            )}
          </Button>
          <Button
            onClick={() => handleSubmit(PostStatus.PUBLISHED)}
            disabled={isDraftLoading || isPublishLoading}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isPublishLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {mode === 'create' ? 'Publishing...' : 'Updating...'}
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {mode === 'create' ? 'Publish' : 'Update & Publish'}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
