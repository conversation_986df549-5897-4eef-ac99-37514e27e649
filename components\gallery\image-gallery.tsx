"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Maximize2, X, Download } from "lucide-react"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import Image from "next/image"

interface ThreadImage {
  id: string
  url: string
  alt?: string
  caption?: string
  width?: number
  height?: number
}

interface ImageGalleryProps {
  images: ThreadImage[]
  className?: string
}

export function ImageGallery({ images, className = "" }: ImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<number | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  if (!images || images.length === 0) return null

  const openLightbox = (index: number) => {
    setCurrentIndex(index)
    setSelectedImage(index)
  }

  const closeLightbox = () => {
    setSelectedImage(null)
  }

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const downloadImage = async (url: string, filename: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = downloadUrl
      link.download = filename || "image"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      console.error("Failed to download image:", error)
    }
  }

  const renderGallery = () => {
    if (images.length === 1) {
      // Single image - full width
      return (
        <div className="relative group cursor-pointer" onClick={() => openLightbox(0)}>
          <div className="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800 theme-transition">
            <Image
              src={images[0].url || "/placeholder.svg"}
              alt={images[0].alt || "Thread image"}
              width={800}
              height={600}
              className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gray-900/0 group-hover:bg-gray-900/20 dark:bg-black/0 dark:group-hover:bg-black/20 transition-colors duration-200 theme-transition" />
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-3 right-3 bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 theme-transition"
            >
              <Maximize2 className="w-4 h-4" />
            </Button>
          </div>
          {images[0].caption && <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 italic theme-transition">{images[0].caption}</p>}
        </div>
      )
    } else if (images.length === 2) {
      // Two images - side by side
      return (
        <div className="grid grid-cols-2 gap-2">
          {images.map((image, index) => (
            <div key={image.id} className="relative group cursor-pointer" onClick={() => openLightbox(index)}>
              <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
                <Image
                  src={image.url || "/placeholder.svg"}
                  alt={image.alt || `Thread image ${index + 1}`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <Maximize2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )
    } else if (images.length === 3) {
      // Three images - first large, two small
      return (
        <div className="grid grid-cols-2 gap-2">
          <div className="relative group cursor-pointer row-span-2" onClick={() => openLightbox(0)}>
            <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
              <Image
                src={images[0].url || "/placeholder.svg"}
                alt={images[0].alt || "Thread image 1"}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <Maximize2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
          {images.slice(1).map((image, index) => (
            <div key={image.id} className="relative group cursor-pointer" onClick={() => openLightbox(index + 1)}>
              <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
                <Image
                  src={image.url || "/placeholder.svg"}
                  alt={image.alt || `Thread image ${index + 2}`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <Maximize2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )
    } else {
      // Four or more images - grid with overflow indicator
      return (
        <div className="grid grid-cols-2 gap-2">
          {images.slice(0, 3).map((image, index) => (
            <div key={image.id} className="relative group cursor-pointer" onClick={() => openLightbox(index)}>
              <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
                <Image
                  src={image.url || "/placeholder.svg"}
                  alt={image.alt || `Thread image ${index + 1}`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <Maximize2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
          <div className="relative group cursor-pointer" onClick={() => openLightbox(3)}>
            <div className="relative overflow-hidden rounded-lg bg-gray-800 aspect-square">
              <Image
                src={images[3].url || "/placeholder.svg"}
                alt={images[3].alt || "Thread image 4"}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              {images.length > 4 && (
                <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                  <span className="text-white text-xl font-bold">+{images.length - 4}</span>
                </div>
              )}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
            </div>
          </div>
        </div>
      )
    }
  }

  return (
    <div className={className}>
      {renderGallery()}

      {/* Lightbox Modal */}
      <Dialog open={selectedImage !== null} onOpenChange={closeLightbox}>
        <DialogContent className="max-w-7xl max-h-[95vh] p-0 bg-black/95 border-gray-800">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={closeLightbox}
              className="absolute top-4 right-4 z-50 bg-black/50 hover:bg-black/70 text-white"
            >
              <X className="w-5 h-5" />
            </Button>

            {/* Download Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => downloadImage(images[currentIndex].url, `image-${currentIndex + 1}`)}
              className="absolute top-4 right-16 z-50 bg-black/50 hover:bg-black/70 text-white"
            >
              <Download className="w-5 h-5" />
            </Button>

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 bg-black/50 hover:bg-black/70 text-white"
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 bg-black/50 hover:bg-black/70 text-white"
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </>
            )}

            {/* Main Image */}
            <div className="relative max-w-full max-h-full p-8">
              <Image
                src={images[currentIndex].url || "/placeholder.svg"}
                alt={images[currentIndex].alt || `Image ${currentIndex + 1}`}
                width={1200}
                height={900}
                className="max-w-full max-h-full object-contain"
              />
            </div>

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                {currentIndex + 1} / {images.length}
              </div>
            )}

            {/* Caption */}
            {images[currentIndex].caption && (
              <div className="absolute bottom-4 left-4 right-4 bg-black/50 text-white p-3 rounded-lg text-center">
                <p className="text-sm">{images[currentIndex].caption}</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
