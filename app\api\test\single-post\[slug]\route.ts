import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

interface RouteParams {
  params: Promise<{
    slug: string
  }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params
    console.log('🔍 Testing single post with slug:', slug)

    const postService = new PostApiSimpleService()
    const post = await postService.getPostBySlug(slug)

    if (!post) {
      return ApiResponseBuilder.notFound(`Post with slug '${slug}' not found`)
    }

    console.log('📄 Post found:', {
      id: post.id,
      title: post.title,
      status: post.status,
      author: post.author?.name,
      type: post.type?.name
    })

    // Analyze post data structure
    const analysis = {
      hasTitle: !!post.title,
      hasContent: !!post.content,
      hasAuthor: !!post.author,
      hasType: !!post.type,
      hasImages: post.images && post.images.length > 0,
      hasTags: post.tags && post.tags.length > 0,
      hasReactions: typeof post.reactionHart === 'number',
      hasViewCount: typeof post.viewCount === 'number',
      isPublished: post.status === 'PUBLISHED',
      contentLength: post.content?.length || 0,
      imageCount: post.images?.length || 0,
      tagCount: post.tags?.length || 0
    }

    return ApiResponseBuilder.success(
      {
        post: {
          id: post.id,
          title: post.title,
          slug: post.slug,
          status: post.status,
          featured: post.featured,
          excerpt: post.excerpt,
          version: post.version,
          readTime: post.readTime,
          createdAt: post.createdAt,
          updatedAt: post.updatedAt,
          publishedAt: post.publishedAt,
          viewCount: post.viewCount,
          reactionHart: post.reactionHart,
          comments: post.comments,
          tags: post.tags,
          author: post.author,
          type: post.type,
          images: post.images?.map(img => ({
            url: img.url,
            altText: img.altText || img.alt_text,
            caption: img.caption,
            hasAltText: !!(img.altText || img.alt_text),
            hasCaption: !!img.caption
          })) || [],
          contentPreview: post.content?.substring(0, 200) + '...'
        },
        analysis,
        metadata: {
          slug,
          found: true,
          dataComplete: analysis.hasTitle && analysis.hasContent && analysis.hasAuthor && analysis.hasType
        }
      },
      `Single post data retrieved successfully for slug: ${slug}`
    )

  } catch (error) {
    console.error('💥 Error testing single post:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to get single post data'
    )
  }
}
