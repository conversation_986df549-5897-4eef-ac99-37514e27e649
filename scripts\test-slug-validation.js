const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const BASE_URL = 'http://localhost:3001'

async function testSlugValidation() {
  console.log('🧪 Testing Slug Validation API...\n')

  const testCases = [
    {
      name: 'Valid new slug',
      slug: 'test-new-slug-' + Date.now(),
      expectedAvailable: true
    },
    {
      name: 'Existing slug',
      slug: 'css-grid-layout-tip',
      expectedAvailable: false
    },
    {
      name: 'Invalid characters (uppercase)',
      slug: 'Test-Slug-123',
      expectedError: true
    },
    {
      name: 'Invalid characters (underscore)',
      slug: 'test_slug_123',
      expectedError: true
    },
    {
      name: 'Invalid characters (special chars)',
      slug: 'test-slug-123!',
      expectedError: true
    },
    {
      name: 'Empty slug',
      slug: '',
      expectedError: true
    },
    {
      name: 'Valid slug with numbers',
      slug: 'test-slug-123',
      expectedAvailable: true
    },
    {
      name: 'Valid slug with hyphens',
      slug: 'test-slug-with-many-hyphens',
      expectedAvailable: true
    }
  ]

  let passedTests = 0
  let totalTests = testCases.length

  for (const testCase of testCases) {
    try {
      console.log(`🔍 Testing: ${testCase.name}`)
      console.log(`   Slug: "${testCase.slug}"`)

      const response = await fetch(`${BASE_URL}/api/test/validate-slug?slug=${encodeURIComponent(testCase.slug)}`)
      const result = await response.json()

      if (testCase.expectedError) {
        if (!response.ok) {
          console.log(`   ✅ Expected error received: ${result.error.message}`)
          passedTests++
        } else {
          console.log(`   ❌ Expected error but got success`)
        }
      } else {
        if (response.ok) {
          const isAvailable = result.data.available
          if (isAvailable === testCase.expectedAvailable) {
            console.log(`   ✅ Expected availability: ${testCase.expectedAvailable}, Got: ${isAvailable}`)
            passedTests++
          } else {
            console.log(`   ❌ Expected availability: ${testCase.expectedAvailable}, Got: ${isAvailable}`)
          }
        } else {
          console.log(`   ❌ Unexpected error: ${result.error.message}`)
        }
      }

    } catch (error) {
      console.log(`   ❌ Test failed with error: ${error.message}`)
    }

    console.log('') // Empty line for readability
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Slug validation is working correctly.')
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.')
  }
}

// Test with excludeId parameter
async function testSlugValidationWithExclude() {
  console.log('\n🧪 Testing Slug Validation with excludeId...\n')

  try {
    // Test with existing slug but excluding its own ID
    const existingSlug = 'css-grid-layout-tip'
    const excludeId = 'e72db5e6-e297-4a49-b597-262c8e4dbc2d' // ID of the post with this slug

    console.log(`🔍 Testing existing slug with excludeId`)
    console.log(`   Slug: "${existingSlug}"`)
    console.log(`   ExcludeId: "${excludeId}"`)

    const response = await fetch(`${BASE_URL}/api/test/validate-slug?slug=${encodeURIComponent(existingSlug)}&excludeId=${excludeId}`)
    const result = await response.json()

    if (response.ok && result.data.available) {
      console.log('   ✅ Slug is available when excluding its own ID')
    } else {
      console.log('   ❌ Slug should be available when excluding its own ID')
    }

  } catch (error) {
    console.log(`   ❌ Test failed with error: ${error.message}`)
  }
}

// Run tests
async function runAllTests() {
  await testSlugValidation()
  await testSlugValidationWithExclude()
}

runAllTests().catch(console.error)
