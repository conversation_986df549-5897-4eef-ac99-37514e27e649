import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Search, BookOpen } from "lucide-react"
import Link from "next/link"

interface QuickActionsCardProps {
  onCreateClick?: () => void
  onSearchClick?: () => void
}

export function QuickActionsCard({ onCreateClick, onSearchClick }: QuickActionsCardProps) {
  return (
    <Card className="bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm theme-transition">
      <CardContent className="p-6">
        <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4 theme-transition">Quick Actions</h3>
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-gray-600 hover:text-green-500 hover:bg-green-500/10 dark:text-gray-400 dark:hover:text-green-400 theme-transition"
            onClick={onCreateClick}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Thread
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-gray-600 hover:text-green-500 hover:bg-green-500/10 dark:text-gray-400 dark:hover:text-green-400 theme-transition"
            onClick={onSearchClick}
          >
            <Search className="w-4 h-4 mr-2" />
            Search Threads
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-gray-600 hover:text-blue-500 hover:bg-blue-500/10 dark:text-gray-400 dark:hover:text-blue-400 theme-transition"
            asChild
          >
            <Link href="/about">
              <BookOpen className="w-4 h-4 mr-2" />
              About Me
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
