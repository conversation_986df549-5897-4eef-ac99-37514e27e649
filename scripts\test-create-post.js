const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testCreatePost() {
  console.log('🧪 Testing create post API...\n')

  try {
    // First, let's check if we have any post types
    console.log('📋 Checking available post types...')
    const { data: postTypes, error: postTypesError } = await supabase
      .from('post_types')
      .select('*')
    
    if (postTypesError) {
      console.error('❌ Error fetching post types:', postTypesError.message)
      return
    }

    if (!postTypes || postTypes.length === 0) {
      console.log('⚠️ No post types found. Creating default post type...')
      
      const { data: newPostType, error: createTypeError } = await supabase
        .from('post_types')
        .insert({
          name: 'Article',
          slug: 'article',
          color: '#10b981',
          icon: 'file-text'
        })
        .select()
        .single()
      
      if (createTypeError) {
        console.error('❌ Error creating post type:', createTypeError.message)
        return
      }
      
      console.log('✅ Created default post type:', newPostType)
      postTypes.push(newPostType)
    }

    console.log('✅ Available post types:', postTypes.map(pt => `${pt.name} (${pt.id})`))

    // Test data for creating a post
    const testPostData = {
      title: 'Test Post from API',
      slug: 'test-post-api-' + Date.now(),
      content: '# Test Post\n\nThis is a test post created via API to test the Supabase integration.\n\n## Features\n\n- Markdown support\n- Slug validation\n- Author integration\n\nThis post should be created successfully!',
      typeId: postTypes[0].id,
      status: 'DRAFT',
      featured: false,
      showFullContent: true,
      tags: ['test', 'api', 'supabase']
    }

    console.log('\n📝 Testing create post with data:', testPostData)

    // Test the API endpoint
    const response = await fetch('http://localhost:3000/api/test/create-post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPostData)
    })

    const result = await response.json()

    if (response.ok) {
      console.log('✅ Post created successfully!')
      console.log('📄 Created post:', {
        id: result.data.id,
        title: result.data.title,
        slug: result.data.slug,
        status: result.data.status,
        author: result.data.author
      })
    } else {
      console.error('❌ Failed to create post:', result)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testCreatePost()
