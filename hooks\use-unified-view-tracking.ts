"use client"

import { useState, useCallback, useRef, useEffect } from 'react'

interface UseUnifiedViewTrackingOptions {
  postId: string
  threshold?: number
  delay?: number
  enabled?: boolean
}

interface UseUnifiedViewTrackingReturn {
  ref: React.RefObject<HTMLElement>
  isInViewport: boolean
  hasRecordedView: boolean
  isRecordingView: boolean
  error: string | null
  recordView: () => Promise<void>
}

/**
 * Unified view tracking hook that ensures only 1 view per post per 24h
 * regardless of whether viewed via list viewport or detail page
 */
export function useUnifiedViewTracking({
  postId,
  threshold = 0.6,
  delay = 1500,
  enabled = true
}: UseUnifiedViewTrackingOptions): UseUnifiedViewTrackingReturn {
  const elementRef = useRef<HTMLElement>(null)
  const [isInViewport, setIsInViewport] = useState(false)
  const [hasRecordedView, setHasRecordedView] = useState(false)
  const [isRecordingView, setIsRecordingView] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const observerRef = useRef<IntersectionObserver | null>(null)
  const hasTriggeredRef = useRef(false)
  const viewportTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Storage key for tracking viewed posts
  const storageKey = `viewed_posts_24h`

  /**
   * Check if post was already viewed today (localStorage + API)
   */
  const checkIfAlreadyViewed = useCallback((): boolean => {
    if (typeof window === 'undefined') return false

    try {
      // Check localStorage first (fast check)
      const viewedPosts = localStorage.getItem(storageKey)
      if (viewedPosts) {
        const parsed = JSON.parse(viewedPosts)
        const today = new Date().toDateString()
        
        // Clean old entries (older than 24h)
        const cleanedPosts = Object.entries(parsed).reduce((acc: any, [id, date]) => {
          if (new Date(date as string).toDateString() === today) {
            acc[id] = date
          }
          return acc
        }, {})
        
        // Update localStorage with cleaned data
        localStorage.setItem(storageKey, JSON.stringify(cleanedPosts))
        
        // Check if current post was viewed today
        return cleanedPosts[postId] !== undefined
      }
      
      return false
    } catch (err) {
      console.error('Error checking viewed posts:', err)
      return false
    }
  }, [postId, storageKey])

  /**
   * Mark post as viewed in localStorage
   */
  const markAsViewed = useCallback(() => {
    if (typeof window === 'undefined') return

    try {
      const viewedPosts = localStorage.getItem(storageKey)
      const parsed = viewedPosts ? JSON.parse(viewedPosts) : {}
      
      // Add current post with current timestamp
      parsed[postId] = new Date().toISOString()
      
      localStorage.setItem(storageKey, JSON.stringify(parsed))
    } catch (err) {
      console.error('Error marking post as viewed:', err)
    }
  }, [postId, storageKey])

  /**
   * Record view via API (unified endpoint)
   */
  const recordView = useCallback(async () => {
    if (hasRecordedView || isRecordingView) {
      return
    }

    // Check if already viewed today
    if (checkIfAlreadyViewed()) {
      console.log('📊 Post already viewed today, skipping:', postId)
      setHasRecordedView(true)
      return
    }

    try {
      setIsRecordingView(true)
      setError(null)

      console.log('📊 Recording unified view for post:', postId)

      // Try full system first
      let response = await fetch(`/api/posts/${postId}/view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source: 'unified', // Mark as unified tracking
          timestamp: new Date().toISOString()
        })
      })

      let result = await response.json()

      // Fallback to simple system if full system fails
      if (!response.ok || !result.success) {
        console.log('🔄 Falling back to simple view recording...')
        
        response = await fetch(`/api/test/post-interactions-simple`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'simulate-view',
            postId,
            source: 'unified'
          })
        })

        result = await response.json()
      }

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to record view')
      }

      // Mark as viewed locally to prevent duplicate views
      markAsViewed()
      setHasRecordedView(true)
      
      console.log('✅ Unified view recorded successfully for post:', postId)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to record view'
      setError(errorMessage)
      console.error('❌ Error recording unified view:', err)
    } finally {
      setIsRecordingView(false)
    }
  }, [postId, hasRecordedView, isRecordingView, checkIfAlreadyViewed, markAsViewed])

  /**
   * Handle viewport intersection
   */
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries
    const isCurrentlyInViewport = entry.isIntersecting

    setIsInViewport(isCurrentlyInViewport)

    if (isCurrentlyInViewport && !hasTriggeredRef.current && !hasRecordedView) {
      console.log('👁️ Post entered viewport (unified):', postId)
      
      // Clear any existing timer
      if (viewportTimerRef.current) {
        clearTimeout(viewportTimerRef.current)
      }
      
      // Set delay timer
      viewportTimerRef.current = setTimeout(() => {
        if (!hasRecordedView && !hasTriggeredRef.current) {
          hasTriggeredRef.current = true
          recordView()
        }
      }, delay)
    } else if (!isCurrentlyInViewport && viewportTimerRef.current) {
      // Clear timer if element exits viewport before delay
      clearTimeout(viewportTimerRef.current)
      viewportTimerRef.current = null
    }
  }, [postId, delay, hasRecordedView, recordView])

  /**
   * Setup intersection observer
   */
  const setupObserver = useCallback(() => {
    const element = elementRef.current
    
    if (!element || !enabled) {
      return
    }

    // Check if already viewed on mount
    if (checkIfAlreadyViewed()) {
      setHasRecordedView(true)
      return
    }

    // Check if Intersection Observer is supported
    if (!window.IntersectionObserver) {
      console.warn('IntersectionObserver not supported, recording view immediately')
      recordView()
      return
    }

    // Create observer
    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold,
      rootMargin: '0px'
    })

    // Start observing
    observerRef.current.observe(element)

    // Cleanup function
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
        observerRef.current = null
      }
      if (viewportTimerRef.current) {
        clearTimeout(viewportTimerRef.current)
        viewportTimerRef.current = null
      }
    }
  }, [enabled, threshold, handleIntersection, checkIfAlreadyViewed, recordView])

  // Setup observer when ref is available
  useEffect(() => {
    if (elementRef.current) {
      return setupObserver()
    }
  }, [setupObserver])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      if (viewportTimerRef.current) {
        clearTimeout(viewportTimerRef.current)
      }
    }
  }, [])

  return {
    ref: elementRef,
    isInViewport,
    hasRecordedView,
    isRecordingView,
    error,
    recordView
  }
}
