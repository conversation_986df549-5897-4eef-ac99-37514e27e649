// API helper functions for client-side requests

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ApiError {
  message: string
  status: number
  code?: string
}

export class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        throw new ApiError(
          data.error || data.message || 'Request failed',
          response.status,
          data.code
        )
      }

      return data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0
      )
    }
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint
    return this.request<T>(url, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// Default API client instance
export const apiClient = new ApiClient()

// Helper functions for common API operations
export const api = {
  // Posts
  posts: {
    getAll: (params?: Record<string, any>) => 
      apiClient.get('/posts', params),
    getById: (id: string) => 
      apiClient.get(`/posts/${id}`),
    getBySlug: (slug: string) => 
      apiClient.get(`/posts/slug/${slug}`),
    getFeatured: (limit?: number) => 
      apiClient.get('/posts/featured', limit ? { limit } : undefined),
    create: (data: any) => 
      apiClient.post('/posts', data),
    update: (id: string, data: any) => 
      apiClient.put(`/posts/${id}`, data),
    delete: (id: string) => 
      apiClient.delete(`/posts/${id}`),
    addReaction: (id: string, reactionType: string) => 
      apiClient.post(`/posts/${id}/reactions`, { reactionType }),
    search: (query: string, params?: Record<string, any>) => 
      apiClient.get('/posts/search', { q: query, ...params }),
  },

  // Post Types
  postTypes: {
    getAll: () => 
      apiClient.get('/post-types'),
    getById: (id: string) => 
      apiClient.get(`/post-types/${id}`),
    getBySlug: (slug: string) => 
      apiClient.get(`/post-types/slug/${slug}`),
    create: (data: any) => 
      apiClient.post('/post-types', data),
    update: (id: string, data: any) => 
      apiClient.put(`/post-types/${id}`, data),
    delete: (id: string) => 
      apiClient.delete(`/post-types/${id}`),
    getWithCounts: () => 
      apiClient.get('/post-types/with-counts'),
  },

  // Health check
  health: () => 
    apiClient.get('/health'),
}

// Error handling utilities
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError
}

export function getErrorMessage(error: any): string {
  if (isApiError(error)) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}

// Response utilities
export function createSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message
  }
}

export function createErrorResponse(error: string, message?: string): ApiResponse {
  return {
    success: false,
    error,
    message
  }
}
