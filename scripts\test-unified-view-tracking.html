<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Unified View Tracking (1 View Only)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .vs-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .vs-table th, .vs-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .vs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .vs-table .old {
            background-color: #f8d7da;
        }
        .vs-table .new {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <h1>🎯 Test: Unified View Tracking</h1>
    
    <div class="container">
        <div class="success">
            <h2>✅ Problem Solved: 1 View Only!</h2>
            <p><strong>Sekarang sistem memastikan hanya 1 view per post per 24 jam</strong>, tidak peduli user lihat via:</p>
            <ul>
                <li>📋 <strong>List/Homepage</strong> - Scroll dan lihat di viewport</li>
                <li>📄 <strong>Detail Page</strong> - Klik dan buka halaman detail</li>
                <li>⭐ <strong>Featured Posts</strong> - Scroll horizontal featured section</li>
            </ul>
            <p><strong>Hasil:</strong> Tetap hitung sebagai <span style="color: #28a745; font-weight: bold;">1 VIEW SAJA</span> ✅</p>
        </div>
    </div>

    <div class="container">
        <h2>🔧 How Unified Tracking Works</h2>
        
        <div class="feature-grid">
            <div class="feature-item">
                <strong>🗄️ 24h localStorage Cache</strong><br>
                Track viewed posts di browser storage
            </div>
            <div class="feature-item">
                <strong>🔍 Smart Detection</strong><br>
                Check if post already viewed today
            </div>
            <div class="feature-item">
                <strong>🚫 Duplicate Prevention</strong><br>
                Skip recording jika sudah viewed
            </div>
            <div class="feature-item">
                <strong>🔄 Auto Cleanup</strong><br>
                Remove old entries > 24h
            </div>
            <div class="feature-item">
                <strong>⚡ Fast Check</strong><br>
                localStorage check sebelum API call
            </div>
            <div class="feature-item">
                <strong>🎯 Unified API</strong><br>
                Single endpoint untuk semua sources
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Before vs After Comparison</h2>
        
        <table class="vs-table">
            <tr>
                <th>Scenario</th>
                <th class="old">❌ Before (Duplicate Views)</th>
                <th class="new">✅ After (Unified Tracking)</th>
            </tr>
            <tr>
                <td><strong>User scrolls homepage</strong></td>
                <td class="old">+1 view (viewport)</td>
                <td class="new">+1 view (unified)</td>
            </tr>
            <tr>
                <td><strong>Then clicks to detail page</strong></td>
                <td class="old">+1 view (detail) = <strong>2 TOTAL</strong></td>
                <td class="new">+0 view (already counted) = <strong>1 TOTAL</strong></td>
            </tr>
            <tr>
                <td><strong>User opens detail directly</strong></td>
                <td class="old">+1 view (detail)</td>
                <td class="new">+1 view (unified)</td>
            </tr>
            <tr>
                <td><strong>Then goes back to homepage</strong></td>
                <td class="old">+1 view (viewport) = <strong>2 TOTAL</strong></td>
                <td class="new">+0 view (already counted) = <strong>1 TOTAL</strong></td>
            </tr>
            <tr>
                <td><strong>Featured posts scroll</strong></td>
                <td class="old">+1 view per scroll</td>
                <td class="new">+1 view (unified, once only)</td>
            </tr>
            <tr>
                <td><strong>Multiple page visits same day</strong></td>
                <td class="old">Multiple views</td>
                <td class="new">1 view only per 24h</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>🧪 Test Instructions</h2>
        
        <div class="test-case">
            <h3>Test 1: Homepage → Detail (Should be 1 view)</h3>
            <ol>
                <li>Open browser console (F12)</li>
                <li>Go to homepage dan scroll perlahan</li>
                <li>Lihat log "Recording unified view for post"</li>
                <li>Click post untuk ke detail page</li>
                <li>Lihat log "Post already viewed today, skipping"</li>
                <li>Check view count - should only increase by 1</li>
            </ol>
            <a href="http://localhost:3000" class="link" target="_blank">Test Homepage First</a>
        </div>

        <div class="test-case">
            <h3>Test 2: Detail → Homepage (Should be 1 view)</h3>
            <ol>
                <li>Open detail page directly</li>
                <li>Lihat log "Recording unified view for post"</li>
                <li>Go back to homepage</li>
                <li>Scroll ke post yang sama</li>
                <li>Lihat log "Post already viewed today, skipping"</li>
                <li>Check view count - should only increase by 1</li>
            </ol>
            <a href="http://localhost:3000/posts/getting-started-with-react-hooks" class="link" target="_blank">Test Detail First</a>
        </div>

        <div class="test-case">
            <h3>Test 3: Featured Posts (Should be 1 view)</h3>
            <ol>
                <li>Scroll ke featured posts section</li>
                <li>Scroll horizontal untuk lihat posts</li>
                <li>Click featured post ke detail</li>
                <li>Should not double count</li>
            </ol>
            <a href="http://localhost:3000#featured" class="link" target="_blank">Test Featured Posts</a>
        </div>

        <div class="test-case">
            <h3>Test 4: 24h Reset (New day = new view)</h3>
            <ol>
                <li>View a post (gets counted)</li>
                <li>View same post again (should skip)</li>
                <li>Clear localStorage OR wait 24h</li>
                <li>View same post (should count again)</li>
            </ol>
            <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0;">
                <strong>Quick Test:</strong> Open console → <code>localStorage.clear()</code> → refresh page
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Debug Information</h2>
        
        <div class="warning">
            <h3>🛠️ Console Logs to Watch:</h3>
            <ul>
                <li><strong>✅ First View:</strong> "📊 Recording unified view for post: [id]"</li>
                <li><strong>✅ Success:</strong> "✅ Unified view recorded successfully for post: [id]"</li>
                <li><strong>🚫 Skip:</strong> "📊 Post already viewed today, skipping: [id]"</li>
                <li><strong>🔄 Fallback:</strong> "🔄 Falling back to simple view recording..."</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Expected Behavior:</h3>
            <ul>
                <li>First view dari manapun → Count +1</li>
                <li>Subsequent views same day → Count +0 (skipped)</li>
                <li>Console shows "already viewed today" messages</li>
                <li>localStorage contains viewed posts data</li>
                <li>View count accurate dan tidak duplicate</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>💾 localStorage Structure</h2>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace;">
// localStorage key: "viewed_posts_24h"<br>
{<br>
&nbsp;&nbsp;"post-id-1": "2024-01-15T10:30:00.000Z",<br>
&nbsp;&nbsp;"post-id-2": "2024-01-15T11:45:00.000Z",<br>
&nbsp;&nbsp;"post-id-3": "2024-01-15T14:20:00.000Z"<br>
}<br><br>
// Auto-cleanup removes entries older than 24h
        </div>
    </div>

    <div class="container">
        <h2>🎯 Benefits Achieved</h2>
        
        <div class="feature-grid">
            <div class="feature-item">
                <strong>📊 Accurate Analytics</strong><br>
                No more inflated view counts
            </div>
            <div class="feature-item">
                <strong>🎯 True Engagement</strong><br>
                1 view = 1 unique daily visitor
            </div>
            <div class="feature-item">
                <strong>⚡ Performance</strong><br>
                Fast localStorage checks
            </div>
            <div class="feature-item">
                <strong>🔄 Reliable</strong><br>
                Works across page navigations
            </div>
            <div class="feature-item">
                <strong>🧹 Self-Cleaning</strong><br>
                Auto-cleanup old entries
            </div>
            <div class="feature-item">
                <strong>🛡️ Robust</strong><br>
                Fallback system included
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Ready to Test!</h2>
        <p>Sistem unified view tracking sudah siap! Sekarang tidak akan ada duplicate views lagi.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">🏠 Start Testing</a>
            <a href="http://localhost:3000/posts/getting-started-with-react-hooks" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">📄 Test Detail Page</a>
        </div>
        
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center; margin: 20px 0;">
            <strong>🎯 Goal Achieved:</strong> 1 Post = 1 View per 24h (regardless of source)
        </div>
    </div>
</body>
</html>
