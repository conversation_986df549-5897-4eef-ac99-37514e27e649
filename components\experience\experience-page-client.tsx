"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { LanguageSwitcher } from "@/components/navigation/language-switcher"
import {
  ArrowLeft,
  Building,
  Calendar,
  MapPin,
  Award,
  TrendingUp,
  Code,
  Briefcase,
  Star,
  CheckCircle,
  Users,
  Target,
  Rocket,
  Zap,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import Link from "next/link"
import { Profile } from "@/types"

interface ExperiencePageClientProps {
  profile: Profile
  locale: string
  translations: {
    navigation: {
      backToHome: string
      experience: string
    }
    header: {
      title: string
      subtitle: string
      description: string
    }
    overview: {
      professionalJourney: string
      experienceOverview: string
      description: string
    }
    stats: {
      yearsExperience: string
      projectsDelivered: string
      companies: string
      roles: string
    }
    experience: {
      professionalExperience: string
      detailedJourney: string
      duration: string
      teamSize: string
      projects: string
      delivered: string
      keyHighlights: string
      coreTechnologies: string
      showDetails: string
      hideDetails: string
      keyAchievements: string
      notableProjects: string
      technologiesUsed: string
      members: string
      now: string
    }
    skills: {
      title: string
      description: string
      frontend: string
      backend: string
      devops: string
      tools: string
    }
  }
}

export function ExperiencePageClient({
  profile,
  locale,
  translations
}: ExperiencePageClientProps) {
  const [expandedExperience, setExpandedExperience] = useState<string | null>(null)

  const toggleExperience = (id: string) => {
    setExpandedExperience(expandedExperience === id ? null : id)
  }

  // Calculate stats from profile data
  const stats = {
    yearsOfExperience: 5,
    totalProjects: 15,
    companiesWorked: profile.experience.length,
    roles: profile.experience.length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-950 dark:via-gray-900 dark:to-gray-950 theme-transition">
      {/* Enhanced Header */}
      <header className="sticky top-0 z-50 border-b border-gray-200/50 dark:border-gray-800/50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl theme-transition">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="text-gray-600 hover:text-green-500 dark:text-gray-400 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition"
              >
                <Link href={`/${locale}`}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {translations.navigation.backToHome}
                </Link>
              </Button>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-700 theme-transition"></div>
              <div className="flex-1">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white theme-transition">
                  {translations.navigation.experience}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 theme-transition">
                  {translations.header.subtitle}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <LanguageSwitcher />
              <ThemeSwitcher />
            </div>
          </div>
        </div>
      </header>

      <main className="relative container mx-auto px-4 py-8 pb-32">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Experience Overview Stats */}
          <Card className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
            <CardContent className="p-8 relative overflow-hidden">
              <div className="relative">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">
                      {translations.overview.professionalJourney}
                    </span>
                  </div>
                  <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4 theme-transition">
                    {translations.overview.experienceOverview}
                  </h2>
                  <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed theme-transition">
                    {translations.overview.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { icon: Calendar, value: `${stats.yearsOfExperience}+`, label: translations.stats.yearsExperience },
                    { icon: Target, value: `${stats.totalProjects}+`, label: translations.stats.projectsDelivered },
                    { icon: Building, value: stats.companiesWorked, label: translations.stats.companies },
                    { icon: Briefcase, value: stats.roles, label: translations.stats.roles }
                  ].map((stat, index) => (
                    <div key={index} className="group/stat text-center p-6 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:scale-105 hover:-translate-y-1 theme-transition">
                      <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-800 rounded-lg group-hover/stat:bg-green-100 dark:group-hover/stat:bg-green-900/30 transition-all duration-300 group-hover/stat:scale-110">
                        <stat.icon className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover/stat:text-green-600 dark:group-hover/stat:text-green-400 transition-colors duration-300" />
                      </div>
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1 group-hover/stat:scale-110 transition-transform duration-300">{stat.value}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-500 font-medium theme-transition">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Professional Experience Section */}
          <div className="space-y-8">
            {/* Section Header */}
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-xl">
                <Briefcase className="w-6 h-6 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white theme-transition">
                  {translations.experience.professionalExperience}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 theme-transition">
                  {translations.experience.detailedJourney}
                </p>
              </div>
            </div>

            {/* Experience Cards */}
            <div className="space-y-6">
              {profile.experience.map((exp, index) => (
                <Card
                  key={exp.id}
                  className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition"
                >
                  <CardContent className="relative p-8">
                    <div className="flex flex-col lg:flex-row gap-8">
                      {/* Company Info */}
                      <div className="lg:w-1/3 space-y-4">
                        <div className="flex items-start gap-4">
                          <div className="relative group/company">
                            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 flex items-center justify-center group-hover/company:scale-110 transition-transform duration-300">
                              <Building className="w-8 h-8 text-gray-600 dark:text-gray-400" />
                            </div>
                            {/* Timeline connector */}
                            {index < profile.experience.length - 1 && (
                              <div className="absolute top-16 left-1/2 w-0.5 h-16 bg-gradient-to-b from-gray-300 dark:from-gray-600 to-transparent transform -translate-x-1/2" />
                            )}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1 theme-transition">{exp.position}</h3>
                            <p className="text-green-600 dark:text-green-400 font-semibold mb-3 theme-transition">{exp.company}</p>

                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 theme-transition">
                                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                                  <Calendar className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                                </div>
                                <span className="font-medium">{exp.duration}</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 theme-transition">
                                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                                  <MapPin className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                                </div>
                                <span className="font-medium">{exp.location}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg theme-transition">
                          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed theme-transition">{exp.description}</p>
                        </div>
                      </div>

                      {/* Details Section */}
                      <div className="lg:w-2/3 space-y-6">
                        {/* Quick Summary */}
                        <div className="space-y-4">
                          {/* Key Metrics */}
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            <div className="group/metric p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center group-hover/metric:bg-green-100 dark:group-hover/metric:bg-green-900/30 transition-colors duration-300">
                                  <Calendar className="w-3 h-3 text-gray-600 dark:text-gray-400 group-hover/metric:text-green-600 dark:group-hover/metric:text-green-400 transition-colors duration-300" />
                                </div>
                                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">{translations.experience.duration}</span>
                              </div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-white theme-transition">
                                {exp.duration.includes(locale === 'id' ? 'Sekarang' : 'Present') ?
                                  `${exp.duration.split(' - ')[0]} - ${translations.experience.now}` :
                                  exp.duration
                                }
                              </div>
                            </div>

                            <div className="group/metric p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center group-hover/metric:bg-green-100 dark:group-hover/metric:bg-green-900/30 transition-colors duration-300">
                                  <Users className="w-3 h-3 text-gray-600 dark:text-gray-400 group-hover/metric:text-green-600 dark:group-hover/metric:text-green-400 transition-colors duration-300" />
                                </div>
                                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">{translations.experience.teamSize}</span>
                              </div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-white theme-transition">
                                {index === 0 ? `5+ ${translations.experience.members}` : index === 1 ? `3-4 ${translations.experience.members}` : `2-3 ${translations.experience.members}`}
                              </div>
                            </div>

                            <div className="group/metric p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center group-hover/metric:bg-green-100 dark:group-hover/metric:bg-green-900/30 transition-colors duration-300">
                                  <Target className="w-3 h-3 text-gray-600 dark:text-gray-400 group-hover/metric:text-green-600 dark:group-hover/metric:text-green-400 transition-colors duration-300" />
                                </div>
                                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">{translations.experience.projects}</span>
                              </div>
                              <div className="text-sm font-semibold text-gray-900 dark:text-white theme-transition">
                                {exp.projects.length}+ {translations.experience.delivered}
                              </div>
                            </div>
                          </div>

                          {/* Role Highlights */}
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                                <Zap className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                              </div>
                              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300 theme-transition">{translations.experience.keyHighlights}</span>
                            </div>

                            <div className="grid gap-2">
                              {exp.achievements.slice(0, 2).map((achievement, idx) => (
                                <div key={idx} className="group/highlight flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                                  <div className="flex-shrink-0 w-5 h-5 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mt-0.5 group-hover/highlight:bg-green-100 dark:group-hover/highlight:bg-green-900/30 transition-colors duration-300">
                                    <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 group-hover/highlight:bg-green-500 dark:group-hover/highlight:bg-green-400 rounded-full transition-colors duration-300" />
                                  </div>
                                  <span className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed theme-transition">{achievement}</span>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Technology Preview */}
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                                <Code className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                              </div>
                              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300 theme-transition">{translations.experience.coreTechnologies}</span>
                            </div>

                            <div className="flex flex-wrap gap-2">
                              {exp.technologies.slice(0, 4).map((tech) => (
                                <Badge
                                  key={tech}
                                  variant="outline"
                                  className="border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-400 bg-gray-100/50 dark:bg-gray-800/50 hover:border-green-500/50 hover:text-green-600 dark:hover:border-green-500/70 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors duration-200 text-xs theme-transition"
                                >
                                  {tech}
                                </Badge>
                              ))}
                              {exp.technologies.length > 4 && (
                                <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-500 theme-transition">
                                  +{exp.technologies.length - 4} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Expandable Content Toggle */}
                        <div className="space-y-4">
                          <button
                            onClick={() => toggleExperience(exp.id)}
                            className="flex items-center justify-between w-full p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition"
                          >
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 theme-transition">
                              {expandedExperience === exp.id ? translations.experience.hideDetails : translations.experience.showDetails}
                            </span>
                            {expandedExperience === exp.id ? (
                              <ChevronUp className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                            ) : (
                              <ChevronDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                            )}
                          </button>

                          {expandedExperience === exp.id && (
                            <div className="space-y-6 animate-in slide-in-from-top-2 duration-300">
                              {/* Key Achievements */}
                              <div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2 theme-transition">
                                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                                    <Award className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                  </div>
                                  {translations.experience.keyAchievements}
                                </h4>
                                <div className="space-y-3">
                                  {exp.achievements.map((achievement, idx) => (
                                    <div key={idx} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                                      <div className="flex-shrink-0 w-6 h-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mt-0.5">
                                        <CheckCircle className="w-3 h-3 text-green-600 dark:text-green-400" />
                                      </div>
                                      <span className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed theme-transition">{achievement}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>

                              {/* Notable Projects */}
                              <div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2 theme-transition">
                                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                                    <Star className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                  </div>
                                  {translations.experience.notableProjects}
                                </h4>
                                <div className="grid gap-4">
                                  {exp.projects.map((project, idx) => (
                                    <div key={idx} className="group/project p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition">
                                      <div className="flex items-start gap-3">
                                        <div className="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center group-hover/project:bg-green-100 dark:group-hover/project:bg-green-900/30 transition-colors duration-300">
                                          <Rocket className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover/project:text-green-600 dark:group-hover/project:text-green-400 transition-colors duration-300" />
                                        </div>
                                        <div className="flex-1">
                                          <h5 className="font-semibold text-gray-900 dark:text-white mb-2 theme-transition">{project.name}</h5>
                                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 leading-relaxed theme-transition">{project.description}</p>
                                          <div className="flex items-center gap-2">
                                            <div className="flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-md">
                                              <TrendingUp className="w-3 h-3 text-green-600 dark:text-green-400" />
                                              <span className="text-xs text-green-600 dark:text-green-400 font-medium">{project.impact}</span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>

                              {/* Technologies */}
                              <div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2 theme-transition">
                                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                                    <Code className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                  </div>
                                  {translations.experience.technologiesUsed}
                                </h4>
                                <div className="flex flex-wrap gap-2">
                                  {exp.technologies.map((tech) => (
                                    <Badge
                                      key={tech}
                                      variant="outline"
                                      className="border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-400 bg-gray-100/50 dark:bg-gray-800/50 hover:border-green-500/50 hover:text-green-600 dark:hover:border-green-500/70 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors duration-200 theme-transition"
                                    >
                                      {tech}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
