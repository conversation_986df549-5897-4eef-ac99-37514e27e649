import { NextRequest } from 'next/server'
import { PostApiSupabaseService } from '@/services/post-api-supabase.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { RequestValidator } from '@/lib/api/validation'
import { withMiddleware, withErrorHandler, withLogging, withRateLimit } from '@/lib/api/middleware'
import { PaginationOptions } from '@/types/api'
import { PostFilters } from '@/types'

const postService = new PostApiSupabaseService()

// Validation schema for query parameters
const querySchema = {
  page: {
    type: 'number' as const,
    min: 1,
  },
  limit: {
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  search: {
    type: 'string' as const,
    maxLength: 255,
  },
  type: {
    type: 'string' as const,
    maxLength: 50,
  },
  status: {
    type: 'string' as const,
    enum: ['draft', 'published', 'archived'],
  },
  author: {
    type: 'string' as const,
    maxLength: 100,
  },
  featured: {
    type: 'boolean' as const,
  },
  sortBy: {
    type: 'string' as const,
    enum: ['createdAt', 'updatedAt', 'title', 'viewCount'],
  },
  sortOrder: {
    type: 'string' as const,
    enum: ['asc', 'desc'],
  },
}

async function getPostsHandler(request: NextRequest) {
  // Validate query parameters
  const validation = RequestValidator.validateQuery(request, querySchema)

  if (!validation.isValid) {
    return ApiResponseBuilder.validationError(
      'Invalid query parameters',
      validation.errors[0]?.field,
      { errors: validation.errors }
    )
  }

  // Extract pagination options
  const pagination: PaginationOptions = {
    page: validation.data?.page || 1,
    limit: validation.data?.limit || 10,
    sortBy: validation.data?.sortBy || 'createdAt',
    sortOrder: validation.data?.sortOrder || 'desc',
  }

  // Extract filter options
  const filters: PostFilters = {
    search: validation.data?.search,
    type: validation.data?.type,
    status: validation.data?.status,
    author: validation.data?.author,
    featured: validation.data?.featured,
  }

  // Get posts from service
  const result = await postService.getAllPosts(pagination, filters)

  // Calculate pagination metadata
  const totalPages = Math.ceil(result.total / pagination.limit!)
  const paginationMeta = {
    page: pagination.page!,
    limit: pagination.limit!,
    total: result.total,
    totalPages,
    hasNext: pagination.page! < totalPages,
    hasPrev: pagination.page! > 1,
  }

  return ApiResponseBuilder.success(
    {
      posts: result.posts,
      pagination: paginationMeta,
      filters
    },
    `Retrieved ${result.posts.length} posts`,
    200
  )
}

// Apply middleware to handlers
export const GET = withMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(100, 15 * 60 * 1000) // 100 requests per 15 minutes
)(getPostsHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
