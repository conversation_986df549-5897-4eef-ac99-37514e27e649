<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Single Post with Supabase Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-case h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .expected {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .link.api {
            background-color: #28a745;
        }
        .link.api:hover {
            background-color: #1e7e34;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .status-published {
            background-color: #d4edda;
            color: #155724;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <h1>📄 Test: Single Post with Supabase Integration</h1>
    
    <div class="instructions">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li><strong>Test existing posts:</strong> Click on post links to verify they load correctly</li>
            <li><strong>Check API responses:</strong> Verify data structure and completeness</li>
            <li><strong>Test 404 handling:</strong> Try non-existent post slugs</li>
            <li><strong>Verify SSR:</strong> Check page source for server-rendered content</li>
            <li><strong>Test metadata:</strong> Check page title and meta tags</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>🔗 Test Links</h2>
        
        <h3>📄 Published Posts:</h3>
        <a href="http://localhost:3000/posts/getting-started-with-react-hooks" class="link" target="_blank">React Hooks Post</a>
        <a href="http://localhost:3000/posts/future-of-web-development-opinion" class="link" target="_blank">Web Dev Future Post</a>
        <a href="http://localhost:3000/posts/building-scalable-applications" class="link" target="_blank">Scalable Apps Post</a>
        
        <h3>📊 API Test Endpoints:</h3>
        <a href="http://localhost:3000/api/test/single-post/getting-started-with-react-hooks" class="link api" target="_blank">React Hooks API</a>
        <a href="http://localhost:3000/api/test/single-post/future-of-web-development-opinion" class="link api" target="_blank">Web Dev API</a>
        <a href="http://localhost:3000/api/test/single-post/non-existent-post" class="link api" target="_blank">404 Test API</a>
        
        <h3>❌ 404 Tests:</h3>
        <a href="http://localhost:3000/posts/non-existent-post" class="link" target="_blank">Non-existent Post</a>
        <a href="http://localhost:3000/posts/draft-post-test" class="link" target="_blank">Draft Post Test</a>
    </div>

    <div class="test-container">
        <h2>🔍 Test Cases</h2>

        <div class="test-case">
            <h3>Test Case 1: Post Data Loading</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Click on any published post link above</li>
                <li>Verify page loads without errors</li>
                <li>Check all post data is displayed correctly</li>
            </ol>
            <div class="expected">
                <strong>Expected Behavior:</strong><br>
                ✅ Post title, content, author, and metadata display correctly<br>
                ✅ Post type badge shows with correct color and icon<br>
                ✅ Tags are displayed if available<br>
                ✅ View count and reactions show current values<br>
                ✅ Author information displays properly
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 2: API Data Structure</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Click on API test endpoints above</li>
                <li>Verify JSON response structure</li>
                <li>Check data completeness analysis</li>
            </ol>
            <div class="expected">
                <strong>Expected API Response:</strong><br>
                <div class="code">
{
  "success": true,
  "data": {
    "post": {
      "id": "...",
      "title": "...",
      "slug": "...",
      "status": "PUBLISHED",
      "author": {
        "id": "...",
        "name": "Admin User"
      },
      "type": {
        "name": "Article",
        "color": "#10b981",
        "icon": "file-text"
      },
      "reactionHart": 11,
      "viewCount": 828
    },
    "analysis": {
      "dataComplete": true,
      "hasTitle": true,
      "hasContent": true,
      "hasAuthor": true,
      "hasType": true
    }
  }
}
                </div>
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 3: 404 Handling</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Click on "Non-existent Post" link</li>
                <li>Verify 404 page is shown</li>
                <li>Test API endpoint for non-existent post</li>
            </ol>
            <div class="expected">
                <strong>Expected Behavior:</strong><br>
                ❌ Post page shows 404 Not Found<br>
                ❌ API returns 404 error response<br>
                ✅ No server errors or crashes<br>
                ✅ Proper error handling
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>✅ Supabase Integration Features</h2>
        <div class="feature-list">
            <div class="feature-item">
                <strong>SSR Data Fetching</strong><br>
                Posts loaded server-side with Supabase
            </div>
            <div class="feature-item">
                <strong>Slug-based Routing</strong><br>
                SEO-friendly URLs with slug lookup
            </div>
            <div class="feature-item">
                <strong>Complete Post Data</strong><br>
                Author, type, tags, reactions, views
            </div>
            <div class="feature-item">
                <strong>Error Handling</strong><br>
                Proper 404 for missing posts
            </div>
            <div class="feature-item">
                <strong>Metadata Generation</strong><br>
                Dynamic page titles and meta tags
            </div>
            <div class="feature-item">
                <strong>Type Safety</strong><br>
                Proper data mapping and validation
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🔧 Technical Implementation</h2>
        <div class="code">
// Single Post Page (app/posts/[slug]/page.tsx)
export default async function PostPage({ params }: PostPageProps) {
  const { slug } = await params
  const postService = new PostApiSimpleService()
  const post = await postService.getPostBySlug(slug)
  
  if (!post) {
    notFound()
  }
  
  return &lt;PostDetailClient post={post} /&gt;
}

// Repository Method (repositories/post-supabase-simple.repository.ts)
async findBySlug(slug: string): Promise&lt;any | null&gt; {
  const { data: post, error } = await this.supabase
    .from('posts')
    .select('*')
    .eq('slug', slug)
    .single()
    
  return post ? this.mapPostFromDatabase(post) : null
}
        </div>
    </div>

    <div class="test-container">
        <h2>🎯 Success Criteria</h2>
        <ul>
            <li>✅ Single post pages load correctly with Supabase data</li>
            <li>✅ All post metadata displays properly</li>
            <li>✅ Author information shows correctly</li>
            <li>✅ Post type and tags render properly</li>
            <li>✅ Reactions and view counts display</li>
            <li>✅ 404 handling works for missing posts</li>
            <li>✅ SSR implementation functional</li>
            <li>✅ SEO metadata generated correctly</li>
            <li>✅ No data structure errors</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>📊 Current Status</h2>
        <div class="success">
            <strong>✅ Implementation Complete:</strong><br>
            • Single post pages integrated with Supabase<br>
            • Proper data mapping for author and type objects<br>
            • SSR with slug-based routing<br>
            • Error handling for missing posts<br>
            • Dynamic metadata generation<br>
            • Reaction and view count display<br>
            • Type-safe data handling
        </div>
    </div>
</body>
</html>
