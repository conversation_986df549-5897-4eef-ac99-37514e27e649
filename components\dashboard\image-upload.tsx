import { useState, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react'

interface ImageUploadProps {
  label: string
  name: string
  value?: string | string[]
  onChange: (url: string | string[]) => void
  onRemove: (index?: number) => void
  error?: string
  disabled?: boolean
  className?: string
  description?: string
  accept?: string
  maxSize?: number // in MB
  multiple?: boolean
  maxFiles?: number
}

export function ImageUpload({
  label,
  name,
  value,
  onChange,
  onRemove,
  error,
  disabled = false,
  className,
  description,
  accept = "image/*",
  maxSize = 5,
  multiple = false,
  maxFiles = 5
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const hasError = !!error

  // Normalize value to always be an array for easier handling
  const images = multiple ? (Array.isArray(value) ? value : []) : (value ? [value] : [])
  const canAddMore = multiple ? images.length < maxFiles : images.length === 0

  const handleFileSelect = async (files: FileList) => {
    const validFiles = Array.from(files).filter(file => {
      if (file.size > maxSize * 1024 * 1024) {
        console.error(`File ${file.name} is too large`)
        return false
      }
      if (!file.type.startsWith('image/')) {
        console.error(`File ${file.name} is not an image`)
        return false
      }
      return true
    })

    if (validFiles.length === 0) return

    setIsUploading(true)

    try {
      const newUrls = validFiles.map(file => URL.createObjectURL(file))

      if (multiple) {
        const currentImages = Array.isArray(value) ? value : []
        const updatedImages = [...currentImages, ...newUrls].slice(0, maxFiles)
        onChange(updatedImages)
      } else {
        onChange(newUrls[0])
      }
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (disabled || isUploading || !canAddMore) return

    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileSelect(files)
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
  }

  const handleUrlInput = (url: string) => {
    if (url.trim()) {
      if (multiple) {
        const currentImages = Array.isArray(value) ? value : []
        const updatedImages = [...currentImages, url.trim()].slice(0, maxFiles)
        onChange(updatedImages)
      } else {
        onChange(url.trim())
      }
    }
  }

  const handleRemoveImage = (index: number) => {
    if (multiple) {
      const currentImages = Array.isArray(value) ? value : []
      const updatedImages = currentImages.filter((_, i) => i !== index)
      onChange(updatedImages)
    } else {
      onRemove()
    }
  }

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
        {multiple && images.length > 0 && (
          <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
            ({images.length}/{maxFiles})
          </span>
        )}
      </Label>

      {description && (
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}

      {/* Image Previews */}
      {images.length > 0 && (
        <div className={cn(
          "grid gap-4",
          multiple ? "grid-cols-2 md:grid-cols-3" : "grid-cols-1"
        )}>
          {images.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <div className="relative w-full h-32 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                <img
                  src={imageUrl}
                  alt={`Image ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => handleRemoveImage(index)}
                    disabled={disabled}
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-red-600 hover:bg-red-700 text-white"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upload Area */}
      {canAddMore && (
        <div
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onDragEnter={() => setIsDragging(true)}
          onDragLeave={() => setIsDragging(false)}
          className={cn(
            "relative w-full h-32 border-2 border-dashed rounded-lg transition-all duration-200",
            "flex flex-col items-center justify-center space-y-2",
            isDragging
              ? "border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20"
              : "border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50",
            hasError && "border-red-500 dark:border-red-400",
            !disabled && "hover:border-green-400 dark:hover:border-green-500 cursor-pointer",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleFileInput}
            disabled={disabled || isUploading}
            className="hidden"
          />

          <div className="text-center">
            <ImageIcon className={cn(
              "h-8 w-8 mx-auto mb-2",
              isDragging ? "text-green-500 dark:text-green-400" : "text-gray-400 dark:text-gray-500"
            )} />

            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {isUploading ? 'Uploading...' : `Drop ${multiple ? 'images' : 'image'} here or click to browse`}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                PNG, JPG, GIF up to {maxSize}MB{multiple ? ` (max ${maxFiles})` : ''}
              </p>
            </div>
          </div>

          {isUploading && (
            <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
            </div>
          )}
        </div>
      )}

      {/* URL Input Alternative */}
      {canAddMore && (
        <div className="space-y-2">
          <Label className="text-xs text-gray-600 dark:text-gray-400">
            Or enter image URL:
          </Label>
          <div className="flex space-x-2">
            <input
              type="url"
              placeholder="https://example.com/image.jpg"
              disabled={disabled}
              className={cn(
                "flex-1 px-3 py-2 text-sm rounded-md border",
                "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600",
                "text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
                "focus:border-green-500 dark:focus:border-green-400 focus:ring-1 focus:ring-green-500 dark:focus:ring-green-400",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleUrlInput((e.target as HTMLInputElement).value)
                  ;(e.target as HTMLInputElement).value = ''
                }
              }}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              disabled={disabled}
              className="border-green-600 dark:border-green-500 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
              onClick={(e) => {
                const input = e.currentTarget.previousElementSibling as HTMLInputElement
                handleUrlInput(input.value)
                input.value = ''
              }}
            >
              Add
            </Button>
          </div>
        </div>
      )}

      {hasError && (
        <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}
