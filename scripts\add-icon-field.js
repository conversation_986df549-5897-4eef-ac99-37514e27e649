const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const ICON_MAPPING = {
  'learning': 'BookOpen',
  'error-log': 'AlertCircle',
  'opinion': 'Lightbulb',
  'tip': 'Code',
  'showcase': 'Star',
  'photo': 'ImageIcon'
}

async function main() {
  console.log('🔧 Adding icon field to PostType table...')

  try {
    // Step 1: Add the icon column with a default value
    console.log('📝 Adding icon column...')
    await prisma.$executeRaw`ALTER TABLE "post_types" ADD COLUMN IF NOT EXISTS "icon" VARCHAR(50) DEFAULT 'BookOpen'`

    // Step 2: Update existing records with proper icon names
    console.log('🎨 Updating existing records with icon names...')
    
    for (const [slug, iconName] of Object.entries(ICON_MAPPING)) {
      await prisma.postType.updateMany({
        where: { slug: slug },
        data: { icon: iconName }
      })
      console.log(`✅ Updated ${slug} with icon: ${iconName}`)
    }

    // Step 3: Make the column NOT NULL
    console.log('🔒 Making icon column NOT NULL...')
    await prisma.$executeRaw`ALTER TABLE "post_types" ALTER COLUMN "icon" SET NOT NULL`

    // Verify the changes
    const postTypes = await prisma.postType.findMany({
      select: { slug: true, name: true, icon: true }
    })

    console.log('\n📊 Updated PostTypes:')
    postTypes.forEach(pt => {
      console.log(`   ${pt.name} (${pt.slug}): ${pt.icon}`)
    })

    console.log('\n🎉 Icon field added successfully!')

  } catch (error) {
    console.error('❌ Error adding icon field:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
