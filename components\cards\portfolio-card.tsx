import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Award, ExternalLink, Code2, Globe, Github, Star, TrendingUp, Users, Database, Layers, Rocket, CheckCircle } from "lucide-react"
import Link from "next/link"
import { PortfolioProject } from "@/types"

interface PortfolioCardProps {
  portfolio: PortfolioProject[]
  locale: string
  translations: {
    title: string
    projects: string
    viewAll: string
  }
}

export function PortfolioCard({ portfolio, locale, translations }: PortfolioCardProps) {
  const uniqueTechnologies = new Set(portfolio.flatMap((p) => p.technologies))
  const liveProjects = portfolio.filter(p => p.status === 'Live').length
  const featuredProject = portfolio.find(p => p.category === 'Full Stack') || portfolio[0]

  return (
    <Card className="group relative overflow-hidden bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm shadow-xl hover:shadow-2xl theme-transition hover:scale-[1.02] transition-all duration-300">
      {/* Advanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      {/* Code Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.03] dark:opacity-[0.08]" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2322c55e' fill-opacity='0.1'%3E%3Cpath d='M20 20l8-8h8l-8 8zm16 0l8-8h8l-8 8zm-16 16l8-8h8l-8 8zm32-16l8-8h8l-8 8z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundSize: '40px 40px'
      }} />

      <CardContent className="relative p-6 space-y-4">
        {/* Portfolio Overview Header */}
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
            <Layers className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white theme-transition">
              {translations.title}
            </h3>
            <p className="text-xs text-green-600 dark:text-green-400 font-medium">
              Full Stack Development
            </p>
            <div className="flex items-center gap-1 mt-1">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                Active Development
              </span>
            </div>
          </div>
        </div>

        {/* Portfolio Impact Summary */}
        <div className="p-3 bg-gradient-to-r from-gray-50/50 to-gray-100/50 dark:from-gray-800/30 dark:to-gray-900/30 rounded-lg border border-gray-200/50 dark:border-gray-700/50 theme-transition">
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed theme-transition">
            Developing modern web applications across{" "}
            <span className="inline-flex items-center gap-1 text-green-600 dark:text-green-400 font-semibold">
              <Database className="w-3 h-3" />
              multiple domains
            </span> with{" "}
            <span className="inline-flex items-center gap-1 text-green-600 dark:text-green-400 font-semibold">
              <TrendingUp className="w-3 h-3" />
              scalable architecture
            </span>.
          </p>
        </div>

        {/* Portfolio Overview Stats */}
        <div className="grid grid-cols-3 gap-3">
          <div className="group/stat text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 bg-gray-100 dark:bg-gray-800 rounded-lg group-hover/stat:bg-green-100 dark:group-hover/stat:bg-green-900/30 transition-colors duration-300">
              <Layers className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover/stat:text-green-600 dark:group-hover/stat:text-green-400 transition-colors duration-300" />
            </div>
            <div className="text-lg font-bold text-green-600 dark:text-green-400">{portfolio.length}+</div>
            <div className="text-xs text-gray-600 dark:text-gray-500 font-medium theme-transition">{translations.projects}</div>
          </div>

          <div className="group/stat text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 bg-gray-100 dark:bg-gray-800 rounded-lg group-hover/stat:bg-green-100 dark:group-hover/stat:bg-green-900/30 transition-colors duration-300">
              <Code2 className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover/stat:text-green-600 dark:group-hover/stat:text-green-400 transition-colors duration-300" />
            </div>
            <div className="text-lg font-bold text-green-600 dark:text-green-400">{uniqueTechnologies.size}+</div>
            <div className="text-xs text-gray-600 dark:text-gray-500 font-medium theme-transition">Technologies</div>
          </div>

          <div className="group/stat text-center p-3 bg-gradient-to-b from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-900/10 rounded-xl border border-green-200/50 dark:border-green-700/30 hover:border-green-300 dark:hover:border-green-600/50 transition-all duration-300">
            <div className="flex items-center justify-center w-8 h-8 mx-auto mb-2 bg-green-100 dark:bg-green-900/30 rounded-lg group-hover/stat:bg-green-200 dark:group-hover/stat:bg-green-800/50 transition-colors duration-300">
              <Globe className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-lg font-bold text-green-600 dark:text-green-400">50K+</div>
            <div className="text-xs text-gray-600 dark:text-gray-500 font-medium theme-transition">Users Served</div>
          </div>
        </div>

        {/* Technology Overview */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Database className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Technology Expertise</span>
          </div>

          <div className="flex flex-wrap gap-2">
            {["React", "Node.js", "TypeScript", "MongoDB", "AWS"].map((tech) => (
              <Badge
                key={tech}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-400 bg-gray-100/50 dark:bg-gray-800/50 hover:border-green-500/50 hover:text-green-600 dark:hover:border-green-500/70 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors duration-200 px-2 py-1 font-medium theme-transition"
              >
                {tech}
              </Badge>
            ))}
            <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-500 px-2 py-1 theme-transition">
              +{Math.max(0, uniqueTechnologies.size - 5)} more
            </Badge>
          </div>
        </div>

        {/* Project Categories */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Star className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">Project Categories</span>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="text-sm font-bold text-green-600 dark:text-green-400">Web Apps</div>
              <div className="text-xs text-gray-600 dark:text-gray-500">Full Stack</div>
            </div>

            <div className="text-center p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="text-sm font-bold text-green-600 dark:text-green-400">APIs</div>
              <div className="text-xs text-gray-600 dark:text-gray-500">Backend</div>
            </div>
          </div>
        </div>

        {/* Enhanced View Projects Button */}
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start text-gray-700 hover:text-green-500 hover:bg-green-500/10 dark:text-gray-300 dark:hover:text-green-400 transition-all duration-300 group/btn theme-transition py-3 h-auto"
          asChild
        >
          <Link href={`/${locale}/portfolio`}>
            <div className="flex items-center gap-2 flex-1">
              <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center group-hover/btn:bg-green-100 dark:group-hover/btn:bg-green-900/30 transition-colors duration-300">
                <Award className="w-4 h-4 text-gray-600 dark:text-gray-400 group-hover/btn:text-green-600 dark:group-hover/btn:text-green-400 transition-colors duration-300" />
              </div>
              <div className="text-left">
                <div className="text-sm font-medium">{translations.viewAll}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">{locale === 'id' ? 'Showcase portofolio lengkap' : 'Complete portfolio showcase'}</div>
              </div>
            </div>
            <ExternalLink className="w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform duration-300" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}
