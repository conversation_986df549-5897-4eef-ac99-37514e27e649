'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { Check, X, Loader2, RotateCcw, AlertCircle, Lightbulb } from 'lucide-react'

interface SlugCheckResult {
  available: boolean
  error?: string
  message?: string
  suggestions?: string[]
  existingPost?: {
    id: string
    title: string
  }
}

interface SlugInputProps {
  label: string
  name: string
  value: string
  onChange: (value: string) => void
  title?: string // Title value for auto-generation comparison
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
  description?: string
  maxLength?: number
  excludeId?: string // For edit mode
}

export function SlugInput({
  label,
  name,
  value,
  onChange,
  title,
  error,
  required = false,
  disabled = false,
  className,
  description,
  maxLength = 100,
  excludeId
}: SlugInputProps) {
  const [isChecking, setIsChecking] = useState(false)
  const [checkResult, setCheckResult] = useState<SlugCheckResult | null>(null)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [lastCheckedSlug, setLastCheckedSlug] = useState('')
  const [isManualChange, setIsManualChange] = useState(false)

  const debounceRef = useRef<NodeJS.Timeout>()
  const hasError = !!error || (checkResult && !checkResult.available)

  // Generate slug from title (same logic as in the parent component)
  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  // Check if current slug differs from auto-generated slug
  const getAutoGeneratedSlug = (): string => {
    return title ? generateSlug(title) : ''
  }

  const shouldShowSyncButton = (): boolean => {
    if (!title || !value || isChecking) return false
    const autoSlug = getAutoGeneratedSlug()
    return autoSlug !== value && autoSlug.length > 0
  }

  // Debounced slug check function
  const checkSlugAvailability = useCallback(async (slug: string) => {
    if (!slug || slug.length < 3) {
      setCheckResult(null)
      setIsChecking(false)
      return
    }

    // Skip if already checked this slug
    if (slug === lastCheckedSlug && checkResult) {
      setIsChecking(false)
      return
    }

    setIsChecking(true)
    setCheckResult(null)

    try {
      const params = new URLSearchParams({ slug })
      if (excludeId) {
        params.append('excludeId', excludeId)
      }

      const response = await fetch(`/api/dashboard/posts/check-slug?${params}`)
      const result: SlugCheckResult = await response.json()

      setCheckResult(result)
      setLastCheckedSlug(slug)

      if (!result.available && result.suggestions && result.suggestions.length > 0) {
        setShowSuggestions(true)
      }
    } catch (error) {
      console.error('Slug check error:', error)
      setCheckResult({
        available: false,
        error: 'Failed to check slug availability'
      })
    } finally {
      setIsChecking(false)
    }
  }, [lastCheckedSlug, checkResult, excludeId])

  // Handle input change with debounce
  const handleInputChange = (newValue: string) => {
    // Clean the slug: lowercase, replace spaces with hyphens, remove invalid chars
    const cleanedSlug = newValue
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')

    onChange(cleanedSlug)
    setIsManualChange(true)
    setShowSuggestions(false)

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    // Set new debounce
    debounceRef.current = setTimeout(() => {
      checkSlugAvailability(cleanedSlug)
    }, 500) // 500ms debounce
  }

  // Check slug when value changes externally (from title)
  useEffect(() => {
    if (!isManualChange && value && value !== lastCheckedSlug) {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }

      debounceRef.current = setTimeout(() => {
        checkSlugAvailability(value)
      }, 300) // Shorter debounce for auto-generated slugs
    }
  }, [value, isManualChange, checkSlugAvailability, lastCheckedSlug])

  // Sync slug with title function
  const handleSync = () => {
    if (!title) return

    const autoSlug = getAutoGeneratedSlug()
    if (autoSlug) {
      setShowSuggestions(false)
      setLastCheckedSlug('')
      setCheckResult(null)
      setIsManualChange(false)
      onChange(autoSlug) // This will trigger useEffect to check availability
    }
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion)
    setIsManualChange(true)
    setShowSuggestions(false)
    setLastCheckedSlug('')
    setCheckResult(null)
    checkSlugAvailability(suggestion)
  }

  // Get status icon
  const getStatusIcon = () => {
    if (isChecking) {
      return <Loader2 className="w-4 h-4 text-blue-500 dark:text-blue-400 animate-spin" />
    }

    if (checkResult) {
      if (checkResult.available) {
        return <Check className="w-4 h-4 text-green-500 dark:text-green-400" />
      } else {
        return <X className="w-4 h-4 text-red-500 dark:text-red-400" />
      }
    }

    return null
  }

  // Get status message
  const getStatusMessage = () => {
    if (isChecking) {
      return 'Checking availability...'
    }

    if (checkResult) {
      if (checkResult.available) {
        return checkResult.message || 'Slug is available!'
      } else {
        return checkResult.error || 'Slug is not available'
      }
    }

    return null
  }

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  return (
    <div className={className}>
      <div className="space-y-2">
        <Label htmlFor={name} className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>

        <div className="relative">
          <Input
            id={name}
            name={name}
            type="text"
            value={value}
            onChange={(e) => handleInputChange(e.target.value)}
            disabled={disabled}
            maxLength={maxLength}
            placeholder="post-url-slug"
            className={cn(
              "pr-20 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
              "focus:outline-none focus:border-green-500 dark:focus:border-green-400 focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
              hasError && "border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500 dark:focus:ring-red-400 focus-visible:ring-red-500 dark:focus-visible:ring-red-400",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          />

          {/* Status Icon & Sync Button */}
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {getStatusIcon()}
            {shouldShowSyncButton() && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleSync}
                className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Reset slug to match title"
              >
                <RotateCcw className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Status Message */}
        {getStatusMessage() && !isChecking && (
          <div className={cn(
            "flex items-center gap-2 text-sm",
            checkResult?.available && "text-green-600 dark:text-green-400",
            checkResult && !checkResult.available && "text-red-600 dark:text-red-400"
          )}>
            {getStatusIcon()}
            <span>{getStatusMessage()}</span>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}

        {/* Description */}
        {description && (
          <p className="text-xs text-gray-600 dark:text-gray-400">
            {description}
          </p>
        )}

        {/* Suggestions */}
        {showSuggestions && checkResult?.suggestions && checkResult.suggestions.length > 0 && (
          <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Suggested alternatives:
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {checkResult.suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="text-xs bg-white dark:bg-gray-800 border-yellow-300 dark:border-yellow-600 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-800/30"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
