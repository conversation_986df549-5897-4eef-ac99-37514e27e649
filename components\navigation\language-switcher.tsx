"use client"

import { useLocale, useTranslations } from '@/contexts/locale-context'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { locales, getLocaleDisplayName, type Locale } from '@/lib/i18n'
import { Flag } from '@/components/ui/flag-icons'
import { useState, useEffect } from 'react'

export function LanguageSwitcher() {
  const { locale } = useLocale()
  const t = useTranslations()
  const router = useRouter()
  const pathname = usePathname()
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  const switchLocale = (newLocale: Locale) => {
    // Remove the current locale from the pathname if it exists
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/'

    // Create the new path with the new locale
    const newPath = `/${newLocale}${pathWithoutLocale}`

    router.push(newPath)
  }

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="w-9 h-9 p-0 border-gray-300 bg-gray-100/50 hover:bg-gray-200/50 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50 backdrop-blur-sm theme-transition"
        disabled
      >
        <div className="w-[18px] h-[18px] bg-gray-300 dark:bg-gray-600 rounded-sm animate-pulse" />
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="w-9 h-9 p-0 border-gray-300 bg-gray-100/50 hover:bg-gray-200/50 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50 backdrop-blur-sm theme-transition"
          title={t('languageSwitcher.switchLanguage')}
        >
          <Flag locale={locale} size={18} />
          <span className="sr-only">{t('languageSwitcher.switchLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[140px]">
        {locales.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onClick={() => switchLocale(loc)}
            className={`flex items-center gap-3 cursor-pointer py-2 px-3 ${
              locale === loc ? 'bg-gray-100 dark:bg-gray-800' : ''
            }`}
          >
            <Flag locale={loc} size={18} />
            <span className="text-sm font-medium">
              {getLocaleDisplayName(loc)}
            </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
