"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import {
  ArrowLeft,
  GitBranch,
  Clock,
  ThumbsUp,
  Heart,
  Brain,
  MessageSquare,
  Share,
  Edit,
  MoreHorizontal,
  BookOpen,
  Eye,
  Reply,
} from "lucide-react"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { Thread } from "@/types"

interface ThreadDetailClientProps {
  thread: Thread
}

export function ThreadDetailClient({ thread }: ThreadDetailClientProps) {
  const [viewCount, setViewCount] = useState(127)
  const [newComment, setNewComment] = useState("")
  const [reactions, setReactions] = useState(thread.reactions)

  // <PERSON><PERSON> comments data - in real app this would come from database
  const mockComments = [
    {
      id: "1",
      author: "Sarah Chen",
      content: "Great tutorial! The Socket.io implementation is really clean. I especially liked how you handled the connection management.",
      createdAt: "2024-01-16",
      reactions: { thumbsUp: 5, heart: 2 }
    },
    {
      id: "2",
      author: "Mike Rodriguez",
      content: "This helped me solve a similar issue I was having with real-time updates. Thanks for sharing!",
      createdAt: "2024-01-17",
      reactions: { thumbsUp: 3, heart: 1 }
    }
  ]

  const typeColors = {
    learning: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
    "error-log": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
    opinion: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
    tip: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
    showcase: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
    photo: "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300",
  }

  const handleReaction = (type: 'thumbsUp' | 'heart' | 'brain') => {
    setReactions(prev => ({
      ...prev,
      [type]: prev[type] + 1
    }))
  }

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      // In real app, this would submit to API
      console.log('Submitting comment:', newComment)
      setNewComment("")
    }
  }

  useEffect(() => {
    // Simulate view count increment
    const timer = setTimeout(() => {
      setViewCount(prev => prev + 1)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 theme-transition">
      {/* Simplified Sticky Header */}
      <div className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800 theme-transition">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 theme-transition"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-700 theme-transition" />
              <div className="flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-green-500 dark:text-green-400" />
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-1 theme-transition">{thread.title}</h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ThemeSwitcher />
              <Button
                variant="outline"
                size="sm"
                className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:border-green-500/50 dark:hover:border-green-500/70 hover:bg-green-50 dark:hover:bg-green-900/20 bg-white/50 dark:bg-gray-800/50 transition-all duration-300 theme-transition"
              >
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:border-green-500/50 dark:hover:border-green-500/70 hover:bg-green-50 dark:hover:bg-green-900/20 bg-white/50 dark:bg-gray-800/50 transition-all duration-300 theme-transition"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:border-green-500/50 dark:hover:border-green-500/70 hover:bg-green-50 dark:hover:bg-green-900/20 bg-white/50 dark:bg-gray-800/50 transition-all duration-300 theme-transition"
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800 theme-transition">
                  <DropdownMenuItem className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 theme-transition">
                    Copy Link
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 theme-transition">
                    Report
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      <main className="relative container mx-auto px-4 py-8 max-w-4xl pb-32">
        {/* Thread Header */}
        <div className="mb-8">
          {/* Thread Meta Information */}
          <div className="flex items-center gap-4 mb-4">
            <Badge className={`${typeColors[thread.type as keyof typeof typeColors]}`}>
              {thread.type}
            </Badge>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-500 theme-transition">
              <GitBranch className="w-3 h-3" />
              <span>{thread.version}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-500 theme-transition">
              <Clock className="w-3 h-3" />
              <span>{thread.readTime}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-500 theme-transition">
              <Eye className="w-3 h-3" />
              <span>{viewCount} views</span>
            </div>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6 leading-tight theme-transition">
            {thread.title}
          </h1>

          {/* Author & Engagement Section */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <Avatar className="w-10 h-10 border-2 border-green-500">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback className="bg-green-600 text-white font-bold">
                  {thread.author.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-gray-900 dark:text-white theme-transition">{thread.author}</div>
                <div className="text-sm text-gray-600 dark:text-gray-500 theme-transition">Updated {thread.updatedAt}</div>
              </div>
            </div>

            {/* Reaction Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleReaction('thumbsUp')}
                className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 hover:border-blue-500/50 dark:hover:border-blue-500/70 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 theme-transition"
              >
                <ThumbsUp className="w-4 h-4 mr-1" />
                {reactions.thumbsUp}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleReaction('heart')}
                className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 hover:border-red-500/50 dark:hover:border-red-500/70 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 theme-transition"
              >
                <Heart className="w-4 h-4 mr-1" />
                {reactions.heart}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleReaction('brain')}
                className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 hover:border-purple-500/50 dark:hover:border-purple-500/70 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 theme-transition"
              >
                <Brain className="w-4 h-4 mr-1" />
                {reactions.brain}
              </Button>
            </div>
          </div>
        </div>

        {/* Thread Content */}
        <Card className="mb-8 bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition">
          <CardContent className="p-8">
            <div className="prose prose-gray dark:prose-invert max-w-none">
              <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 leading-relaxed font-sans theme-transition text-base">{thread.content}</pre>
            </div>
          </CardContent>
        </Card>

        {/* Comments Section */}
        <div className="space-y-6">
          {/* Comments Header */}
          <div className="flex items-center gap-3">
            <MessageSquare className="w-6 h-6 text-green-500 dark:text-green-400" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white theme-transition">Comments ({mockComments.length})</h2>
          </div>

          {/* Add Comment */}
          <Card className="bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition">
            <CardContent className="p-6">
              <div className="space-y-4">
                <Textarea
                  placeholder="Share your thoughts..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="min-h-[100px] bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 theme-transition"
                />
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim()}
                    className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white transition-all duration-300"
                  >
                    <Reply className="w-4 h-4 mr-2" />
                    Comment
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Comments List */}
          {mockComments.map((comment) => (
            <Card key={comment.id} className="bg-white dark:bg-gray-900/80 border-gray-200 dark:border-gray-800/50 theme-transition">
              <CardContent className="p-6">
                <div className="flex gap-4">
                  <Avatar className="w-10 h-10 border-2 border-gray-300 dark:border-gray-600">
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-white font-bold">
                      {comment.author.split(" ").map((n) => n[0]).join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="font-medium text-gray-900 dark:text-white theme-transition">{comment.author}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-500 theme-transition">{comment.createdAt}</div>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 mb-3 theme-transition">{comment.content}</p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 theme-transition"
                      >
                        <ThumbsUp className="w-3 h-3 mr-1" />
                        {comment.reactions.thumbsUp}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 theme-transition"
                      >
                        <Heart className="w-3 h-3 mr-1" />
                        {comment.reactions.heart}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 dark:text-gray-400 hover:text-green-500 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 theme-transition"
                      >
                        <Reply className="w-3 h-3 mr-1" />
                        Reply
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
