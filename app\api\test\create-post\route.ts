import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { z } from 'zod'
import { PostStatus } from '@/types'

const postService = new PostApiSimpleService()

// Validation schema for test create post
const testCreatePostSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().min(1, 'Slug is required'),
  content: z.string().min(1, 'Content is required'),
  typeId: z.string().min(1, 'Post type is required'),
  status: z.nativeEnum(PostStatus).default(PostStatus.DRAFT),
  featured: z.boolean().default(false),
  showFullContent: z.boolean().default(false),
  tags: z.array(z.string()).default([])
})

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing create post without authentication...')

    // Parse request body
    const body = await request.json()
    console.log('Request body:', body)

    // Validate request data
    const validatedData = testCreatePostSchema.parse(body)
    console.log('Validated data:', validatedData)

    // Use a dummy author ID for testing
    const dummyAuthorId = '00000000-0000-0000-0000-000000000000'

    // Create post using service
    const postData = {
      title: validatedData.title,
      slug: validatedData.slug,
      content: validatedData.content,
      authorId: dummyAuthorId, // Dummy author for testing
      typeId: validatedData.typeId,
      status: validatedData.status,
      featured: validatedData.featured || false,
      excerpt: validatedData.showFullContent ? false : true,
      tags: validatedData.tags || []
    }

    console.log('Creating post with data:', postData)
    const createdPost = await postService.createPost(postData)
    console.log('Post created successfully:', createdPost.id)

    return ApiResponseBuilder.success(
      createdPost,
      `Test post ${validatedData.status === 'PUBLISHED' ? 'published' : 'saved as draft'} successfully`,
      201
    )
  } catch (error) {
    console.error('❌ Test create post error:', error)
    
    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(
        'Validation failed',
        error.errors[0]?.path.join('.') || 'unknown',
        { errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        })) }
      )
    }

    if (error instanceof Error) {
      return ApiResponseBuilder.badRequest(error.message)
    }

    return ApiResponseBuilder.internalError('Failed to create test post')
  }
}
