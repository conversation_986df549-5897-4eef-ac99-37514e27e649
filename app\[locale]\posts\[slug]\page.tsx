import { notFound } from "next/navigation"
import { PostApiSimpleService } from "@/services/post-api-simple.service"
import { PostDetailClient } from "@/components/posts/post-detail-client"
import { Metadata } from 'next'

interface PostPageProps {
  params: Promise<{
    locale: string
    slug: string
  }>
}

export async function generateMetadata({ params }: PostPageProps): Promise<Metadata> {
  const { locale, slug } = await params

  // Fetch post data for metadata
  const postService = new PostApiSimpleService()
  const post = await postService.getPostBySlug(slug)

  if (!post) {
    return {
      title: 'Post Not Found'
    }
  }

  // Simple metadata without translations for now
  const siteTitle = locale === 'id' ? 'Blog Saya' : 'My Blog'
  const defaultDescription = locale === 'id' ? 'Artikel blog terbaru' : 'Latest blog articles'

  return {
    title: `${post.title} | ${siteTitle}`,
    description: post.excerpt || post.content?.substring(0, 160) || defaultDescription,
    keywords: post.tags?.join(', ') || (locale === 'id' ? 'blog, artikel' : 'blog, articles'),
  }
}

export default async function PostPage({ params }: PostPageProps) {
  // Await params before using its properties (Next.js 15 requirement)
  const { locale, slug } = await params

  // Fetch post data from database using slug
  const postService = new PostApiSimpleService()
  const post = await postService.getPostBySlug(slug)

  if (!post) {
    notFound()
  }

  // Static translations based on locale
  const translations = {
    header: {
      backToPosts: locale === 'id' ? 'Kembali' : 'Back',
      share: locale === 'id' ? 'Bagikan' : 'Share',
      edit: locale === 'id' ? 'Edit' : 'Edit',
      copyLink: locale === 'id' ? 'Salin Tautan' : 'Copy Link',
      report: locale === 'id' ? 'Laporkan' : 'Report'
    },
    content: {
      publishedOn: locale === 'id' ? 'Diterbitkan pada' : 'Published on',
      sharePost: locale === 'id' ? 'Bagikan Artikel' : 'Share this article',
      views: locale === 'id' ? 'tampilan' : 'views'
    }
  }

  return <PostDetailClient post={post} locale={locale} translations={translations} />
}
