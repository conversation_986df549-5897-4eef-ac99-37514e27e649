import type React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { LocaleProvider } from '@/contexts/locale-context'
import { LocaleHtmlWrapper } from '@/components/locale-html-wrapper'
import { locales, getMessages, type Locale } from '@/lib/i18n'
import { notFound } from 'next/navigation'



interface LocaleLayoutProps {
  children: React.ReactNode
  params: Promise<{
    locale: string
  }>
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default async function LocaleLayout({
  children,
  params
}: LocaleLayoutProps) {
  const { locale } = await params

  // Validate locale
  if (!locales.includes(locale as Locale)) {
    notFound()
  }

  // Load messages for the current locale
  const messages = await getMessages(locale as Locale)

  return (
    <LocaleProvider locale={locale as Locale} messages={messages}>
      <LocaleHtmlWrapper>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem={true}
          disableTransitionOnChange={false}
          themes={['light', 'dark', 'system']}
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </LocaleHtmlWrapper>
    </LocaleProvider>
  )
}
