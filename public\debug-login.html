<!DOCTYPE html>
<html>
<head>
    <title>Debug Login</title>
</head>
<body>
    <h1>Debug Login Test</h1>

    <div>
        <h2>Test Login API:</h2>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testMe()">Test /api/auth/me</button>
        <button onclick="clearCookies()">Clear Cookies</button>
    </div>

    <div id="result" style="margin-top: 20px; padding: 10px; background: #f0f0f0;"></div>

    <script>
        async function testLogin() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing login...';

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin123!@#'
                    })
                });

                const data = await response.json();

                result.innerHTML = `
                    <h3>Login Response:</h3>
                    <p>Status: ${response.status}</p>
                    <p>Success: ${data.success}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <h3>Cookies:</h3>
                    <p>${document.cookie}</p>
                `;

                console.log('Login response:', data);
                console.log('Response headers:', [...response.headers.entries()]);

            } catch (error) {
                result.innerHTML = `Error: ${error.message}`;
                console.error('Login error:', error);
            }
        }

        async function testMe() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing /api/auth/me...';

            try {
                const response = await fetch('/api/auth/me', {
                    method: 'GET',
                    credentials: 'include',
                });

                const data = await response.json();

                result.innerHTML = `
                    <h3>/api/auth/me Response:</h3>
                    <p>Status: ${response.status}</p>
                    <p>Success: ${data.success}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <h3>Cookies:</h3>
                    <p>${document.cookie}</p>
                `;

                console.log('/api/auth/me response:', data);

            } catch (error) {
                result.innerHTML = `Error: ${error.message}`;
                console.error('/api/auth/me error:', error);
            }
        }

        function clearCookies() {
            document.cookie = "auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            document.getElementById('result').innerHTML = 'Cookies cleared!';
        }
    </script>
</body>
</html>
