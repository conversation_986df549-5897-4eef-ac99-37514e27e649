"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { BookOpen, Terminal, ChevronDown } from "lucide-react"
import { Post } from "@/types"
import { POST_FILTERS } from "@/lib/constants"
import { PostCard } from "@/components/cards/post-card"
import { motion, AnimatePresence } from "framer-motion"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTranslations } from '@/contexts/locale-context'

interface PostsSectionProps {
  posts: Post[]
  locale?: string
}

export function PostsSection({ posts, locale = 'en' }: PostsSectionProps) {
  const t = useTranslations()
  const [activeTab, setActiveTab] = useState("all")

  const filteredPosts = activeTab === "all"
    ? posts
    : posts.filter((post) => post.type.slug === activeTab)

  // Show first 3 main filters + All
  const mainFilters = ["all", "learning", "opinion", "tip"]
  const remainingFilters = POST_FILTERS.filter(filter => !mainFilters.includes(filter))

  return (
    <section>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 theme-transition">
            <BookOpen className="w-5 h-5 text-gray-700 dark:text-gray-300" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white theme-transition">{t('postsSection.title')}</h2>
            <p className="text-sm text-gray-600 dark:text-gray-500 theme-transition">{t('postsSection.description')}</p>
          </div>
        </div>

        {/* Filter Tabs - Consistent with App Theme */}
        <div className="flex items-center gap-2">
          {/* Main Filters */}
          {mainFilters.map((tab) => {
            const isActive = activeTab === tab
            return (
              <Button
                key={tab}
                variant="outline"
                size="sm"
                onClick={() => setActiveTab(tab)}
                className={`
                  transition-all duration-200 theme-transition
                  ${isActive
                    ? "border-green-500 text-green-600 dark:text-green-400 bg-green-50/50 dark:bg-green-900/20 hover:bg-green-100/50 dark:hover:bg-green-900/30"
                    : "border-gray-300 text-gray-600 hover:text-green-500 hover:border-green-500 bg-gray-100/50 backdrop-blur-sm dark:border-gray-700 dark:text-gray-400 dark:hover:text-green-400 dark:bg-gray-800/50"
                  }
                `}
              >
                {t(`postsSection.filters.${tab}`)}
              </Button>
            )
          })}

          {/* More Options Dropdown */}
          {remainingFilters.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`
                    transition-all duration-200 theme-transition
                    ${remainingFilters.includes(activeTab)
                      ? "border-green-500 text-green-600 dark:text-green-400 bg-green-50/50 dark:bg-green-900/20 hover:bg-green-100/50 dark:hover:bg-green-900/30"
                      : "border-gray-300 text-gray-600 hover:text-green-500 hover:border-green-500 bg-gray-100/50 backdrop-blur-sm dark:border-gray-700 dark:text-gray-400 dark:hover:text-green-400 dark:bg-gray-800/50"
                    }
                  `}
                >
                  {remainingFilters.includes(activeTab)
                    ? t(`postsSection.filters.${activeTab}`)
                    : t('postsSection.more')
                  }
                  <ChevronDown className="w-3 h-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="min-w-[140px] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-gray-200/50 dark:border-gray-800/50 shadow-xl theme-transition"
              >
                {remainingFilters.map((tab) => (
                  <DropdownMenuItem
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`
                      text-sm cursor-pointer transition-all duration-200
                      ${activeTab === tab
                        ? "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50"
                      }
                    `}
                  >
                    {t(`postsSection.filters.${tab}`)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Terminal Prompt */}
      <div className="mb-6 p-4 bg-gray-100/80 border border-gray-200/50 dark:bg-gray-900/80 dark:border-gray-800/50 rounded-lg backdrop-blur-sm theme-transition">
        <div className="flex items-center gap-2 text-sm font-mono">
          <span className="text-green-500 dark:text-green-400">$</span>
          <span className="text-gray-600 dark:text-gray-400 theme-transition">postbook --filter {activeTab} --count</span>
        </div>
        <div className="mt-2 text-sm text-gray-600 dark:text-gray-500 font-mono theme-transition">
          Found {filteredPosts.length} posts matching filter "{activeTab}"
        </div>
      </div>

      {/* Posts Grid */}
      <div className="space-y-6">
        <AnimatePresence mode="wait">
          {filteredPosts.map((post, index) => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
                delay: index * 0.1
              }}
            >
              <PostCard post={post} variant="full" locale={locale} />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      <AnimatePresence mode="wait">
        {filteredPosts.length === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ type: "spring", stiffness: 260, damping: 20 }}
            className="text-center py-16"
          >
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-full flex items-center justify-center theme-transition">
              <Terminal className="w-12 h-12 text-gray-600 dark:text-gray-500 theme-transition" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-2 theme-transition">{t('postsSection.noPostsFound')}</h3>
            <p className="text-gray-600 dark:text-gray-500 mb-8 max-w-md mx-auto theme-transition">
              {t('postsSection.tryDifferentFilter')}
            </p>
            <Button
              onClick={() => setActiveTab("all")}
              variant="outline"
              className="border-green-500/30 text-green-500 hover:bg-green-500/10 hover:border-green-500 dark:text-green-400 theme-transition"
            >
              {t('postsSection.filters.all')}
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  )
}
