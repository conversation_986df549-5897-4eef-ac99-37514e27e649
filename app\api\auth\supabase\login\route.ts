import { NextRequest } from 'next/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { signInWithEmailPassword } from '@/lib/supabase/auth'
import { with<PERSON>rror<PERSON><PERSON><PERSON>, withLogging, withRateLimit } from '@/lib/api/middleware'
import { z } from 'zod'

// Validation schema for login request
const loginSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .max(100, 'Password must be less than 100 characters'),
})

async function loginHandler(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json()

    // Validate request data
    const validatedData = loginSchema.parse(body)

    // Attempt to sign in with Supabase
    const result = await signInWithEmailPassword(
      validatedData.email,
      validatedData.password
    )

    if (!result.success) {
      return ApiResponseBuilder.unauthorized(
        result.error || 'Invalid email or password',
        { field: 'credentials' }
      )
    }

    if (!result.session || !result.user) {
      return ApiResponseBuilder.unauthorized(
        'Authentication failed',
        { field: 'credentials' }
      )
    }

    // Return success with session info
    return ApiResponseBuilder.success(
      {
        user: {
          id: result.user.id,
          email: result.user.email,
          name: result.user.user_metadata?.name || result.user.email?.split('@')[0],
        },
        session: {
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token,
          expires_at: result.session.expires_at,
        },
      },
      'Login successful'
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(
        'Validation failed',
        error.errors[0]?.path.join('.') || 'unknown',
        {
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      )
    }

    console.error('Login error:', error)
    return ApiResponseBuilder.internalError('Login failed')
  }
}

// Apply middleware
export const POST = withErrorHandler(
  withLogging(
    withRateLimit(10, 15 * 60 * 1000)(loginHandler) // 10 requests per 15 minutes
  )
)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
