const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Slug generation function (same as TypeScript version)
function generateSlug(title) {
  return title
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+|-+$/g, '')
}

function generateUniqueSlug(baseSlug, existingSlugs) {
  let slug = baseSlug
  let counter = 1
  
  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`
    counter++
  }
  
  return slug
}

async function main() {
  console.log('🔧 Adding slug field to Post table...')

  try {
    // Step 1: Add the slug column as nullable first
    console.log('📝 Adding slug column...')
    await prisma.$executeRaw`ALTER TABLE "posts" ADD COLUMN IF NOT EXISTS "slug" VARCHAR(600)`

    // Step 2: Get all existing posts
    console.log('📚 Fetching existing posts...')
    const posts = await prisma.post.findMany({
      select: { id: true, title: true, slug: true }
    })

    if (posts.length === 0) {
      console.log('ℹ️  No posts found, skipping slug generation')
    } else {
      // Step 3: Generate slugs for existing posts
      console.log(`🎯 Generating slugs for ${posts.length} posts...`)
      
      const existingSlugs = []
      
      for (const post of posts) {
        if (!post.slug) {
          const baseSlug = generateSlug(post.title)
          const uniqueSlug = generateUniqueSlug(baseSlug, existingSlugs)
          
          await prisma.post.update({
            where: { id: post.id },
            data: { slug: uniqueSlug }
          })
          
          existingSlugs.push(uniqueSlug)
          console.log(`✅ Generated slug for "${post.title}": ${uniqueSlug}`)
        } else {
          existingSlugs.push(post.slug)
          console.log(`ℹ️  Post "${post.title}" already has slug: ${post.slug}`)
        }
      }
    }

    // Step 4: Make the column NOT NULL and UNIQUE
    console.log('🔒 Making slug column NOT NULL and UNIQUE...')
    await prisma.$executeRaw`ALTER TABLE "posts" ALTER COLUMN "slug" SET NOT NULL`
    await prisma.$executeRaw`CREATE UNIQUE INDEX IF NOT EXISTS "posts_slug_key" ON "posts"("slug")`
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "posts_slug_idx" ON "posts"("slug")`

    // Verify the changes
    const updatedPosts = await prisma.post.findMany({
      select: { title: true, slug: true }
    })

    console.log('\n📊 Updated Posts with Slugs:')
    updatedPosts.forEach(post => {
      console.log(`   "${post.title}" → ${post.slug}`)
    })

    console.log('\n🎉 Slug field added successfully!')

  } catch (error) {
    console.error('❌ Error adding slug field:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
