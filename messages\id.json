{"common": {"loading": "Memuat...", "error": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON>", "backToHome": "<PERSON><PERSON><PERSON> ke Beranda", "readMore": "Baca Selengkapnya", "viewAll": "<PERSON><PERSON>", "search": "<PERSON><PERSON>", "filter": "Filter", "all": "<PERSON><PERSON><PERSON>"}, "navigation": {"home": "Be<PERSON><PERSON>", "posts": "Artikel", "portfolio": "Portofolio", "experience": "Pengalaman", "about": "Tentang", "contact": "Kontak"}, "header": {"tagline": "Portofolio <PERSON>", "version": "v1.0.0", "searchPlaceholder": "<PERSON>i artikel..."}, "hero": {"title": "Berbagi Ide &", "subtitle": "<PERSON><PERSON><PERSON><PERSON>", "description": "Software Engineer & Full-Stack Developer yang bersemangat dalam menciptakan solusi inovatif dan berbagi pengetahuan melalui kode. <PERSON><PERSON>, saya juga senang menulis tentang wawasan teknologi, pen<PERSON><PERSON> hidup, dan praktik rekayasa perangkat lunak."}, "featuredPosts": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>nten pilihan yang layak dibaca"}, "postsSection": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> semua konten saya", "filters": {"all": "<PERSON><PERSON><PERSON>", "learning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opinion": "Opini", "tip": "Tips", "photo": "Foto", "showcase": "Showcase", "error-log": "Log <PERSON>r"}, "more": "<PERSON><PERSON><PERSON>", "noPostsFound": "Tidak ada artikel di<PERSON>n", "tryDifferentFilter": "Coba filter atau kata kunci pencarian yang berbeda"}, "sidebar": {"profile": {"title": "Profil", "location": "Jakarta, Indonesia", "status": "Tersedia untuk bekerja", "contact": "<PERSON><PERSON><PERSON><PERSON>"}, "experience": {"title": "Pengalaman", "current": "Saat ini", "years": "tahun", "viewAll": "<PERSON><PERSON>"}, "portfolio": {"title": "Portofolio", "projects": "Proyek", "viewAll": "<PERSON><PERSON>"}, "stats": {"title": "Statistik", "posts": "Artikel", "views": "Tampilan", "reactions": "<PERSON><PERSON><PERSON>"}}, "postCard": {"readTime": "menit baca", "reactions": "<PERSON><PERSON>i", "comments": "komentar", "views": "tampilan", "featured": "<PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>"}, "postDetail": {"publishedOn": "Diterbitkan pada", "lastUpdated": "<PERSON><PERSON><PERSON>", "readTime": "menit baca", "sharePost": "Bagikan Artikel", "relatedPosts": "Art<PERSON><PERSON>", "backToPosts": "Ke<PERSON>li ke Artikel", "author": "<PERSON><PERSON><PERSON>", "tags": "Tag", "share": "Bagikan", "edit": "Edit", "copyLink": "<PERSON><PERSON>", "report": "Laporkan", "like": "<PERSON><PERSON>", "unlike": "<PERSON><PERSON>", "comments": "Komentar", "noComments": "Belum ada komentar", "addComment": "Tambahkan komentar", "writeComment": "<PERSON><PERSON> k<PERSON>nta<PERSON>...", "postComment": "<PERSON><PERSON>", "reply": "<PERSON><PERSON>"}, "languageSwitcher": {"switchLanguage": "Ganti Bahasa", "currentLanguage": "Bahasa Saat Ini"}, "portfolio": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Menciptakan Solusi Digital", "description": "Showcase karya terbaik saya dalam pengembangan web, menampilkan aplikasi full-stack, visualisasi data, dan solusi inovatif yang menyelesaikan masalah dunia nyata.", "categories": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON>i proyek...", "viewProject": "<PERSON><PERSON>", "viewCode": "<PERSON><PERSON>", "caseStudy": "<PERSON><PERSON>", "technologies": "Teknologi", "features": "<PERSON><PERSON>", "challenges": "Tantangan", "solutions": "<PERSON><PERSON><PERSON>", "metrics": "<PERSON><PERSON>", "status": "Status", "duration": "<PERSON><PERSON><PERSON>", "team": "<PERSON>", "role": "<PERSON><PERSON>", "stats": {"projects": "Proyek", "users": "Pengguna", "rating": "Rating"}, "cta": {"startProject": "<PERSON><PERSON>", "viewExperience": "<PERSON><PERSON>"}}, "experience": {"title": "Pengalaman Profesional", "subtitle": "<PERSON><PERSON><PERSON><PERSON> karir dan pen<PERSON>n", "description": "Pandangan komprehensif tentang perjalanan profesional, k<PERSON><PERSON><PERSON> tek<PERSON>, dan pencapaian karir saya dalam pengembangan software.", "overview": {"professionalJourney": "Perjalanan Profesional", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pandangan komprehensif tentang perjalanan profesional, k<PERSON><PERSON><PERSON> tek<PERSON>, dan pencapaian karir saya dalam pengembangan software."}, "stats": {"yearsExperience": "<PERSON><PERSON>", "projectsDelivered": "Proyek Diselesaikan", "companies": "<PERSON><PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON>"}, "section": {"title": "Pengalaman Profesional", "description": "<PERSON><PERSON><PERSON><PERSON> karir dan pencapaian detail"}, "details": {"duration": "<PERSON><PERSON><PERSON>", "teamSize": "<PERSON><PERSON><PERSON>", "projects": "Proyek", "delivered": "Diselesaikan", "keyHighlights": "<PERSON><PERSON><PERSON>", "coreTechnologies": "Teknologi Inti", "showDetails": "<PERSON><PERSON><PERSON><PERSON>", "hideDetails": "Sembunyikan Detail", "keyAchievements": "Pencapaian Utama", "notableProjects": "Proyek Terkemuka", "technologiesUsed": "Teknologi yang <PERSON>", "members": "Anggota", "now": "<PERSON><PERSON><PERSON>"}, "skills": {"title": "<PERSON><PERSON><PERSON>", "description": "Teknologi dan tools yang saya gunakan", "frontend": "Frontend", "backend": "Backend", "devops": "DevOps", "tools": "Tools"}}, "meta": {"title": "Threadbook - <PERSON><PERSON><PERSON><PERSON>", "description": "Platform hybrid yang menggabungkan thread Twitter, repositori GitHub, dan fungsi blogging untuk mengorganisir dan berb<PERSON> pemikiran.", "keywords": "blog, portofolio, software engineer, full-stack developer, teknologi, pemrograman"}}