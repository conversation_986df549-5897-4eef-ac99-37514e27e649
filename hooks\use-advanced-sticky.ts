"use client"

import { useEffect, useState, useRef, useCallback } from 'react'

interface UseAdvancedStickyOptions {
  topOffset?: number
  bottomOffset?: number
  enabled?: boolean
  containerSelector?: string
}

export function useAdvancedSticky(options: UseAdvancedStickyOptions = {}) {
  const {
    topOffset = 96, // Default top offset (header height + padding)
    bottomOffset = 32, // Default bottom offset
    enabled = true,
    containerSelector = 'main'
  } = options

  const elementRef = useRef<HTMLDivElement>(null)
  const placeholderRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)
  const [isAtBottom, setIsAtBottom] = useState(false)
  const [elementWidth, setElementWidth] = useState(0)
  const [elementHeight, setElementHeight] = useState(0)
  const [originalTop, setOriginalTop] = useState(0)

  // Calculate initial position and dimensions
  const updateInitialPosition = useCallback(() => {
    const element = elementRef.current
    if (!element) return

    const rect = element.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    
    setOriginalTop(rect.top + scrollTop)
    setElementWidth(rect.width)
    setElementHeight(rect.height)
  }, [])

  // Main scroll handler with proper sticky behavior
  const handleScroll = useCallback(() => {
    if (!enabled) return

    const element = elementRef.current
    const placeholder = placeholderRef.current
    const container = document.querySelector(containerSelector) as HTMLElement
    
    if (!element || !placeholder || !container) return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top + scrollTop
    const containerBottom = containerTop + container.offsetHeight

    // Calculate when sidebar should become sticky
    // Sticky activates when the original position would be above the topOffset
    const stickyThreshold = originalTop - topOffset
    const shouldBeSticky = scrollTop >= stickyThreshold

    // Calculate when sidebar should unstick at bottom
    // Unstick when the bottom of the viewport reaches the bottom of the container
    const viewportBottom = scrollTop + windowHeight
    const shouldUnstickAtBottom = viewportBottom >= (containerBottom - bottomOffset)

    const newIsSticky = shouldBeSticky && !shouldUnstickAtBottom
    const newIsAtBottom = shouldUnstickAtBottom && shouldBeSticky

    // Update sticky state
    if (newIsSticky !== isSticky) {
      setIsSticky(newIsSticky)
      
      if (newIsSticky) {
        // Show placeholder to maintain layout
        placeholder.style.height = `${elementHeight}px`
        placeholder.style.display = 'block'
      } else {
        // Hide placeholder
        placeholder.style.display = 'none'
      }
    }

    // Update bottom state
    if (newIsAtBottom !== isAtBottom) {
      setIsAtBottom(newIsAtBottom)
    }
  }, [enabled, originalTop, topOffset, bottomOffset, containerSelector, isSticky, isAtBottom, elementHeight])

  // Handle resize events
  const handleResize = useCallback(() => {
    updateInitialPosition()
    handleScroll()
  }, [updateInitialPosition, handleScroll])

  useEffect(() => {
    if (!enabled) return

    const element = elementRef.current
    if (!element) return

    // Initial setup
    updateInitialPosition()

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleResize, { passive: true })
    
    // Initial scroll calculation with small delay to ensure DOM is ready
    const timeoutId = setTimeout(handleScroll, 100)

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
      clearTimeout(timeoutId)
    }
  }, [handleScroll, handleResize, updateInitialPosition, enabled])

  // Generate styles based on current state
  const getStickyStyles = (): React.CSSProperties => {
    if (!enabled) return {}

    if (isAtBottom) {
      // When at bottom, position absolutely at the bottom of container
      return {
        position: 'absolute',
        bottom: `${bottomOffset}px`,
        width: `${elementWidth}px`,
        left: 0,
      }
    }

    if (isSticky) {
      // When sticky, fix to top of viewport
      return {
        position: 'fixed',
        top: `${topOffset}px`,
        width: `${elementWidth}px`,
        zIndex: 10,
      }
    }

    // Normal state - relative positioning
    return {
      position: 'relative',
    }
  }

  // Generate CSS classes for easier styling
  const getStickyClasses = () => {
    const baseClasses = 'transition-all duration-200 ease-in-out'
    
    if (isAtBottom) {
      return `${baseClasses} absolute bottom-8 left-0`
    }
    
    if (isSticky) {
      return `${baseClasses} fixed z-10 shadow-lg`
    }
    
    return `${baseClasses} relative`
  }

  return {
    elementRef,
    placeholderRef,
    isSticky,
    isAtBottom,
    getStickyStyles,
    getStickyClasses,
    elementWidth,
    elementHeight,
    originalTop,
  }
}

/**
 * Simplified sticky hook for basic use cases
 */
export function useSimpleSticky(options: { topOffset?: number; enabled?: boolean } = {}) {
  const {
    topOffset = 96,
    enabled = true
  } = options

  return useAdvancedSticky({
    topOffset,
    enabled,
    containerSelector: 'main'
  })
}
