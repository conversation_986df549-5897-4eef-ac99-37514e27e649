/**
 * Cookie utilities for authentication
 */

export function setAuthCookie(token: string) {
  if (typeof document !== 'undefined') {
    // Set cookie with proper settings
    const expires = new Date()
    expires.setTime(expires.getTime() + (60 * 60 * 1000)) // 1 hour
    
    document.cookie = `supabase_token=${token}; path=/; expires=${expires.toUTCString()}; SameSite=Lax; Secure=${window.location.protocol === 'https:'}`
    
    console.log('Auth cookie set:', { token: token.substring(0, 20) + '...', expires })
  }
}

export function clearAuthCookie() {
  if (typeof document !== 'undefined') {
    document.cookie = 'supabase_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
    console.log('Auth cookie cleared')
  }
}

export function getAuthCookie(): string | null {
  if (typeof document !== 'undefined') {
    const cookies = document.cookie.split(';')
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === 'supabase_token') {
        return value
      }
    }
  }
  return null
}
