import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

const postService = new PostApiSimpleService()

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 TEST: Creating post with images...')

    // Use actual user ID from database
    const mockUserId = '66c32a84-b0b5-4ede-a4f3-2cc919e756b6' // Valid user ID from user_profiles

    // Test post data with images
    const testPostData = {
      title: 'Test Post with Images - ' + new Date().toISOString(),
      slug: 'test-post-images-' + Date.now(),
      content: 'This is a test post created to verify image upload functionality. It should create records in both posts and post_images tables.',
      authorId: mockUserId,
      typeId: '3c686fa8-a62d-4cec-b4cc-59609109c55d', // Photo type ID
      status: 'DRAFT',
      featured: false,
      excerpt: true,
      tags: ['test', 'images', 'debug'],
      images: [
        {
          url: 'https://picsum.photos/800/600?random=1',
          altText: 'Random test image 1 for accessibility',
          caption: 'This is the first test image with a descriptive caption',
          name: 'test-image-1.jpg',
          size: 156789
        },
        {
          url: 'https://picsum.photos/800/600?random=2',
          altText: 'Random test image 2 for accessibility',
          caption: 'This is the second test image with a descriptive caption',
          name: 'test-image-2.jpg',
          size: 234567
        },
        {
          url: 'https://picsum.photos/800/600?random=3',
          altText: '', // Test empty alt text
          caption: '', // Test empty caption
          name: 'test-image-3.jpg',
          size: 345678
        }
      ]
    }

    console.log('📤 Creating post with data:', {
      ...testPostData,
      images: testPostData.images.map(img => ({
        name: img.name,
        size: img.size,
        hasAltText: !!img.altText,
        hasCaption: !!img.caption
      }))
    })

    // Create post using service
    const createdPost = await postService.createPost(testPostData)
    console.log('✅ Post created successfully:', createdPost.id)

    return ApiResponseBuilder.success(
      {
        post: createdPost,
        imagesCount: testPostData.images.length,
        testData: {
          expectedImages: testPostData.images.length,
          postId: createdPost.id
        }
      },
      `Test post created successfully with ${testPostData.images.length} images`,
      201
    )

  } catch (error) {
    console.error('💥 Error in test create post:', error)

    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to create test post'
    )
  }
}

export async function GET() {
  return ApiResponseBuilder.success(
    {
      message: 'Test endpoint for creating posts with images',
      usage: 'Send POST request to create a test post with images',
      expectedBehavior: [
        'Creates a post in posts table',
        'Creates image records in post_images table',
        'Returns post data with image count',
        'Logs debug information to console'
      ]
    },
    'Test endpoint ready'
  )
}
