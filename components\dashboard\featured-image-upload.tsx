'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { Upload, X, ImageIcon, AlertCircle, Loader2 } from 'lucide-react'
import Image from 'next/image'

interface FeaturedImageUploadProps {
  label: string
  name: string
  value?: string
  onChange: (url: string) => void
  onRemove: () => void
  error?: string
  disabled?: boolean
  className?: string
  description?: string
  maxSize?: number // in MB
}

export function FeaturedImageUpload({
  label,
  name,
  value,
  onChange,
  onRemove,
  error,
  disabled = false,
  className,
  description,
  maxSize = 5
}: FeaturedImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const hasError = !!error

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`
    }
    if (!file.type.startsWith('image/')) {
      return 'File must be an image'
    }
    return null
  }

  const handleFileSelect = async (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      console.error(validationError)
      return
    }

    setIsUploading(true)
    
    try {
      // For now, we'll create a local URL
      // In production, you'd upload to a cloud service like AWS S3, Cloudinary, etc.
      const url = URL.createObjectURL(file)
      onChange(url)
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    if (disabled || isUploading) return
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      handleFileSelect(file)
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleUrlInput = (url: string) => {
    if (url.trim()) {
      onChange(url.trim())
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={cn("space-y-4", className)}>
      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </Label>
      
      {description && (
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}

      {value ? (
        // Preview existing image
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 overflow-hidden">
          <CardContent className="p-0">
            <div className="relative aspect-video bg-gray-100 dark:bg-gray-800">
              <Image
                src={value}
                alt="Featured image preview"
                fill
                className="object-cover"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onRemove}
                disabled={disabled}
                className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white w-8 h-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            <div className="p-3 bg-gray-50 dark:bg-gray-800/50">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Featured Image
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        // Upload area
        <div
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onDragEnter={() => setIsDragging(true)}
          onDragLeave={() => setIsDragging(false)}
          className={cn(
            "relative border-2 border-dashed rounded-lg p-8 transition-all duration-200",
            "flex flex-col items-center justify-center space-y-4",
            isDragging
              ? "border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20"
              : "border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50",
            hasError && "border-red-500 dark:border-red-400",
            !disabled && "hover:border-green-400 dark:hover:border-green-500 cursor-pointer",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInput}
            disabled={disabled || isUploading}
            className="hidden"
          />
          
          <div className="text-center">
            <div className="flex justify-center mb-4">
              {isUploading ? (
                <Loader2 className="w-12 h-12 text-green-500 dark:text-green-400 animate-spin" />
              ) : (
                <div className="p-3 bg-gray-200 dark:bg-gray-700/50 rounded-full">
                  <ImageIcon className="w-8 h-8 text-green-500 dark:text-green-400" />
                </div>
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {isUploading ? 'Uploading image...' : 'Add featured image'}
            </h3>

            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Drag and drop an image here, or click to browse
            </p>

            <Button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Upload className="w-4 h-4 mr-2" />
              Choose Image
            </Button>

            <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">
              Support for JPEG, PNG, GIF, WebP up to {maxSize}MB
            </p>
          </div>
          
          {isUploading && (
            <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 flex items-center justify-center rounded-lg">
              <div className="text-center">
                <Loader2 className="w-8 h-8 text-green-500 dark:text-green-400 animate-spin mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400">Uploading...</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* URL Input Alternative */}
      {!value && (
        <div className="space-y-2">
          <Label className="text-xs text-gray-600 dark:text-gray-400">
            Or enter image URL:
          </Label>
          <div className="flex space-x-2">
            <input
              type="url"
              placeholder="https://example.com/image.jpg"
              disabled={disabled}
              className={cn(
                "flex-1 px-3 py-2 text-sm rounded-md border",
                "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600",
                "text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
                "focus:border-green-500 dark:focus:border-green-400 focus:ring-1 focus:ring-green-500 dark:focus:ring-green-400",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleUrlInput((e.target as HTMLInputElement).value)
                  ;(e.target as HTMLInputElement).value = ''
                }
              }}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              disabled={disabled}
              className="border-green-600 dark:border-green-500 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
              onClick={(e) => {
                const input = e.currentTarget.previousElementSibling as HTMLInputElement
                handleUrlInput(input.value)
                input.value = ''
              }}
            >
              Add
            </Button>
          </div>
        </div>
      )}
      
      {hasError && (
        <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}
