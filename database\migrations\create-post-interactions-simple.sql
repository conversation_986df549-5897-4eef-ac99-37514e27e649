-- Create post_likes table for tracking likes
CREATE TABLE post_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create post_views table for tracking views (1x per 24h)
CREATE TABLE post_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    user_identifier TEXT NOT NULL,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_post_likes_post_id ON post_likes(post_id);
CREATE INDEX idx_post_likes_user_id ON post_likes(user_id);
CREATE INDEX idx_post_likes_identifier ON post_likes(user_identifier);
CREATE INDEX idx_post_likes_post_user ON post_likes(post_id, user_identifier);

CREATE INDEX idx_post_views_post_id ON post_views(post_id);
CREATE INDEX idx_post_views_user_id ON post_views(user_id);
CREATE INDEX idx_post_views_identifier ON post_views(user_identifier);
CREATE INDEX idx_post_views_date ON post_views(viewed_at);

-- Create unique constraint for views (1 per day per user)
CREATE UNIQUE INDEX idx_post_views_unique_daily 
ON post_views(post_id, user_identifier, DATE(viewed_at));

-- Enable RLS
ALTER TABLE post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_views ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Allow read access to post_likes" ON post_likes
    FOR SELECT USING (true);

CREATE POLICY "Allow read access to post_views" ON post_views
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to post_likes" ON post_likes
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow insert access to post_views" ON post_views
    FOR INSERT WITH CHECK (true);

-- Create functions to update post counts
CREATE OR REPLACE FUNCTION update_post_reaction_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET reactions_heart = (
        SELECT COUNT(*) 
        FROM post_likes 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_post_view_count(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE posts 
    SET view_count = (
        SELECT COUNT(DISTINCT user_identifier) 
        FROM post_views 
        WHERE post_id = post_uuid
    )
    WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create trigger functions
CREATE OR REPLACE FUNCTION trigger_update_reaction_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_reaction_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_reaction_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trigger_update_view_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_post_view_count(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_post_view_count(OLD.post_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_post_likes_count
    AFTER INSERT OR DELETE ON post_likes
    FOR EACH ROW EXECUTE FUNCTION trigger_update_reaction_count();

CREATE TRIGGER trigger_post_views_count
    AFTER INSERT OR DELETE ON post_views
    FOR EACH ROW EXECUTE FUNCTION trigger_update_view_count();
