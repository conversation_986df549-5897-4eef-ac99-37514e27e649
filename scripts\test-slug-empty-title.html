<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Slug Empty Title Behavior</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-case h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .expected {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test: Slug Empty Title Behavior</h1>
    
    <div class="instructions">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li>Make sure you're logged in to the dashboard</li>
            <li>Open the create post page</li>
            <li>Follow the test cases below</li>
            <li>Verify the expected behavior</li>
        </ol>
    </div>

    <a href="http://localhost:3001/admin-access" class="link" target="_blank">🔐 Login Page</a>
    <a href="http://localhost:3001/dashboard/posts/new" class="link" target="_blank">📝 Create Post Page</a>

    <div class="test-container">
        <h2>🔍 Test Cases</h2>

        <div class="test-case">
            <h3>Test Case 1: Empty Title → Empty Slug</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Start with empty title field</li>
                <li>Observe slug field</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug field should be empty, no validation errors shown
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 2: Type Title → Auto-generate Slug</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Type "My Awesome Post" in title field</li>
                <li>Observe slug field</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug should auto-generate to "my-awesome-post"
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 3: Clear Title → Clear Slug</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Type title "Test Post"</li>
                <li>Wait for slug to generate ("test-post")</li>
                <li>Clear the title field completely</li>
                <li>Observe slug field</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug should automatically clear to empty, no validation errors
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 4: Manual Slug + Clear Title</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Type title "Original Title"</li>
                <li>Manually edit slug to "custom-slug"</li>
                <li>Clear the title field</li>
                <li>Observe slug field and sync button</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug should remain "custom-slug" (manual change preserved), sync button should appear
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 5: Sync Button with Empty Title</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Continue from Test Case 4</li>
                <li>Click the sync button (↻)</li>
                <li>Observe slug field</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug should clear to empty, sync button should disappear
            </div>
        </div>

        <div class="test-case">
            <h3>Test Case 6: Re-type Title After Clear</h3>
            <p><strong>Steps:</strong></p>
            <ol>
                <li>Continue from Test Case 5 (empty title and slug)</li>
                <li>Type new title "New Post Title"</li>
                <li>Observe slug field</li>
            </ol>
            <div class="expected">
                <strong>Expected:</strong> Slug should auto-generate to "new-post-title"
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>✅ Expected Behaviors Summary</h2>
        <ul>
            <li><strong>Empty title</strong> → Empty slug (no errors)</li>
            <li><strong>Type title</strong> → Auto-generate slug</li>
            <li><strong>Clear title</strong> → Auto-clear slug (if not manually changed)</li>
            <li><strong>Manual slug + clear title</strong> → Keep manual slug, show sync button</li>
            <li><strong>Sync with empty title</strong> → Clear slug</li>
            <li><strong>No validation errors</strong> when both title and slug are empty</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>🐛 Common Issues to Check</h2>
        <ul>
            <li>Slug validation errors when title is empty</li>
            <li>Sync button not appearing/disappearing correctly</li>
            <li>Manual changes not being preserved</li>
            <li>Auto-generation not working after clearing</li>
        </ul>
    </div>
</body>
</html>
