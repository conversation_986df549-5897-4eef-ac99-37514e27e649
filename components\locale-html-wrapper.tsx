"use client"

import { useEffect } from 'react'
import { useLocale } from '@/contexts/locale-context'

interface LocaleHtmlWrapperProps {
  children: React.ReactNode
}

export function LocaleHtmlWrapper({ children }: LocaleHtmlWrapperProps) {
  const { locale } = useLocale()

  useEffect(() => {
    // Update the html lang attribute when locale changes
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale
    }
  }, [locale])

  return <>{children}</>
}
