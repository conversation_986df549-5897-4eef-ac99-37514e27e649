"use client"

import { Sparkles } from 'lucide-react'
import { useTranslations } from '@/contexts/locale-context'

export function HeroSection() {
  const t = useTranslations()

  return (
    <section className="relative overflow-hidden py-8">
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-emerald-500/5 dark:from-green-400/5 dark:to-emerald-400/5" />

      {/* Floating Code Elements */}
      <div className="absolute top-4 left-4 opacity-20 dark:opacity-30">
        <div className="text-xs font-mono text-green-600 dark:text-green-400 transform -rotate-12">
          &lt;/&gt;
        </div>
      </div>
      <div className="absolute top-6 right-6 opacity-20 dark:opacity-30">
        <div className="text-xs font-mono text-green-600 dark:text-green-400 transform rotate-12">
          {`{ }`}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center">

        {/* Main Heading */}
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          <span className="bg-gradient-to-r from-gray-900 via-gray-700 to-gray-600 dark:from-white dark:via-gray-200 dark:to-gray-400 bg-clip-text text-transparent theme-transition">
            {t('hero.title')}
          </span>
          <br />
          <span className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-600 bg-clip-text text-transparent relative">
            {t('hero.subtitle')}
            <div className="absolute -top-2 -right-2 opacity-60">
              <Sparkles className="w-4 h-4 text-green-500 dark:text-green-400 animate-pulse" />
            </div>
          </span>
        </h1>

        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed theme-transition">
          {t('hero.description')}
        </p>
      </div>
    </section>
  )
}
