'use client'

import { useState } from 'react'
import { PostTypeSelector } from '@/components/dashboard/post-type-selector'

export default function TestPostTypeSelectorPage() {
  const [selectedType, setSelectedType] = useState('')

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
          Test Post Type Selector
        </h1>
        
        <div className="space-y-4">
          <PostTypeSelector
            label="Post Type"
            value={selectedType}
            onChange={setSelectedType}
            required
          />
          
          <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Selected Type ID: <span className="font-mono">{selectedType || 'None'}</span>
            </p>
          </div>
          
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <p>Open browser console (F12) to see debug logs</p>
            <p>Check Network tab for API requests</p>
          </div>
        </div>
      </div>
    </div>
  )
}
