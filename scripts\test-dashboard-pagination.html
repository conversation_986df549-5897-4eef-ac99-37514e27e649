<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Dashboard Pagination</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .pagination-demo {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #007cba;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .before {
            background-color: #f8d7da;
        }
        .after {
            background-color: #d4edda;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-case {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>📄 Test: Dashboard Pagination</h1>
    
    <div class="container">
        <div class="success">
            <h2>✅ New Feature: Advanced Pagination!</h2>
            <p>Dashboard Posts sekarang dilengkapi dengan <strong>advanced pagination system</strong>:</p>
            <ul>
                <li>📄 <strong>Flexible Page Size</strong> - 10, 20, 50, 100 posts per page</li>
                <li>🔢 <strong>Smart Navigation</strong> - First, Previous, Next, Last buttons</li>
                <li>📊 <strong>Items Counter</strong> - "Showing X to Y of Z results"</li>
                <li>🎯 <strong>Page Numbers</strong> - Smart pagination with ellipsis</li>
                <li>⚡ <strong>Real-time Updates</strong> - Instant page switching</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎛️ Pagination Features</h2>
        
        <div class="feature-grid">
            <div class="feature-item">
                <strong>📄 Items Per Page</strong><br>
                Dropdown: 10, 20, 50, 100 posts
            </div>
            <div class="feature-item">
                <strong>🔢 Page Navigation</strong><br>
                First, Previous, Numbers, Next, Last
            </div>
            <div class="feature-item">
                <strong>📊 Items Counter</strong><br>
                "Showing 1 to 10 of 45 results"
            </div>
            <div class="feature-item">
                <strong>🎯 Smart Numbers</strong><br>
                1 ... 5 6 7 ... 15 (with ellipsis)
            </div>
            <div class="feature-item">
                <strong>⚡ Loading States</strong><br>
                Disabled buttons during API calls
            </div>
            <div class="feature-item">
                <strong>🎨 Visual Feedback</strong><br>
                Current page highlighted in green
            </div>
        </div>
    </div>

    <div class="pagination-demo">
        <h3>🎨 Pagination UI Preview</h3>
        <div style="background: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 14px; color: #666;">Show</span>
                    <select style="padding: 4px 8px; border: 1px solid #ccc; border-radius: 4px;">
                        <option>10 per page</option>
                        <option>20 per page</option>
                        <option>50 per page</option>
                        <option>100 per page</option>
                    </select>
                </div>
                
                <div style="font-size: 14px; color: #666;">
                    Showing <strong>1</strong> to <strong>10</strong> of <strong>45</strong> results
                </div>
                
                <div style="display: flex; gap: 4px;">
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">⏮️</button>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">◀️</button>
                    <button style="padding: 4px 8px; border: 1px solid #28a745; background: #28a745; color: white; border-radius: 4px;">1</button>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">2</button>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">3</button>
                    <span style="padding: 4px 8px; color: #999;">...</span>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">5</button>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">▶️</button>
                    <button style="padding: 4px 8px; border: 1px solid #ccc; background: white; border-radius: 4px;">⏭️</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Before vs After</h2>
        
        <table class="comparison-table">
            <tr>
                <th>Feature</th>
                <th class="before">❌ Before (No Pagination)</th>
                <th class="after">✅ After (Advanced Pagination)</th>
            </tr>
            <tr>
                <td><strong>Posts Display</strong></td>
                <td class="before">Fixed 10 posts, no navigation</td>
                <td class="after">10/20/50/100 posts with navigation</td>
            </tr>
            <tr>
                <td><strong>Large Datasets</strong></td>
                <td class="before">Slow loading, poor performance</td>
                <td class="after">Fast loading, paginated chunks</td>
            </tr>
            <tr>
                <td><strong>User Control</strong></td>
                <td class="before">No control over display</td>
                <td class="after">Full control over page size</td>
            </tr>
            <tr>
                <td><strong>Navigation</strong></td>
                <td class="before">Scroll only</td>
                <td class="after">Page numbers, first/last buttons</td>
            </tr>
            <tr>
                <td><strong>Performance</strong></td>
                <td class="before">Loads all data at once</td>
                <td class="after">Loads only current page data</td>
            </tr>
            <tr>
                <td><strong>User Experience</strong></td>
                <td class="before">Limited for large datasets</td>
                <td class="after">Smooth navigation, clear feedback</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>🧪 Test Instructions</h2>
        
        <div class="test-case">
            <h3>Test 1: Items Per Page Selection</h3>
            <ol>
                <li>Go to Dashboard → Posts</li>
                <li>Look for "Show X per page" dropdown</li>
                <li>Try changing from 10 to 20, 50, 100</li>
                <li>Verify page reloads with correct number of items</li>
                <li>Check that pagination updates accordingly</li>
            </ol>
            <a href="http://localhost:3000/dashboard/posts" class="link" target="_blank">🏠 Test Dashboard</a>
        </div>

        <div class="test-case">
            <h3>Test 2: Page Navigation</h3>
            <ol>
                <li>Set items per page to 10 (to see multiple pages)</li>
                <li>Test First Page button (⏮️)</li>
                <li>Test Previous Page button (◀️)</li>
                <li>Click on page numbers (1, 2, 3, etc.)</li>
                <li>Test Next Page button (▶️)</li>
                <li>Test Last Page button (⏭️)</li>
            </ol>
        </div>

        <div class="test-case">
            <h3>Test 3: Smart Page Numbers</h3>
            <ol>
                <li>Create enough posts to have 10+ pages</li>
                <li>Navigate to middle pages (page 5-6)</li>
                <li>Verify ellipsis (...) appears correctly</li>
                <li>Check pattern: 1 ... 5 6 7 ... 15</li>
                <li>Test edge cases (first 3 pages, last 3 pages)</li>
            </ol>
        </div>

        <div class="test-case">
            <h3>Test 4: Items Counter</h3>
            <ol>
                <li>Check "Showing X to Y of Z results" display</li>
                <li>Verify numbers update when changing pages</li>
                <li>Test with different items per page settings</li>
                <li>Check accuracy on last page (partial results)</li>
            </ol>
        </div>

        <div class="test-case">
            <h3>Test 5: Filter + Pagination Integration</h3>
            <ol>
                <li>Apply search filter (reduce total results)</li>
                <li>Verify pagination updates to match filtered results</li>
                <li>Test status filter + pagination</li>
                <li>Check that filters reset to page 1</li>
            </ol>
        </div>

        <div class="test-case">
            <h3>Test 6: Loading States</h3>
            <ol>
                <li>Click page navigation buttons</li>
                <li>Verify buttons are disabled during loading</li>
                <li>Check loading indicators work properly</li>
                <li>Test with slow network (throttle in dev tools)</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2>🔍 What to Look For</h2>
        
        <div class="success">
            <h3>✅ Expected Behavior:</h3>
            <ul>
                <li><strong>Dropdown Works:</strong> Items per page changes correctly</li>
                <li><strong>Page Navigation:</strong> All buttons work as expected</li>
                <li><strong>Smart Numbers:</strong> Ellipsis appears for large page counts</li>
                <li><strong>Current Page:</strong> Highlighted in green</li>
                <li><strong>Items Counter:</strong> Shows accurate range and total</li>
                <li><strong>Loading States:</strong> Buttons disabled during API calls</li>
                <li><strong>Filter Integration:</strong> Pagination resets with filters</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ Potential Issues to Check:</h3>
            <ul>
                <li><strong>API Errors:</strong> Pagination fails with server errors</li>
                <li><strong>State Sync:</strong> Page state not syncing with API</li>
                <li><strong>Edge Cases:</strong> Last page with partial results</li>
                <li><strong>Performance:</strong> Slow loading with large datasets</li>
                <li><strong>Mobile:</strong> Pagination UI on small screens</li>
                <li><strong>Filter Reset:</strong> Page not resetting to 1 with new filters</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Page Size Options</h2>
        
        <div class="feature-grid">
            <div class="feature-item">
                <strong>📄 10 per page</strong><br>
                Default, good for detailed review
            </div>
            <div class="feature-item">
                <strong>📋 20 per page</strong><br>
                Balanced view, moderate scrolling
            </div>
            <div class="feature-item">
                <strong>📊 50 per page</strong><br>
                Quick overview, more scrolling
            </div>
            <div class="feature-item">
                <strong>📈 100 per page</strong><br>
                Maximum view, bulk operations
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Benefits Achieved</h2>
        
        <div class="success">
            <h3>✅ User Experience:</h3>
            <ul>
                <li><strong>Flexible Control:</strong> Choose how many posts to see</li>
                <li><strong>Fast Navigation:</strong> Jump to any page quickly</li>
                <li><strong>Clear Feedback:</strong> Always know where you are</li>
                <li><strong>Efficient Browsing:</strong> No need to load all data</li>
                <li><strong>Professional Feel:</strong> Enterprise-grade pagination</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Technical Benefits:</h3>
            <ul>
                <li><strong>Performance:</strong> Only load current page data</li>
                <li><strong>Scalability:</strong> Handles thousands of posts</li>
                <li><strong>API Efficiency:</strong> Reduced server load</li>
                <li><strong>Memory Usage:</strong> Lower browser memory consumption</li>
                <li><strong>Responsive:</strong> Works on all screen sizes</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Ready to Test!</h2>
        <p>Dashboard pagination sudah siap dengan semua fitur advanced! User sekarang punya kontrol penuh atas tampilan posts.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000/dashboard/posts" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">🏠 Test Dashboard Pagination</a>
        </div>
        
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; text-align: center; margin: 20px 0;">
            <strong>🎯 Goal Achieved:</strong> Advanced pagination dengan flexible page sizes!
        </div>
    </div>
</body>
</html>
