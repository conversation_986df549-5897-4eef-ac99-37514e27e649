import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { AlertCircle, Type, Bold, Italic, List, Link } from 'lucide-react'
import { useState } from 'react'

interface TextEditorProps {
  label: string
  name: string
  placeholder?: string
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  minRows?: number
  maxLength?: number
  className?: string
  description?: string
}

export function TextEditor({
  label,
  name,
  placeholder = "Start writing your post content...",
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  minRows = 16,
  maxLength = 50000,
  className,
  description
}: TextEditorProps) {
  const [isFocused, setIsFocused] = useState(false)
  const hasError = !!error

  const textareaClasses = cn(
    "min-h-[400px] resize-y font-mono text-sm leading-relaxed",
    "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600",
    "text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400",
    "focus:outline-none focus:border-green-500 dark:focus:border-green-400 focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
    hasError && "border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500 dark:focus:ring-red-400 focus-visible:ring-red-500 dark:focus-visible:ring-red-400",
    disabled && "opacity-50 cursor-not-allowed",
    className
  )

  const insertText = (before: string, after: string = '') => {
    const textarea = document.getElementById(name) as HTMLTextAreaElement
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = value.substring(start, end)

    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end)
    onChange(newText)

    // Restore cursor position
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + before.length, end + before.length)
    }, 0)
  }

  const formatButtons = [
    {
      icon: Bold,
      label: 'Bold',
      action: () => insertText('**', '**'),
      shortcut: 'Ctrl+B'
    },
    {
      icon: Italic,
      label: 'Italic',
      action: () => insertText('*', '*'),
      shortcut: 'Ctrl+I'
    },
    {
      icon: List,
      label: 'List',
      action: () => insertText('\n- '),
      shortcut: 'Ctrl+L'
    },
    {
      icon: Link,
      label: 'Link',
      action: () => insertText('[', '](url)'),
      shortcut: 'Ctrl+K'
    }
  ]

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label
          htmlFor={name}
          className="text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>

        <div className="flex items-center space-x-1">
          {formatButtons.map((button) => {
            const Icon = button.icon
            return (
              <Button
                key={button.label}
                type="button"
                variant="ghost"
                size="sm"
                onClick={button.action}
                disabled={disabled}
                title={`${button.label} (${button.shortcut})`}
                className="h-8 w-8 p-0 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"
              >
                <Icon className="h-4 w-4" />
              </Button>
            )
          })}
        </div>
      </div>

      {description && (
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}

      <div className={cn(
        "relative border rounded-lg transition-colors duration-200",
        isFocused ? "border-green-500 dark:border-green-400" : "border-gray-300 dark:border-gray-600",
        hasError && "border-red-500 dark:border-red-400"
      )}>
        <Textarea
          id={name}
          name={name}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          rows={minRows}
          maxLength={maxLength}
          className={cn(textareaClasses, "border-0 focus:ring-0")}
        />

        <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded">
          {value.length}/{maxLength}
        </div>
      </div>

      <div className="text-xs text-gray-600 dark:text-gray-400">
        <p>Supports Markdown formatting. Use the toolbar buttons or keyboard shortcuts.</p>
      </div>

      {hasError && (
        <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}
