import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { MessageSquare, Reply } from "lucide-react"
import { AuthorInfo } from "@/components/ui/author-info"
import { ReactionButton } from "@/components/ui/reaction-button"
import { textStyles, cardStyles, inputStyles } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"
import { Comment } from "@/hooks/use-comments"

interface CommentsSectionProps {
  comments: Comment[]
  newComment: string
  onNewCommentChange: (value: string) => void
  onSubmitComment: () => void
  onCommentReaction: (commentId: string, type: 'thumbsUp' | 'heart') => void
  isSubmitting?: boolean
}

/**
 * Comments section component with add comment form and comments list
 */
export function CommentsSection({
  comments,
  newComment,
  onNewCommentChange,
  onSubmitComment,
  onCommentReaction,
  isSubmitting = false
}: CommentsSectionProps) {
  return (
    <div className="space-y-6">
      {/* Comments Header */}
      <div className="flex items-center gap-3">
        <MessageSquare className="w-6 h-6 text-green-500 dark:text-green-400" />
        <h2 className={cn(textStyles.heading.h2)}>
          Comments ({comments.length})
        </h2>
      </div>

      {/* Add Comment */}
      <Card className={cn(cardStyles.base)}>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Textarea
              placeholder="Share your thoughts..."
              value={newComment}
              onChange={(e) => onNewCommentChange(e.target.value)}
              className={cn(inputStyles.textarea, "min-h-[100px]")}
            />
            <div className="flex justify-end">
              <Button
                onClick={onSubmitComment}
                disabled={!newComment.trim() || isSubmitting}
                className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white transition-all duration-300"
              >
                <Reply className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Posting...' : 'Comment'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      {comments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          onReaction={onCommentReaction}
        />
      ))}
    </div>
  )
}

interface CommentItemProps {
  comment: Comment
  onReaction: (commentId: string, type: 'thumbsUp' | 'heart') => void
}

/**
 * Individual comment item component
 */
function CommentItem({ comment, onReaction }: CommentItemProps) {
  return (
    <Card className={cn(cardStyles.base)}>
      <CardContent className="p-6">
        <div className="flex gap-4">
          <AuthorInfo
            author={comment.author}
            date={comment.createdAt}
            dateLabel=""
            size="md"
            showAvatar={true}
          />
          
          <div className="flex-1">
            <p className={cn(textStyles.body.primary, "mb-3")}>
              {comment.content}
            </p>
            
            <div className="flex items-center gap-2">
              <ReactionButton
                type="thumbsUp"
                count={comment.reactions.thumbsUp}
                onClick={() => onReaction(comment.id, 'thumbsUp')}
                variant="ghost"
                size="sm"
              />
              <ReactionButton
                type="heart"
                count={comment.reactions.heart}
                onClick={() => onReaction(comment.id, 'heart')}
                variant="ghost"
                size="sm"
              />
              <Button
                variant="ghost"
                size="sm"
                className={cn(textStyles.body.secondary, "hover:text-green-500 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300 theme-transition")}
              >
                <Reply className="w-3 h-3 mr-1" />
                Reply
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
