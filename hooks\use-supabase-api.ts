import { useAuth } from '@/contexts/auth-context'
import { useCallback } from 'react'

export interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  headers?: Record<string, string>
}

export function useSupabaseApi() {
  const { token } = useAuth()

  const apiCall = useCallback(async (
    endpoint: string, 
    options: ApiOptions = {}
  ) => {
    const { method = 'GET', body, headers = {} } = options

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers,
    }

    // Add Bearer token if available
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers: requestHeaders,
      credentials: 'include',
    }

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify(body)
    }

    try {
      const response = await fetch(endpoint, requestOptions)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error?.message || `HTTP ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`API call failed for ${endpoint}:`, error)
      throw error
    }
  }, [token])

  // Convenience methods
  const get = useCallback((endpoint: string, headers?: Record<string, string>) => 
    apiCall(endpoint, { method: 'GET', headers }), [apiCall])

  const post = useCallback((endpoint: string, body?: any, headers?: Record<string, string>) => 
    apiCall(endpoint, { method: 'POST', body, headers }), [apiCall])

  const put = useCallback((endpoint: string, body?: any, headers?: Record<string, string>) => 
    apiCall(endpoint, { method: 'PUT', body, headers }), [apiCall])

  const del = useCallback((endpoint: string, headers?: Record<string, string>) => 
    apiCall(endpoint, { method: 'DELETE', headers }), [apiCall])

  const patch = useCallback((endpoint: string, body?: any, headers?: Record<string, string>) => 
    apiCall(endpoint, { method: 'PATCH', body, headers }), [apiCall])

  return {
    apiCall,
    get,
    post,
    put,
    delete: del,
    patch,
    hasToken: !!token,
  }
}

// Specific hooks for common API calls
export function useDashboardApi() {
  const api = useSupabaseApi()

  const getPostTypes = useCallback(() => 
    api.get('/api/dashboard/post-types'), [api])

  const getPosts = useCallback((params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    return api.get(`/api/dashboard/posts${queryString}`)
  }, [api])

  const createPost = useCallback((postData: any) => 
    api.post('/api/dashboard/posts', postData), [api])

  const updatePost = useCallback((id: string, postData: any) => 
    api.put(`/api/dashboard/posts/${id}`, postData), [api])

  const deletePost = useCallback((id: string) => 
    api.delete(`/api/dashboard/posts/${id}`), [api])

  return {
    getPostTypes,
    getPosts,
    createPost,
    updatePost,
    deletePost,
    hasToken: api.hasToken,
  }
}
