const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase connection...');
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing Supabase environment variables');
      return;
    }
    
    console.log('✅ Environment variables loaded');
    console.log(`   - URL: ${supabaseUrl}`);
    console.log(`   - Key: ${supabaseKey.substring(0, 20)}...`);
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Get post types
    console.log('\n1️⃣ Testing post_types table...');
    const { data: postTypes, error: postTypesError } = await supabase
      .from('post_types')
      .select('*')
      .limit(5);
    
    if (postTypesError) {
      console.error('❌ Post types error:', postTypesError);
    } else {
      console.log(`✅ Post types: Found ${postTypes.length} records`);
      postTypes.forEach(type => {
        console.log(`   - ${type.name} (${type.color})`);
      });
    }
    
    // Test 2: Get posts count
    console.log('\n2️⃣ Testing posts table...');
    const { count: postsCount, error: postsCountError } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true });
    
    if (postsCountError) {
      console.error('❌ Posts count error:', postsCountError);
    } else {
      console.log(`✅ Posts: Found ${postsCount || 0} records`);
    }
    
    // Test 3: Get user profiles count
    console.log('\n3️⃣ Testing user_profiles table...');
    const { count: profilesCount, error: profilesCountError } = await supabase
      .from('user_profiles')
      .select('*', { count: 'exact', head: true });
    
    if (profilesCountError) {
      console.error('❌ User profiles count error:', profilesCountError);
    } else {
      console.log(`✅ User profiles: Found ${profilesCount || 0} records`);
    }
    
    // Test 4: Try to get posts with basic info
    if (postsCount > 0) {
      console.log('\n4️⃣ Testing posts data...');
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('id, title, status, created_at')
        .limit(3);
      
      if (postsError) {
        console.error('❌ Posts data error:', postsError);
      } else {
        console.log(`✅ Posts data: Retrieved ${posts.length} posts`);
        posts.forEach(post => {
          console.log(`   - ${post.title} (${post.status})`);
        });
      }
    }
    
    console.log('\n🎉 Supabase connection test completed!');
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

// Run the test
testSupabaseConnection();
