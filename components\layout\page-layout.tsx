import { ReactNode } from "react"

interface PageLayoutProps {
  children: ReactNode
  className?: string
  showBackground?: boolean
}

export function PageLayout({
  children,
  className = "",
  showBackground = true
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen theme-transition ${showBackground ? 'bg-gradient-to-br from-gray-50 via-gray-100 to-gray-50 dark:from-gray-950 dark:via-gray-900 dark:to-gray-950' : 'bg-background'}`}>
      {/* Animated Background */}
      {showBackground && (
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>
      )}

      <div className={`relative ${className}`}>
        {children}
      </div>
    </div>
  )
}
