import { Profile } from '@/types'

// English Profile Data
export const profileDataEn: Profile = {
  name: "<PERSON><PERSON><PERSON>",
  title: "Software Engineer",
  bio: "Passionate about building scalable web applications and sharing knowledge through code. Currently working with <PERSON><PERSON>, React, Next.js, and Node.js to create innovative solutions.",
  location: "Purbalingga, Indonesia",
  joinDate: "August 2018",
  avatar: "/profile.png",
  social: {
    github: "rijaldev",
    instagram: "rijaldev",
    facebook: "rijaldev",
    linkedin: "rijal-solahudin",
    email: "<EMAIL>",
  },
  experience: [
    {
      id: "1",
      company: "TechCorp Inc.",
      position: "Senior Full Stack Developer",
      duration: "2022 - Present",
      location: "San Francisco, CA",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Leading development of scalable web applications using React, Next.js, and Node.js. Architecting microservices and implementing CI/CD pipelines.",
      achievements: [
        "Led a team of 5 developers in building a customer portal serving 100K+ users",
        "Reduced application load times by 40% through performance optimization",
        "Implemented automated testing reducing bugs by 60%",
        "Mentored 3 junior developers and conducted technical interviews",
      ],
      technologies: ["React", "Next.js", "Node.js", "TypeScript", "AWS", "Docker", "PostgreSQL", "Redis"],
      projects: [
        {
          name: "Customer Portal Redesign",
          description: "Complete overhaul of customer-facing portal with modern UI/UX",
          impact: "Increased user engagement by 35%",
        },
        {
          name: "Microservices Architecture",
          description: "Migrated monolithic application to microservices",
          impact: "Improved system scalability and reduced deployment time by 50%",
        },
      ],
    },
    {
      id: "2",
      company: "StartupXYZ",
      position: "Full Stack Developer",
      duration: "2020 - 2022",
      location: "Remote",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Built and maintained multiple web applications for various clients. Worked with modern JavaScript frameworks and cloud technologies.",
      achievements: [
        "Developed 8+ production applications serving 50K+ users",
        "Improved code quality by implementing ESLint and Prettier standards",
        "Reduced server costs by 30% through optimization",
        "Collaborated with design team to implement pixel-perfect UIs",
      ],
      technologies: ["Vue.js", "Laravel", "MySQL", "AWS", "Docker", "Git"],
      projects: [
        {
          name: "E-commerce Platform",
          description: "Full-featured online store with payment integration",
          impact: "Generated $500K+ in revenue for client",
        },
        {
          name: "Real-time Chat Application",
          description: "WebSocket-based messaging system",
          impact: "Supported 10K+ concurrent users",
        },
      ],
    },
    {
      id: "3",
      company: "WebStudio Pro",
      position: "Frontend Developer",
      duration: "2018 - 2020",
      location: "Jakarta, Indonesia",
      type: "Full-time",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Specialized in creating responsive and interactive user interfaces. Worked closely with designers to bring creative visions to life.",
      achievements: [
        "Created 20+ responsive websites with 99% client satisfaction",
        "Reduced page load times by 50% through optimization techniques",
        "Implemented accessibility standards improving WCAG compliance",
        "Trained 2 junior developers in modern frontend practices",
      ],
      technologies: ["HTML5", "CSS3", "JavaScript", "React", "Sass", "Webpack", "Figma"],
      projects: [
        {
          name: "Corporate Website Redesign",
          description: "Modern, responsive website for Fortune 500 company",
          impact: "Increased conversion rate by 25%",
        },
        {
          name: "Design System Implementation",
          description: "Comprehensive design system for consistent UI components",
          impact: "Reduced development time by 30%",
        },
      ],
    },
  ],
  portfolio: [], // Will be populated from portfolio data
}

// Indonesian Profile Data
export const profileDataId: Profile = {
  name: "Rijal Solahudin",
  title: "Software Engineer",
  bio: "Bersemangat dalam membangun aplikasi web yang dapat diskalakan dan berbagi pengetahuan melalui kode. Saat ini bekerja dengan Laravel, React, Next.js, dan Node.js untuk menciptakan solusi inovatif.",
  location: "Purbalingga, Indonesia",
  joinDate: "Agustus 2018",
  avatar: "/profile.png",
  social: {
    github: "rijaldev",
    instagram: "rijaldev",
    facebook: "rijaldev",
    linkedin: "rijal-solahudin",
    email: "<EMAIL>",
  },
  experience: [
    {
      id: "1",
      company: "TechCorp Inc.",
      position: "Senior Full Stack Developer",
      duration: "2022 - Sekarang",
      location: "San Francisco, CA",
      type: "Penuh waktu",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Memimpin pengembangan aplikasi web yang dapat diskalakan menggunakan React, Next.js, dan Node.js. Merancang arsitektur microservices dan mengimplementasikan CI/CD pipelines.",
      achievements: [
        "Memimpin tim 5 developer dalam membangun portal pelanggan yang melayani 100K+ pengguna",
        "Mengurangi waktu loading aplikasi sebesar 40% melalui optimisasi performa",
        "Mengimplementasikan automated testing yang mengurangi bug sebesar 60%",
        "Membimbing 3 junior developer dan melakukan wawancara teknis",
      ],
      technologies: ["React", "Next.js", "Node.js", "TypeScript", "AWS", "Docker", "PostgreSQL", "Redis"],
      projects: [
        {
          name: "Redesain Portal Pelanggan",
          description: "Pembaruan menyeluruh portal pelanggan dengan UI/UX modern",
          impact: "Meningkatkan engagement pengguna sebesar 35%",
        },
        {
          name: "Arsitektur Microservices",
          description: "Migrasi aplikasi monolitik ke microservices",
          impact: "Meningkatkan skalabilitas sistem dan mengurangi waktu deployment sebesar 50%",
        },
      ],
    },
    {
      id: "2",
      company: "StartupXYZ",
      position: "Full Stack Developer",
      duration: "2020 - 2022",
      location: "Remote",
      type: "Penuh waktu",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Membangun dan memelihara berbagai aplikasi web untuk klien. Bekerja dengan framework JavaScript modern dan teknologi cloud.",
      achievements: [
        "Mengembangkan 8+ aplikasi produksi yang melayani 50K+ pengguna",
        "Meningkatkan kualitas kode dengan mengimplementasikan standar ESLint dan Prettier",
        "Mengurangi biaya server sebesar 30% melalui optimisasi",
        "Berkolaborasi dengan tim desain untuk mengimplementasikan UI yang pixel-perfect",
      ],
      technologies: ["Vue.js", "Laravel", "MySQL", "AWS", "Docker", "Git"],
      projects: [
        {
          name: "Platform E-commerce",
          description: "Toko online lengkap dengan integrasi pembayaran",
          impact: "Menghasilkan pendapatan $500K+ untuk klien",
        },
        {
          name: "Aplikasi Chat Real-time",
          description: "Sistem pesan berbasis WebSocket",
          impact: "Mendukung 10K+ pengguna bersamaan",
        },
      ],
    },
    {
      id: "3",
      company: "WebStudio Pro",
      position: "Frontend Developer",
      duration: "2018 - 2020",
      location: "Jakarta, Indonesia",
      type: "Penuh waktu",
      logo: "/placeholder.svg?height=60&width=60",
      description: "Spesialisasi dalam membuat antarmuka pengguna yang responsif dan interaktif. Bekerja sama dengan desainer untuk mewujudkan visi kreatif.",
      achievements: [
        "Membuat 20+ website responsif dengan tingkat kepuasan klien 99%",
        "Mengurangi waktu loading halaman sebesar 50% melalui teknik optimisasi",
        "Mengimplementasikan standar aksesibilitas untuk meningkatkan kepatuhan WCAG",
        "Melatih 2 junior developer dalam praktik frontend modern",
      ],
      technologies: ["HTML5", "CSS3", "JavaScript", "React", "Sass", "Webpack", "Figma"],
      projects: [
        {
          name: "Redesain Website Korporat",
          description: "Website modern dan responsif untuk perusahaan Fortune 500",
          impact: "Meningkatkan conversion rate sebesar 25%",
        },
        {
          name: "Implementasi Design System",
          description: "Sistem desain komprehensif untuk komponen UI yang konsisten",
          impact: "Mengurangi waktu pengembangan sebesar 30%",
        },
      ],
    },
  ],
  portfolio: [], // Will be populated from portfolio data
}

// Function to get profile data based on locale
export function getProfileData(locale: 'en' | 'id'): Profile {
  return locale === 'id' ? profileDataId : profileDataEn
}
