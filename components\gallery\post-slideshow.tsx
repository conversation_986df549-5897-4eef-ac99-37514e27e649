"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, Play, Pause, Maximize2, Download, MoreHorizontal, Plus, Minus, RefreshCw } from "lucide-react"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import NextImage from "next/image"
import React from "react"
import { motion, AnimatePresence } from "framer-motion"

interface PostImage {
  id: string
  url: string
  alt?: string
  caption?: string
  width?: number
  height?: number
}

interface PostSlideshowProps {
  images: PostImage[]
  autoPlay?: boolean
  autoPlayInterval?: number
  showThumbnails?: boolean
  className?: string
}

export function PostSlideshow({
  images,
  autoPlay = false,
  autoPlayInterval = 5000,
  showThumbnails = true,
  className = "",
}: PostSlideshowProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [direction, setDirection] = useState<'left' | 'right'>('right')
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const [showLightbox, setShowLightbox] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const dragStart = useRef<{ x: number; y: number } | null>(null)
  const panStart = useRef({ x: 0, y: 0 })
  const autoPlayTimer = useRef<NodeJS.Timeout | undefined>(undefined)
  const containerRef = useRef<HTMLDivElement>(null)
  const slideRef = useRef<HTMLDivElement>(null)
  const lightboxSlideRef = useRef<HTMLDivElement>(null)

  // Preload next and previous images for faster navigation
  useEffect(() => {
    if (images.length <= 1) return

    const preloadImages: HTMLImageElement[] = []
    const nextIndex = (currentIndex + 1) % images.length
    const prevIndex = (currentIndex - 1 + images.length) % images.length

    // Preload next image
    if (images[nextIndex]?.url) {
      const nextImg = new Image()
      nextImg.src = images[nextIndex].url
      preloadImages.push(nextImg)
    }

    // Preload previous image
    if (images[prevIndex]?.url && prevIndex !== nextIndex) {
      const prevImg = new Image()
      prevImg.src = images[prevIndex].url
      preloadImages.push(prevImg)
    }

    return () => {
      preloadImages.forEach(img => {
        img.src = ''
      })
    }
  }, [currentIndex, images])

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || images.length <= 1) return

    autoPlayTimer.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % images.length)
    }, autoPlayInterval)

    return () => {
      if (autoPlayTimer.current) {
        clearInterval(autoPlayTimer.current)
      }
    }
  }, [isAutoPlaying, images.length, autoPlayInterval])

  // Reset pan when zoom is 1 or lightbox closed
  React.useEffect(() => {
    if (zoom === 1) setPan({ x: 0, y: 0 })
  }, [zoom])
  React.useEffect(() => {
    if (!showLightbox) setPan({ x: 0, y: 0 })
  }, [showLightbox])

  if (!images || images.length === 0) return null

  // Function to handle slide transition
  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return
    
    setIsTransitioning(true)
    setDirection(index > currentIndex ? 'right' : 'left')
    setCurrentIndex(index)
    
    // Reset transition state after animation
    setTimeout(() => {
      setIsTransitioning(false)
    }, 1500) // Slightly longer than transition duration
  }

  const downloadImage = async (url: string, filename: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = downloadUrl
      link.download = filename || `image-${currentIndex + 1}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    } catch (error) {
      console.error("Failed to download image:", error)
    }
  }

  const currentImage = images[currentIndex]

  // Mouse event handlers for drag-to-pan
  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom === 1) return
    setIsDragging(true)
    dragStart.current = { x: e.clientX, y: e.clientY }
    panStart.current = { ...pan }
    e.preventDefault()
  }
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || zoom === 1) return
    const dx = e.clientX - (dragStart.current?.x || 0)
    const dy = e.clientY - (dragStart.current?.y || 0)
    setPan({ x: panStart.current.x + dx, y: panStart.current.y + dy })
  }
  const handleMouseUp = () => {
    setIsDragging(false)
  }
  // Touch event handlers for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    if (zoom === 1) return
    setIsDragging(true)
    const touch = e.touches[0]
    dragStart.current = { x: touch.clientX, y: touch.clientY }
    panStart.current = { ...pan }
  }
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || zoom === 1) return
    const touch = e.touches[0]
    const dx = touch.clientX - (dragStart.current?.x || 0)
    const dy = touch.clientY - (dragStart.current?.y || 0)
    setPan({ x: panStart.current.x + dx, y: panStart.current.y + dy })
  }
  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  return (
    <div className={className} ref={containerRef}>
      <Card className="bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm overflow-hidden theme-transition">
        <CardContent className="p-0">
          {/* Main Slideshow Area */}
          <div className="relative group">
            {/* Main Image Container */}
            <div className="relative aspect-video bg-gray-200 dark:bg-gray-800 overflow-hidden theme-transition">
              <div className="relative w-full h-full">
                {/* Slides Container */}
                <div 
                  ref={slideRef}
                  className="absolute inset-0 flex"
                  style={{
                    willChange: 'transform',
                    transform: `translateX(${-currentIndex * 100}%)`,
                    transition: isTransitioning 
                      ? 'transform 1300ms cubic-bezier(0.4, 0, 0.2, 1)' 
                      : 'none'
                  }}
                >
                  {images.map((image, index) => (
                    <div
                      key={image.id}
                      className="relative flex-shrink-0 w-full h-full"
                      style={{ 
                        width: '100%',
                        willChange: 'transform'
                      }}
                    >
                      <NextImage
                        src={image.url || "/placeholder.svg"}
                        alt={image.alt || `Slide ${index + 1}`}
                        fill
                        className="object-cover"
                        priority={index === 0}
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                        style={{
                          transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
                          transition: isDragging ? 'none' : 'transform 0.3s ease-out',
                          willChange: 'transform'
                        }}
                      />
                    </div>
                  ))}
                </div>

                {/* Overlay Controls */}
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 via-transparent to-gray-900/20 dark:from-black/50 dark:via-transparent dark:to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 theme-transition">
                  {/* Navigation Buttons */}
                  {images.length > 1 && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => goToSlide((currentIndex - 1 + images.length) % images.length)}
                        disabled={isTransitioning}
                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-gray-900/80 hover:bg-white dark:hover:bg-gray-900 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300 theme-transition"
                      >
                        <ChevronLeft className="w-5 h-5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => goToSlide((currentIndex + 1) % images.length)}
                        disabled={isTransitioning}
                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-gray-900/80 hover:bg-white dark:hover:bg-gray-900 backdrop-blur-sm border border-gray-200 dark:border-gray-700 shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300 theme-transition"
                      >
                        <ChevronRight className="w-5 h-5" />
                      </Button>
                    </>
                  )}

                  {/* Top Controls */}
                  <div className="absolute top-4 right-4 flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowLightbox(true)}
                      className="bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white backdrop-blur-sm theme-transition"
                    >
                      <Maximize2 className="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white backdrop-blur-sm theme-transition"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700 theme-transition">
                        <DropdownMenuItem
                          onClick={() => downloadImage(currentImage.url, `image-${currentIndex + 1}`)}
                          className="text-gray-700 hover:text-green-500 dark:text-gray-300 dark:hover:text-green-400 theme-transition"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download Image
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Slide Counter */}
                  {images.length > 1 && (
                    <div className="absolute top-4 left-4">
                      <Badge variant="outline" className="bg-gray-900/50 border-gray-400 text-white dark:bg-black/50 dark:border-gray-600 backdrop-blur-sm theme-transition">
                        {currentIndex + 1} / {images.length}
                      </Badge>
                    </div>
                  )}

                  {/* Progress Indicators */}
                  {images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                      {images.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => goToSlide(index)}
                          className={`w-2 h-2 rounded-full transition-all duration-200 ${
                            index === currentIndex ? "bg-white scale-125" : "bg-white/50 hover:bg-white/75"
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Image Caption */}
            {currentImage.caption && (
              <div className="p-4 bg-gray-100/50 border-t border-gray-200/50 dark:bg-gray-800/50 dark:border-gray-700/50 theme-transition">
                <p className="text-sm text-gray-700 dark:text-gray-300 italic theme-transition">{currentImage.caption}</p>
              </div>
            )}
          </div>

          {/* Thumbnails */}
          {showThumbnails && images.length > 1 && (
            <motion.div 
              className="p-4 bg-gray-100/30 border-t border-gray-200/50 dark:bg-gray-800/30 dark:border-gray-700/50 theme-transition"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                type: "tween",
                duration: 0.6,
                ease: [0.32, 0.72, 0, 1]
              }}
            >
              <div className="flex gap-2 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800">
                {images.map((image, index) => (
                  <motion.button
                    key={image.id}
                    onClick={() => goToSlide(index)}
                    whileHover={{ 
                      scale: 1.05,
                      transition: { 
                        type: "tween",
                        duration: 0.3,
                        ease: [0.32, 0.72, 0, 1]
                      }
                    }}
                    whileTap={{ 
                      scale: 0.98,
                      transition: { 
                        type: "tween",
                        duration: 0.2,
                        ease: [0.32, 0.72, 0, 1]
                      }
                    }}
                    className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 theme-transition ${
                      index === currentIndex
                        ? "border-green-500 ring-2 ring-green-500/30"
                        : "border-gray-400 hover:border-gray-500 dark:border-gray-600 dark:hover:border-gray-500"
                    }`}
                  >
                    <NextImage
                      src={image.url || "/placeholder.svg"}
                      alt={image.alt || `Thumbnail ${index + 1}`}
                      fill
                      className="object-cover will-change-transform"
                      placeholder="blur"
                      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                      sizes="64px"
                    />
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>

      {/* Lightbox Modal */}
      <Dialog open={showLightbox} onOpenChange={(open) => { setShowLightbox(open); if (!open) setZoom(1) }}>
        <DialogContent className="w-screen h-screen max-w-none max-h-none p-0 bg-black/95 border-none rounded-none flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLightbox(false)}
              className="absolute top-4 right-4 z-50 bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white theme-transition"
            >
              ✕
            </Button>

            {/* Zoom Controls */}
            <div className="absolute top-4 left-1/2 -translate-x-1/2 z-50 flex items-center gap-2 bg-black/40 rounded-full px-3 py-1">
              <Button variant="ghost" size="icon" onClick={() => setZoom(z => Math.max(0.5, z - 0.2))} className="text-white"><Minus className="w-4 h-4" /></Button>
              <span className="text-white px-2 select-none">{(zoom * 100).toFixed(0)}%</span>
              <Button variant="ghost" size="icon" onClick={() => setZoom(z => Math.min(5, z + 0.2))} className="text-white"><Plus className="w-4 h-4" /></Button>
              <Button variant="ghost" size="icon" onClick={() => setZoom(1)} className="text-white"><RefreshCw className="w-4 h-4" /></Button>
            </div>

            {/* Navigation in Lightbox */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => goToSlide((currentIndex - 1 + images.length) % images.length)}
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white theme-transition"
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => goToSlide((currentIndex + 1) % images.length)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 bg-gray-900/50 hover:bg-gray-900/70 dark:bg-black/50 dark:hover:bg-black/70 text-white theme-transition"
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </>
            )}

            {/* Lightbox Image Container */}
            <div
              className="relative w-full h-full flex items-center justify-center overflow-hidden cursor-grab"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              style={{ cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
            >
              {/* Lightbox Slides Container */}
              <div 
                ref={lightboxSlideRef}
                className="absolute inset-0 flex"
                style={{
                  willChange: 'transform',
                  transform: `translateX(${-currentIndex * 100}%)`,
                  transition: isTransitioning 
                    ? 'transform 1300ms cubic-bezier(0.4, 0, 0.2, 1)' 
                    : 'none'
                }}
              >
                {images.map((image, index) => (
                  <div
                    key={image.id}
                    className="relative flex-shrink-0 w-full h-full flex items-center justify-center"
                    style={{ 
                      width: '100%',
                      willChange: 'transform'
                    }}
                  >
                    <div
                      style={{
                        transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
                        transition: isDragging ? 'none' : 'transform 0.2s',
                        transformOrigin: 'center center',
                      }}
                    >
                      <NextImage
                        src={image.url || "/placeholder.svg"}
                        alt={image.alt || `Image ${index + 1}`}
                        width={1200}
                        height={900}
                        className="max-w-full max-h-full object-contain"
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                        priority
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Lightbox Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-gray-900/50 dark:bg-black/50 text-white px-3 py-1 rounded-full text-sm theme-transition">
                {currentIndex + 1} / {images.length}
              </div>
            )}

            {/* Lightbox Caption */}
            {currentImage.caption && (
              <div className="absolute bottom-4 left-4 right-4 bg-gray-900/50 dark:bg-black/50 text-white p-3 rounded-lg text-center max-w-2xl mx-auto theme-transition">
                <p className="text-sm">{currentImage.caption}</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
