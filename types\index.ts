// Core data types for the application

export interface User {
  id: string
  email: string
  name: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  token?: string
  message?: string
}

export interface PostImage {
  id: string
  url: string
  alt?: string
  caption?: string
  width?: number
  height?: number
}

export interface PostReactions {
  heart: number
}

export interface PostType {
  id: string
  name: string
  color: string
  icon: string
  slug: string
  createdAt: string
  updatedAt: string
}

export enum PostStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

export interface Post {
  id: string
  title: string
  slug: string
  content: string
  excerpt: boolean
  version: string
  type: PostType
  author: User | string
  status: PostStatus
  createdAt: string
  updatedAt: string
  publishedAt?: string
  reactionHart: number
  comments?: number
  viewCount: number
  tags: string[]
  featured: boolean
  readTime?: string
  images?: PostImage[]
}

export interface PostStats {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  archivedPosts: number
  totalViews: number
  totalReactions: number
  totalComments: number
  postsByType: { type: string; count: number }[]
  mostViewedPosts: Pick<Post, 'id' | 'title' | 'viewCount' | 'slug'>[]
  recentPosts: Pick<Post, 'id' | 'title' | 'status' | 'createdAt'>[]
  growthMetrics: {
    thisMonth: number
    lastMonth: number
    growth: number
  }
}

export interface CreatePostData {
  title: string
  content: string
  excerpt?: boolean
  authorId: string
  typeId: string
  status?: PostStatus
  featured?: boolean
  featuredImage?: string
  tags?: string[]
  publishedAt?: string
}

export interface PostImageData {
  url: string
  altText?: string
  caption?: string
  name: string
  size: number
}

export interface CreatePostRequest {
  title: string
  slug: string
  content: string
  typeId: string
  status: PostStatus
  featured?: boolean
  showFullContent?: boolean
  tags?: string[]
  images?: PostImageData[]
}

export interface CreatePostResponse {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: boolean
  status: PostStatus
  featured: boolean
  featuredImage?: string
  author: User
  type: {
    id: string
    name: string
    color?: string
    icon?: string
  }
  tags: string[]
  viewCount: number
  reactionHart: number
  comments: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

export interface UpdatePostData extends Partial<CreatePostData> {
  slug?: string
}

export interface PostFilters {
  status?: PostStatus
  type?: string
  featured?: boolean
  authorId?: string
  search?: string
}

export interface Experience {
  id: string
  company: string
  position: string
  duration: string
  location: string
  type: string
  logo: string
  description: string
  achievements: string[]
  technologies: string[]
  projects: ExperienceProject[]
}

export interface ExperienceProject {
  name: string
  description: string
  impact: string
}

export interface PortfolioProject {
  id: string
  title: string
  description: string
  longDescription: string
  image: string
  technologies: string[]
  category: string
  status: 'Live' | 'In Development' | 'Completed'
  year: string
  duration: string
  team: string
  role: string
  links: {
    live?: string
    github?: string
    case_study?: string
  }
  metrics: {
    users?: string
    transactions?: string
    uptime?: string
    performance?: string
    api_calls?: string
    accuracy?: string
    accounts?: string
    data_points?: string
    reports?: string
    projects?: string
  }
  features: string[]
  challenges: string[]
  solutions: string[]
}

export interface Profile {
  name: string
  title: string
  bio: string
  location: string
  joinDate: string
  avatar: string
  social: {
    github: string
    instagram: string
    facebook: string
    linkedin: string
    email: string
  }
  experience: Experience[]
  portfolio: PortfolioProject[]
}

export interface SkillGroup {
  category: string
  technologies: string[]
}

export interface SocialLink {
  icon: any // Lucide icon component
  href: string
  label: string
}

export interface NavigationItem {
  label: string
  href: string
  icon?: any // Lucide icon component
}
