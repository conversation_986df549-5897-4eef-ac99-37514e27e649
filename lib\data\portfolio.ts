import { PortfolioProject } from '../types'

export const portfolioData: PortfolioProject[] = [
  {
    id: "1",
    title: "E-commerce Platform",
    description:
      "A comprehensive e-commerce solution built with React and Node.js, featuring real-time inventory management, payment processing, and advanced analytics.",
    longDescription:
      "This full-stack e-commerce platform was designed to handle high-traffic scenarios with a focus on performance and user experience. The platform includes features like real-time inventory tracking, multiple payment gateways, order management, customer analytics, and a comprehensive admin dashboard.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "Node.js", "MongoDB", "Stripe", "Redis", "AWS"],
    category: "Full Stack",
    status: "Live",
    year: "2023",
    duration: "6 months",
    team: "4 developers",
    role: "Lead Developer",
    links: {
      live: "https://ecommerce-demo.com",
      github: "https://github.com/alexthompson/ecommerce",
      case_study: "/case-studies/ecommerce",
    },
    metrics: {
      users: "50K+",
      transactions: "$2M+",
      uptime: "99.9%",
      performance: "95/100",
    },
    features: [
      "Real-time inventory management",
      "Multi-vendor marketplace",
      "Advanced search and filtering",
      "Payment gateway integration",
      "Order tracking system",
      "Analytics dashboard",
    ],
    challenges: [
      "Handling high concurrent users during flash sales",
      "Implementing real-time inventory updates",
      "Optimizing database queries for complex searches",
    ],
    solutions: [
      "Implemented Redis caching for session management",
      "Used WebSocket connections for real-time updates",
      "Optimized database indexes and query patterns",
    ],
  },
  {
    id: "2",
    title: "Task Management App",
    description:
      "A collaborative task management application with real-time updates, team collaboration features, and advanced project tracking capabilities.",
    longDescription:
      "Built for modern teams, this task management application provides comprehensive project tracking, team collaboration tools, and real-time synchronization across all devices. The app features drag-and-drop interfaces, time tracking, reporting, and integration with popular productivity tools.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Next.js", "Socket.io", "PostgreSQL", "Prisma", "Tailwind CSS"],
    category: "Web App",
    status: "Live",
    year: "2023",
    duration: "4 months",
    team: "3 developers",
    role: "Full Stack Developer",
    links: {
      live: "https://taskapp-demo.com",
      github: "https://github.com/alexthompson/taskapp",
      case_study: "/case-studies/taskapp",
    },
    metrics: {
      users: "10K+",
      projects: "25K+",
      uptime: "99.8%",
      performance: "92/100",
    },
    features: [
      "Real-time collaboration",
      "Drag-and-drop task management",
      "Time tracking and reporting",
      "Team chat integration",
      "File sharing and comments",
      "Mobile responsive design",
    ],
    challenges: [
      "Implementing real-time collaboration without conflicts",
      "Optimizing performance for large project datasets",
      "Creating intuitive drag-and-drop interfaces",
    ],
    solutions: [
      "Implemented operational transformation for conflict resolution",
      "Used virtual scrolling for large lists",
      "Built custom drag-and-drop components with accessibility",
    ],
  },
  {
    id: "3",
    title: "Weather Dashboard",
    description:
      "An interactive weather dashboard with beautiful data visualizations, location-based forecasts, and historical weather data analysis.",
    longDescription:
      "This weather dashboard combines real-time weather data with historical analysis to provide comprehensive weather insights. Features include interactive maps, detailed forecasts, weather alerts, and beautiful data visualizations using D3.js.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["Vue.js", "D3.js", "Weather API", "Chart.js", "Mapbox"],
    category: "Data Visualization",
    status: "Live",
    year: "2022",
    duration: "3 months",
    team: "2 developers",
    role: "Frontend Developer",
    links: {
      live: "https://weather-dashboard-demo.com",
      github: "https://github.com/alexthompson/weather",
      case_study: "/case-studies/weather",
    },
    metrics: {
      users: "15K+",
      api_calls: "1M+/month",
      accuracy: "95%",
      performance: "98/100",
    },
    features: [
      "Interactive weather maps",
      "7-day detailed forecasts",
      "Historical data analysis",
      "Weather alerts and notifications",
      "Location-based recommendations",
      "Data export capabilities",
    ],
    challenges: [
      "Processing large amounts of weather data efficiently",
      "Creating smooth animations for data transitions",
      "Handling multiple API rate limits",
    ],
    solutions: [
      "Implemented data caching and pagination strategies",
      "Used requestAnimationFrame for smooth animations",
      "Built API request queue with intelligent rate limiting",
    ],
  },
  {
    id: "4",
    title: "Social Media Analytics",
    description:
      "A comprehensive social media analytics platform that tracks engagement, analyzes trends, and provides actionable insights for content creators.",
    longDescription:
      "This analytics platform helps content creators and businesses understand their social media performance across multiple platforms. It provides detailed insights, trend analysis, competitor tracking, and automated reporting features.",
    image: "/placeholder.svg?height=400&width=600",
    technologies: ["React", "Python", "FastAPI", "PostgreSQL", "Celery", "Docker"],
    category: "Analytics",
    status: "In Development",
    year: "2024",
    duration: "8 months",
    team: "5 developers",
    role: "Technical Lead",
    links: {
      github: "https://github.com/alexthompson/social-analytics",
      case_study: "/case-studies/social-analytics",
    },
    metrics: {
      accounts: "5K+",
      data_points: "10M+",
      reports: "50K+",
      accuracy: "97%",
    },
    features: [
      "Multi-platform data aggregation",
      "Real-time engagement tracking",
      "Automated report generation",
      "Competitor analysis",
      "Content performance insights",
      "Custom dashboard creation",
    ],
    challenges: [
      "Handling rate limits across multiple social media APIs",
      "Processing and analyzing large datasets efficiently",
      "Creating meaningful insights from complex data",
    ],
    solutions: [
      "Built robust API management system with retry logic",
      "Implemented distributed data processing with Celery",
      "Used machine learning for trend detection and insights",
    ],
  },
]
