import { Card, CardContent } from "@/components/ui/card"
import { Heart, MessageSquare } from "lucide-react"
import { Post } from "@/types"

interface StatsCardProps {
  posts: Post[]
  locale: string
  translations: {
    title: string
    posts: string
    views: string
    reactions: string
  }
}

export function StatsCard({ posts, locale, translations }: StatsCardProps) {
  const totalLikes = posts.reduce((acc, post) => acc + (post.reactionHart || 0), 0)
  const totalComments = posts.reduce((acc, post) => acc + (post.comments || 0), 0)

  return (
    <Card className="bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm theme-transition">
      <CardContent className="p-6 space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Heart className="w-4 h-4 text-red-400" />
              <span className="text-sm text-gray-700 dark:text-gray-300 theme-transition">{locale === 'id' ? 'Total Suka' : 'Total Likes'}</span>
            </div>
            <span className="text-lg font-bold text-red-400">{totalLikes}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4 text-green-500 dark:text-green-400" />
              <span className="text-sm text-gray-700 dark:text-gray-300 theme-transition">{locale === 'id' ? 'Komentar' : 'Comments'}</span>
            </div>
            <span className="text-lg font-bold text-green-500 dark:text-green-400">{totalComments}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
