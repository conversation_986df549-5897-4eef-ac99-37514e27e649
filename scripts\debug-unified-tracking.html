<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug: Unified View Tracking</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border-left: 4px solid #007cba;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            cursor: pointer;
            border: none;
        }
        .button:hover {
            background-color: #005a87;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        #localStorage-data {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug: Unified View Tracking</h1>
    
    <div class="container">
        <h2>🛠️ Debug Tools</h2>
        
        <div class="debug-section">
            <h3>localStorage Inspector</h3>
            <button class="button" onclick="refreshLocalStorage()">🔄 Refresh Data</button>
            <button class="button" onclick="clearViewedPosts()">🗑️ Clear Viewed Posts</button>
            <button class="button" onclick="addTestData()">➕ Add Test Data</button>
            
            <div id="localStorage-data">
                Loading localStorage data...
            </div>
        </div>

        <div class="debug-section">
            <h3>Console Commands</h3>
            <p>Copy-paste these commands in browser console (F12):</p>
            
            <div class="code">// Check viewed posts
console.log('Viewed Posts:', JSON.parse(localStorage.getItem('viewed_posts_24h') || '{}'));

// Clear all viewed posts
localStorage.removeItem('viewed_posts_24h');
console.log('Cleared viewed posts');

// Add test post as viewed
const viewed = JSON.parse(localStorage.getItem('viewed_posts_24h') || '{}');
viewed['test-post-id'] = new Date().toISOString();
localStorage.setItem('viewed_posts_24h', JSON.stringify(viewed));
console.log('Added test post');</div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Test Scenarios</h2>
        
        <div class="debug-section">
            <h3>Scenario 1: Fresh Start</h3>
            <ol>
                <li>Clear localStorage (button above)</li>
                <li>Go to homepage</li>
                <li>Scroll slowly to see posts</li>
                <li>Watch console for "Recording unified view"</li>
                <li>Check localStorage for new entries</li>
            </ol>
            <a href="http://localhost:3000" class="button" target="_blank">🏠 Test Homepage</a>
        </div>

        <div class="debug-section">
            <h3>Scenario 2: Duplicate Prevention</h3>
            <ol>
                <li>View a post (homepage scroll)</li>
                <li>Click to detail page</li>
                <li>Watch console for "already viewed today, skipping"</li>
                <li>View count should not increase</li>
            </ol>
            <a href="http://localhost:3000/posts/getting-started-with-react-hooks" class="button" target="_blank">📄 Test Detail</a>
        </div>

        <div class="debug-section">
            <h3>Scenario 3: 24h Reset</h3>
            <ol>
                <li>View a post (gets recorded)</li>
                <li>Clear localStorage</li>
                <li>View same post again</li>
                <li>Should record new view</li>
            </ol>
            <button class="button" onclick="simulateNewDay()">🌅 Simulate New Day</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Expected Console Logs</h2>
        
        <div class="success">
            <h3>✅ First View (Should See):</h3>
            <ul>
                <li>👁️ Post entered viewport (unified): [post-id]</li>
                <li>📊 Recording unified view for post: [post-id]</li>
                <li>✅ Unified view recorded successfully for post: [post-id]</li>
            </ul>
        </div>

        <div class="warning">
            <h3>🚫 Duplicate View (Should See):</h3>
            <ul>
                <li>👁️ Post entered viewport (unified): [post-id]</li>
                <li>📊 Post already viewed today, skipping: [post-id]</li>
            </ul>
        </div>

        <div class="error">
            <h3>❌ Error Cases (Should NOT See):</h3>
            <ul>
                <li>Multiple "Recording unified view" for same post</li>
                <li>React errors or undefined errors</li>
                <li>API errors without fallback</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Success Criteria</h2>
        
        <div class="success">
            <h3>✅ System Working If:</h3>
            <ul>
                <li>First view from any source → Count +1</li>
                <li>Subsequent views same day → Count +0 (skipped)</li>
                <li>Console shows appropriate skip messages</li>
                <li>localStorage contains viewed posts with timestamps</li>
                <li>No React errors or crashes</li>
                <li>Fallback system works if API fails</li>
            </ul>
        </div>
    </div>

    <script>
        function refreshLocalStorage() {
            const data = localStorage.getItem('viewed_posts_24h');
            const container = document.getElementById('localStorage-data');
            
            if (data) {
                try {
                    const parsed = JSON.parse(data);
                    const formatted = JSON.stringify(parsed, null, 2);
                    container.innerHTML = `<strong>viewed_posts_24h:</strong>\n${formatted}`;
                } catch (e) {
                    container.innerHTML = `<span style="color: red;">Error parsing data: ${e.message}</span>`;
                }
            } else {
                container.innerHTML = '<em>No viewed posts data found</em>';
            }
        }

        function clearViewedPosts() {
            localStorage.removeItem('viewed_posts_24h');
            refreshLocalStorage();
            alert('✅ Cleared all viewed posts data');
        }

        function addTestData() {
            const viewed = JSON.parse(localStorage.getItem('viewed_posts_24h') || '{}');
            viewed['test-post-' + Date.now()] = new Date().toISOString();
            localStorage.setItem('viewed_posts_24h', JSON.stringify(viewed));
            refreshLocalStorage();
            alert('✅ Added test post data');
        }

        function simulateNewDay() {
            localStorage.removeItem('viewed_posts_24h');
            refreshLocalStorage();
            alert('🌅 Simulated new day - all view tracking reset');
        }

        // Auto-refresh on page load
        refreshLocalStorage();

        // Auto-refresh every 5 seconds
        setInterval(refreshLocalStorage, 5000);
    </script>
</body>
</html>
