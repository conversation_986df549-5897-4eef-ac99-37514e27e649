"use client"

import { useState, useEffect, useCallback } from 'react'
import { Post, PostStats, PostFilters } from '@/types'
import { useAuth } from '@/contexts/auth-context'

export interface UsePostsApiOptions {
  filters?: PostFilters
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  autoFetch?: boolean
}

export interface PostsApiResponse {
  posts: Post[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  filters: PostFilters
}

export interface UsePostsApiReturn {
  posts: Post[]
  pagination: PostsApiResponse['pagination'] | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  updateFilters: (newFilters: Partial<PostFilters>) => void
  updatePagination: (page: number, limit?: number) => void
}

export function usePostsApi(options: UsePostsApiOptions = {}): UsePostsApiReturn {
  const {
    filters = {},
    page = 1,
    limit = 10,
    sortBy = 'created_at',
    sortOrder = 'desc',
    autoFetch = true
  } = options

  const [currentFilters, setCurrentFilters] = useState<PostFilters>(filters)
  const [currentPage, setCurrentPage] = useState(page)
  const [currentLimit, setCurrentLimit] = useState(limit)
  const [posts, setPosts] = useState<Post[]>([])
  const [pagination, setPagination] = useState<PostsApiResponse['pagination'] | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { token } = useAuth()

  const buildQueryParams = useCallback(() => {
    const params = new URLSearchParams()

    params.set('page', currentPage.toString())
    params.set('limit', currentLimit.toString())
    params.set('sortBy', sortBy)
    params.set('sortOrder', sortOrder)

    // Only add filter parameters if they have meaningful values (not empty strings)
    if (currentFilters.status && currentFilters.status.trim() !== '') {
      params.set('status', currentFilters.status)
    }
    if (currentFilters.type && currentFilters.type.trim() !== '') {
      params.set('type', currentFilters.type)
    }
    if (currentFilters.author && currentFilters.author.trim() !== '') {
      params.set('author', currentFilters.author)
    }
    if (currentFilters.search && currentFilters.search.trim() !== '') {
      params.set('search', currentFilters.search)
    }
    if (currentFilters.featured !== undefined) {
      params.set('featured', currentFilters.featured.toString())
    }

    return params.toString()
  }, [currentFilters, currentPage, currentLimit, sortBy, sortOrder])

  const fetchPosts = useCallback(async () => {
    if (!token) return

    try {
      setLoading(true)
      setError(null)

      const queryParams = buildQueryParams()
      const response = await fetch(`/api/dashboard/posts?${queryParams}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!response.ok) {
        if (response.status === 401) {
          // Redirect to login page on authentication error
          window.location.href = '/admin-access?redirect=' + encodeURIComponent(window.location.pathname)
          return
        }
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      if (data.success && data.data) {
        setPosts(data.data.posts)
        setPagination(data.data.pagination)
      } else {
        throw new Error(data.message || 'Failed to fetch posts')
      }
    } catch (err) {
      console.error('Error fetching posts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch posts')
    } finally {
      setLoading(false)
    }
  }, [buildQueryParams, token])

  const refetch = useCallback(async () => {
    await fetchPosts()
  }, [fetchPosts])

  const updateFilters = useCallback((newFilters: Partial<PostFilters>) => {
    setCurrentFilters(prev => ({ ...prev, ...newFilters }))
    setCurrentPage(1) // Reset to first page when filters change
  }, [])

  const updatePagination = useCallback((newPage: number, newLimit?: number) => {
    setCurrentPage(newPage)
    if (newLimit) {
      setCurrentLimit(newLimit)
    }
  }, [])

  useEffect(() => {
    if (!autoFetch || !token) return

    fetchPosts()
  }, [autoFetch, token, fetchPosts])

  return {
    posts,
    pagination,
    loading,
    error,
    refetch,
    updateFilters,
    updatePagination
  }
}

export interface UsePostStatsReturn {
  stats: PostStats | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePostStats(): UsePostStatsReturn {
  const [stats, setStats] = useState<PostStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { token } = useAuth()

  const fetchStats = useCallback(async () => {
    if (!token) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/dashboard/posts/stats', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!response.ok) {
        if (response.status === 401) {
          // Redirect to login page on authentication error
          window.location.href = '/admin-access?redirect=' + encodeURIComponent(window.location.pathname)
          return
        }
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      if (data.success && data.data) {
        setStats(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch stats')
      }
    } catch (err) {
      console.error('Error fetching post stats:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch stats')
    } finally {
      setLoading(false)
    }
  }, [token])

  const refetch = useCallback(async () => {
    await fetchStats()
  }, [fetchStats])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refetch
  }
}

export interface UsePostApiOptions {
  id?: string
  slug?: string
  autoFetch?: boolean
}

export interface UsePostApiReturn {
  post: Post | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePostApi(options: UsePostApiOptions): UsePostApiReturn {
  const { id, slug, autoFetch = true } = options
  const [post, setPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { token } = useAuth()

  const fetchPost = useCallback(async () => {
    if (!id && !slug) return

    try {
      setLoading(true)
      setError(null)

      // For dashboard, use authenticated endpoint for ID, but public endpoint for slug
      const endpoint = id ? `/api/dashboard/posts/${id}` : `/api/posts/slug/${slug}`

      let response: Response
      if (id) {
        // Use authenticated request for dashboard posts by ID
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        }

        // Add Authorization header if token is available
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }

        response = await fetch(endpoint, {
          method: 'GET',
          headers,
          credentials: 'include',
        })
      } else {
        // Use public request for posts by slug (for public pages)
        response = await fetch(endpoint)
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      if (data.success && data.data) {
        setPost(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch post')
      }
    } catch (err) {
      console.error('Error fetching post:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch post')
    } finally {
      setLoading(false)
    }
  }, [id, slug, token])

  const refetch = useCallback(async () => {
    await fetchPost()
  }, [fetchPost])

  useEffect(() => {
    if (autoFetch && (id || slug)) {
      fetchPost()
    }
  }, [autoFetch, id, slug, fetchPost])

  return {
    post,
    loading,
    error,
    refetch
  }
}

export interface UsePostSearchApiOptions {
  query: string
  filters?: Omit<PostFilters, 'search'>
  page?: number
  limit?: number
  debounceMs?: number
}

export interface UsePostSearchApiReturn extends UsePostsApiReturn {
  query: string
  setQuery: (query: string) => void
}

export function usePostSearchApi(options: UsePostSearchApiOptions): UsePostSearchApiReturn {
  const {
    query: initialQuery,
    filters = {},
    page = 1,
    limit = 10,
    debounceMs = 300
  } = options

  const [query, setQuery] = useState(initialQuery)
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [query, debounceMs])

  // Use the main posts API hook with search filter
  const searchFilters: PostFilters = {
    ...filters,
    search: debouncedQuery
  }

  const postsApi = usePostsApi({
    filters: searchFilters,
    page,
    limit,
    autoFetch: !!debouncedQuery.trim()
  })

  return {
    ...postsApi,
    query,
    setQuery
  }
}
