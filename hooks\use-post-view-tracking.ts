"use client"

import { useState, useCallback } from 'react'
import { useViewportTracking } from './use-viewport-tracking'

interface UsePostViewTrackingOptions {
  postId: string
  threshold?: number
  delay?: number
  enabled?: boolean
}

interface UsePostViewTrackingReturn {
  ref: React.RefObject<HTMLElement>
  isInViewport: boolean
  hasRecordedView: boolean
  isRecordingView: boolean
  error: string | null
}

/**
 * Hook for tracking post views when they enter viewport
 * Records view automatically when post is visible for specified time
 */
export function usePostViewTracking({
  postId,
  threshold = 0.5, // 50% of post must be visible
  delay = 1000, // Wait 1 second before recording view
  enabled = true
}: UsePostViewTrackingOptions): UsePostViewTrackingReturn {
  const [hasRecordedView, setHasRecordedView] = useState(false)
  const [isRecordingView, setIsRecordingView] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Record view via API
   */
  const recordView = useCallback(async () => {
    if (hasRecordedView || isRecordingView) {
      return
    }

    try {
      setIsRecordingView(true)
      setError(null)

      console.log('📊 Recording view for post:', postId)

      // Try full system first, fallback to simple system
      let response = await fetch(`/api/posts/${postId}/view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      let result = await response.json()

      // If full system fails, try fallback system
      if (!response.ok || !result.success) {
        console.log('🔄 Falling back to simple view recording...')
        
        response = await fetch(`/api/test/post-interactions-simple`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'simulate-view',
            postId
          })
        })

        result = await response.json()
      }

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to record view')
      }

      setHasRecordedView(true)
      console.log('✅ View recorded successfully for post:', postId)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to record view'
      setError(errorMessage)
      console.error('❌ Error recording view:', err)
    } finally {
      setIsRecordingView(false)
    }
  }, [postId, hasRecordedView, isRecordingView])

  /**
   * Handle viewport enter with delay
   */
  const handleViewportEnter = useCallback(() => {
    if (hasRecordedView || isRecordingView) {
      return
    }

    console.log('👁️ Post entered viewport:', postId)

    // Add delay before recording view
    setTimeout(() => {
      recordView()
    }, delay)
  }, [postId, hasRecordedView, isRecordingView, delay, recordView])

  // Use viewport tracking
  const { ref, isInViewport } = useViewportTracking({
    onEnterViewport: handleViewportEnter,
    threshold,
    triggerOnce: true, // Only trigger once per session
    enabled
  })

  return {
    ref,
    isInViewport,
    hasRecordedView,
    isRecordingView,
    error
  }
}
