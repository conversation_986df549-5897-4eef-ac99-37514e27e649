import { NextRequest } from 'next/server'
import { PostInteractionsService } from '@/services/post-interactions.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

interface RouteParams {
  params: Promise<{
    postId: string
  }>
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params
    console.log('❤️ Adding like to post:', postId)

    // Get user ID from auth (if authenticated)
    // For now, we'll handle anonymous users
    const userId = undefined // TODO: Get from auth when implemented

    const interactionsService = new PostInteractionsService()
    const result = await interactionsService.likePost(postId, userId)

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || 'Failed to like post')
    }

    console.log('✅ Like added successfully:', result.data)

    return ApiResponseBuilder.success(
      result.data,
      'Post liked successfully'
    )

  } catch (error) {
    console.error('💥 Error in like endpoint:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to like post'
    )
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params
    console.log('📊 Getting like status for post:', postId)

    // Get user ID from auth (if authenticated)
    const userId = undefined // TODO: Get from auth when implemented

    const interactionsService = new PostInteractionsService()
    const result = await interactionsService.getLikeStatus(postId, userId)

    if (!result.success) {
      return ApiResponseBuilder.badRequest(result.error || 'Failed to get like status')
    }

    return ApiResponseBuilder.success(
      result.data,
      'Like status retrieved successfully'
    )

  } catch (error) {
    console.error('💥 Error in like status endpoint:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to get like status'
    )
  }
}
