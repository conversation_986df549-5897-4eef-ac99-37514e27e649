import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { PostStatus } from '@/types'
import { FileText, Eye, Archive } from 'lucide-react'

interface StatusSelectorProps {
  label: string
  value: PostStatus
  onChange: (status: PostStatus) => void
  disabled?: boolean
  className?: string
}

const statusOptions = [
  {
    value: PostStatus.DRAFT,
    label: 'Draft',
    description: 'Save as draft to continue editing later',
    icon: FileText,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    borderColor: 'border-yellow-200 dark:border-yellow-700'
  },
  {
    value: PostStatus.PUBLISHED,
    label: 'Published',
    description: 'Make post visible to all users',
    icon: Eye,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-700'
  },
  {
    value: PostStatus.ARCHIVED,
    label: 'Archived',
    description: 'Hide post from public view',
    icon: Archive,
    color: 'text-gray-600 dark:text-gray-400',
    bgColor: 'bg-gray-50 dark:bg-gray-900/20',
    borderColor: 'border-gray-200 dark:border-gray-700'
  }
]

export function StatusSelector({
  label,
  value,
  onChange,
  disabled = false,
  className
}: StatusSelectorProps) {
  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </Label>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        {statusOptions.map((option) => {
          const Icon = option.icon
          const isSelected = value === option.value

          return (
            <button
              key={option.value}
              type="button"
              onClick={() => !disabled && onChange(option.value)}
              disabled={disabled}
              className={cn(
                "relative p-4 rounded-lg border-2 text-left transition-all duration-200",
                "hover:shadow-md focus:outline-none focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
                isSelected
                  ? `${option.bgColor} ${option.borderColor} ${option.color}`
                  : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300",
                disabled && "opacity-50 cursor-not-allowed",
                !disabled && "hover:border-green-300 dark:hover:border-green-600"
              )}
            >
              <div className="flex items-start space-x-3">
                <Icon className={cn(
                  "h-5 w-5 mt-0.5",
                  isSelected ? option.color : "text-gray-400 dark:text-gray-500"
                )} />
                <div className="flex-1 min-w-0">
                  <div className={cn(
                    "font-medium text-sm",
                    isSelected ? option.color : "text-gray-900 dark:text-white"
                  )}>
                    {option.label}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {option.description}
                  </div>
                </div>
              </div>

              {isSelected && (
                <div className={cn(
                  "absolute top-2 right-2 w-2 h-2 rounded-full",
                  option.color.replace('text-', 'bg-')
                )} />
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}
