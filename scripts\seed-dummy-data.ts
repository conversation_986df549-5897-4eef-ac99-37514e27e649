#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createServerSupabaseAdminClient } from '../lib/supabase/server'
import { v4 as uuidv4 } from 'uuid'

// Load environment variables
config({ path: '.env.local' })

async function seedDummyData() {
  console.log('🌱 Seeding dummy data...')

  try {
    const supabase = createServerSupabaseAdminClient()

    // Step 1: Get existing user profile (admin user)
    console.log('\n1️⃣ Getting existing user profile...')

    const { data: userProfiles, error: userError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1)

    if (userError || !userProfiles || userProfiles.length === 0) {
      console.error('❌ No user profiles found. Please create an admin user first.')
      return
    }

    const userProfile = userProfiles[0]
    const dummyUserId = userProfile.id
    console.log('✅ Using existing user profile:', userProfile.name)

    // Step 2: Get post types
    console.log('\n2️⃣ Getting post types...')
    const { data: postTypes, error: typesError } = await supabase
      .from('post_types')
      .select('*')

    if (typesError || !postTypes || postTypes.length === 0) {
      console.error('❌ No post types found. Run seed-post-types first.')
      return
    }

    console.log(`✅ Found ${postTypes.length} post types`)

    // Step 3: Create dummy posts
    console.log('\n3️⃣ Creating dummy posts...')

    const dummyPosts = [
      {
        title: 'Getting Started with React Hooks',
        slug: 'getting-started-with-react-hooks',
        content: `# Getting Started with React Hooks

React Hooks revolutionized how we write React components. In this post, we'll explore the most commonly used hooks and how they can simplify your code.

## useState Hook

The useState hook allows you to add state to functional components:

\`\`\`javascript
const [count, setCount] = useState(0);
\`\`\`

## useEffect Hook

The useEffect hook lets you perform side effects in functional components:

\`\`\`javascript
useEffect(() => {
  document.title = \`Count: \${count}\`;
}, [count]);
\`\`\`

This is a comprehensive guide to getting started with React Hooks.`,
        type_id: postTypes.find(t => t.slug === 'learning')?.id || postTypes[0].id,
        status: 'PUBLISHED',
        featured: true,
        tags: ['react', 'hooks', 'javascript'],
        author_id: dummyUserId
      },
      {
        title: 'Common JavaScript Errors and How to Fix Them',
        slug: 'common-javascript-errors-and-fixes',
        content: `# Common JavaScript Errors and How to Fix Them

Every developer encounters errors. Here are some common JavaScript errors and their solutions.

## TypeError: Cannot read property of undefined

This is one of the most common errors in JavaScript:

\`\`\`javascript
// Error
const user = null;
console.log(user.name); // TypeError

// Fix
console.log(user?.name); // undefined (safe)
\`\`\`

## ReferenceError: Variable is not defined

This happens when you try to use a variable that hasn't been declared:

\`\`\`javascript
// Error
console.log(myVariable); // ReferenceError

// Fix
const myVariable = 'Hello World';
console.log(myVariable); // Works!
\`\`\`

Understanding these errors will make you a better developer.`,
        type_id: postTypes.find(t => t.slug === 'error-log')?.id || postTypes[1].id,
        status: 'PUBLISHED',
        featured: false,
        tags: ['javascript', 'debugging', 'errors'],
        author_id: dummyUserId
      },
      {
        title: 'My Thoughts on the Future of Web Development',
        slug: 'future-of-web-development-opinion',
        content: `# My Thoughts on the Future of Web Development

The web development landscape is constantly evolving. Here are my thoughts on where we're heading.

## Server-Side Rendering is Back

With frameworks like Next.js and Nuxt.js, SSR is making a comeback. The benefits are clear:

- Better SEO
- Faster initial page loads
- Improved user experience

## WebAssembly Will Gain Traction

WebAssembly allows us to run high-performance code in the browser. I believe we'll see more adoption in:

- Gaming
- Image/video processing
- Scientific computing

## The Rise of Edge Computing

Edge computing brings computation closer to users, reducing latency and improving performance.

What do you think? Share your thoughts in the comments!`,
        type_id: postTypes.find(t => t.slug === 'opinion')?.id || postTypes[2].id,
        status: 'PUBLISHED',
        featured: false,
        tags: ['web-development', 'future', 'opinion'],
        author_id: dummyUserId
      },
      {
        title: 'Quick Tip: Use CSS Grid for Better Layouts',
        slug: 'css-grid-layout-tip',
        content: `# Quick Tip: Use CSS Grid for Better Layouts

CSS Grid is a powerful layout system that can simplify your CSS. Here's a quick tip:

## Basic Grid Setup

\`\`\`css
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}
\`\`\`

This creates a responsive grid that automatically adjusts the number of columns based on available space.

## Why This Works

- \`auto-fit\`: Automatically fits columns
- \`minmax(300px, 1fr)\`: Minimum 300px, maximum 1 fraction
- \`gap\`: Adds space between grid items

Try it out in your next project!`,
        type_id: postTypes.find(t => t.slug === 'tip')?.id || postTypes[3].id,
        status: 'DRAFT',
        featured: false,
        tags: ['css', 'grid', 'layout', 'tip'],
        author_id: dummyUserId
      }
    ]

    for (const post of dummyPosts) {
      const { data: createdPost, error: postError } = await supabase
        .from('posts')
        .insert({
          ...post,
          version: 'v1.0',
          excerpt: false,
          read_time: '5 min read',
          view_count: Math.floor(Math.random() * 1000),
          reactions_heart: Math.floor(Math.random() * 50),
          comments_count: Math.floor(Math.random() * 10),
          published_at: post.status === 'PUBLISHED' ? new Date().toISOString() : null
        })
        .select()
        .single()

      if (postError) {
        console.error(`❌ Error creating post "${post.title}":`, postError)
      } else {
        console.log(`✅ Created post: ${createdPost.title}`)
      }
    }

    console.log('\n🎉 Dummy data seeding completed!')

    // Show summary
    const { count: totalPosts } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })

    const { count: totalProfiles } = await supabase
      .from('user_profiles')
      .select('*', { count: 'exact', head: true })

    console.log('\n📊 Summary:')
    console.log(`   - User profiles: ${totalProfiles}`)
    console.log(`   - Posts: ${totalPosts}`)

  } catch (error) {
    console.error('❌ Seeding error:', error)
    process.exit(1)
  }
}

// Run the seeding
seedDummyData()
