# Authentication System

Personal website menggunakan **admin-only authentication** dengan Supabase Auth.

## 🔒 **Admin-Only Access**

Website ini tidak memiliki public registration. Hanya admin yang bisa login untuk manage content.

### **Authentication Flow:**

```
1. Admin login dengan email/password → Supabase Auth
2. Supabase returns JWT token
3. Frontend stores token
4. Dashboard API requests include Bearer token
5. Middleware validates token dengan Supabase
6. Admin dapat akses dashboard untuk manage posts
```

## 🛡️ **Security Features**

- **JWT Tokens**: Managed by Supabase (secure, auto-refresh)
- **Row Level Security**: Database-level security policies
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Zod schema validation
- **Admin-Only Access**: No public registration

## 📡 **API Endpoints**

### **Public Endpoints (No Auth Required)**
- `GET /api/posts` - Get published posts
- `GET /api/posts/[slug]` - Get post by slug
- `GET /api/post-types` - Get post types

### **Authentication Endpoints**
- `POST /api/auth/supabase/login` - Admin login
- `POST /api/auth/supabase/logout` - Admin logout
- `GET /api/auth/supabase/me` - Get admin info

### **Protected Dashboard Endpoints (Admin Only)**
- `GET /api/dashboard/posts` - Get all posts (including drafts)
- `POST /api/dashboard/posts` - Create new post
- `PUT /api/dashboard/posts/[id]` - Update post
- `DELETE /api/dashboard/posts/[id]` - Delete post
- `GET /api/dashboard/post-types` - Get post types for dashboard
- `GET /api/dashboard/analytics` - Get analytics data

## 🔧 **Setup Admin User**

### **Method 1: Using Script (Recommended)**

```bash
# Set admin credentials in environment
export ADMIN_EMAIL="<EMAIL>"
export ADMIN_PASSWORD="your-secure-password"
export ADMIN_NAME="Your Name"

# Create admin user
pnpm create-supabase-admin
```

### **Method 2: Using Supabase Dashboard**

1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Navigate to Authentication > Users
3. Click "Add User"
4. Fill in email, password, and metadata
5. Set `email_confirm: true`
6. User profile will be auto-created

### **Method 3: Manual SQL**

```sql
-- In Supabase SQL Editor
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_user_meta_data
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('your-password', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"name": "Admin User"}'::jsonb
);
```

## 🧪 **Testing Authentication**

```bash
# Test admin login and dashboard access
pnpm test-auth-api

# Test with development server
pnpm dev
# Navigate to /admin-access for login
```

## 🎨 **Frontend Integration**

### **Login Page**
- Location: `/admin-access`
- Simple email/password form
- Redirect to dashboard after login

### **Dashboard Protection**
- All dashboard routes require authentication
- Auto-redirect to login if not authenticated
- JWT token stored in localStorage/cookies

### **Logout**
- Available in dashboard header
- Clears token and redirects to homepage

## 🔄 **Token Management**

### **Access Token**
- Valid for 1 hour
- Automatically refreshed by Supabase
- Stored securely in frontend

### **Refresh Token**
- Used to get new access tokens
- Handled automatically by Supabase client

## 🚨 **Security Best Practices**

1. **Strong Passwords**: Use complex passwords for admin accounts
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Expiry**: Tokens expire automatically
4. **Rate Limiting**: Login attempts are rate limited
5. **Environment Variables**: Never commit credentials to git

## 🔧 **Environment Variables**

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Admin Setup (optional, for scripts)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="secure-password"
ADMIN_NAME="Admin User"
```

## 🐛 **Troubleshooting**

### **Login Failed**
- Check email/password
- Verify user exists in Supabase Auth
- Check network connectivity

### **Dashboard Access Denied**
- Verify JWT token is valid
- Check if user profile exists
- Ensure RLS policies are correct

### **Token Expired**
- Supabase handles refresh automatically
- If issues persist, logout and login again

## 📚 **Related Documentation**

- [Supabase Setup Guide](./SUPABASE_SETUP.md)
- [API Documentation](./API.md)
- [Frontend Integration](./FRONTEND.md)
