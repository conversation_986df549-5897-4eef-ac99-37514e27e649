<!DOCTYPE html>
<html>
<head>
    <title>Test Redirect</title>
</head>
<body>
    <h1>Test Authentication Redirect</h1>
    
    <div>
        <h2>Test Cases:</h2>
        <ol>
            <li><a href="http://localhost:3001/dashboard" target="_blank">Access Dashboard (should redirect to login)</a></li>
            <li><a href="http://localhost:3001/admin-access" target="_blank">Access Login Page</a></li>
            <li>Login with: <EMAIL> / Admin123!@#</li>
            <li>Should redirect to dashboard after login</li>
            <li><a href="http://localhost:3001/admin-access" target="_blank">Access Login Page Again (should redirect to dashboard)</a></li>
        </ol>
    </div>

    <div>
        <h2>Clear Cookies:</h2>
        <button onclick="clearCookies()">Clear Auth Cookies</button>
    </div>

    <script>
        function clearCookies() {
            document.cookie = "auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            alert('Cookies cleared! You can now test login flow.');
        }
    </script>
</body>
</html>
