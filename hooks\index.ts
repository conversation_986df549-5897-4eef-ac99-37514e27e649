// Custom hooks exports
export { useIsMobile } from './use-mobile'
export { useToast, toast } from './use-toast'
export { useStickySidebar, useSimpleStickySidebar } from './use-sticky-sidebar'
export { useSimpleSticky, useBasicSticky } from './use-simple-sticky'
export { useAdvancedSticky } from './use-advanced-sticky'

// Post-related hooks
export {
  usePosts,
  usePost,
  useFeaturedPosts,
  usePostSearch
} from './use-posts'

export {
  usePostTypes,
  usePostType,
  usePostTypesWithCounts
} from './use-post-types'

// API hooks
export { useApi } from './use-api'
export { usePaginatedApi } from './use-paginated-api'
export { usePostsApi, usePostStats, usePostApi } from './use-posts-api'
export { useSupabaseApi, useDashboardApi } from './use-supabase-api'

// State management hooks
export { useReactions } from './use-reactions'
export { useComments } from './use-comments'
export { useCommentsApi } from './use-comments-api'
export { useForm } from './use-form'

// Re-export types for convenience
export type {
  UsePostsOptions,
  UsePostsReturn,
  UsePostOptions,
  UsePostReturn,
  UseFeaturedPostsReturn,
  UsePostSearchOptions,
  UsePostSearchReturn
} from './use-posts'

export type {
  UsePostTypesReturn,
  UsePostTypeOptions,
  UsePostTypeReturn,
  UsePostTypesWithCountsReturn
} from './use-post-types'

export type {
  Reactions,
  ReactionType
} from './use-reactions'

export type {
  Comment
} from './use-comments'
