"use client"

import { useEffect, useState, useRef } from 'react'

interface UseStickyOptions {
  topOffset?: number
  bottomOffset?: number
  containerSelector?: string
}

export function useStickySidebar(options: UseStickyOptions = {}) {
  const {
    topOffset = 96, // Default top offset (header height + padding)
    bottomOffset = 32, // Default bottom offset
    containerSelector = 'main'
  } = options

  const sidebarRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)
  const [isBottom, setIsBottom] = useState(false)
  const [sidebarHeight, setSidebarHeight] = useState(0)
  const [sidebarWidth, setSidebarWidth] = useState(0)

  useEffect(() => {
    const sidebar = sidebarRef.current
    if (!sidebar) return

    // Calculate sidebar dimensions
    const updateSidebarDimensions = () => {
      setSidebarHeight(sidebar.scrollHeight)
      setSidebarWidth(sidebar.offsetWidth)
    }

    // Initial dimensions calculation
    updateSidebarDimensions()

    const handleScroll = () => {
      const container = document.querySelector(containerSelector) as HTMLElement
      if (!container || !sidebar) return

      const scrollTop = window.scrollY
      const windowHeight = window.innerHeight
      const containerTop = container.offsetTop
      const containerHeight = container.scrollHeight
      const containerBottom = containerTop + containerHeight

      // Calculate available space for sidebar
      const availableHeight = windowHeight - topOffset - bottomOffset
      const sidebarFitsInViewport = sidebarHeight <= availableHeight

      // Determine sticky behavior
      const shouldBeSticky = scrollTop >= (containerTop - topOffset)
      const shouldBeAtBottom = (scrollTop + windowHeight) >= (containerBottom - bottomOffset)

      if (sidebarFitsInViewport) {
        // If sidebar fits in viewport, use normal sticky behavior
        setIsSticky(shouldBeSticky && !shouldBeAtBottom)
        setIsBottom(shouldBeAtBottom && shouldBeSticky)
      } else {
        // If sidebar is taller than viewport, allow it to scroll naturally
        // but still stick when appropriate
        const sidebarTop = sidebar.getBoundingClientRect().top
        const sidebarBottom = sidebar.getBoundingClientRect().bottom

        if (sidebarTop <= topOffset && sidebarBottom >= windowHeight - bottomOffset) {
          // Sidebar is filling the viewport, allow natural scroll
          setIsSticky(false)
          setIsBottom(false)
        } else if (sidebarTop > topOffset) {
          // Sidebar top is below the sticky point
          setIsSticky(shouldBeSticky && !shouldBeAtBottom)
          setIsBottom(false)
        } else {
          // Sidebar bottom is above the viewport
          setIsSticky(false)
          setIsBottom(shouldBeAtBottom)
        }
      }
    }

    // Handle resize to recalculate dimensions
    const handleResize = () => {
      updateSidebarDimensions()
      handleScroll()
    }

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleResize, { passive: true })

    // Initial calculation
    handleScroll()

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [topOffset, bottomOffset, containerSelector, sidebarHeight])

  // Generate CSS classes based on state
  const getStickyClasses = () => {
    if (isBottom) {
      return `absolute bottom-${bottomOffset/4} left-0 right-0`
    }

    if (isSticky) {
      return `fixed top-${topOffset/4} left-0 right-0 z-10`
    }

    return 'relative'
  }

  // Generate inline styles for better control
  const getStickyStyles = (): React.CSSProperties => {
    if (isBottom) {
      return {
        position: 'absolute',
        bottom: `${bottomOffset}px`,
        left: 0,
        right: 0,
        width: `${sidebarWidth}px`,
      }
    }

    if (isSticky) {
      return {
        position: 'fixed',
        top: `${topOffset}px`,
        left: 'auto',
        right: 'auto',
        zIndex: 10,
        width: `${sidebarWidth}px`,
        maxWidth: `${sidebarWidth}px`,
      }
    }

    return {
      position: 'relative',
    }
  }

  return {
    sidebarRef,
    containerRef,
    isSticky,
    isBottom,
    sidebarHeight,
    sidebarWidth,
    getStickyClasses,
    getStickyStyles,
  }
}

/**
 * Simplified sticky sidebar hook for basic use cases
 */
export function useSimpleStickySidebar(topOffset: number = 96) {
  const sidebarRef = useRef<HTMLDivElement>(null)
  const [isSticky, setIsSticky] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (!sidebarRef.current) return

      const sidebarTop = sidebarRef.current.offsetTop
      const scrollTop = window.scrollY

      setIsSticky(scrollTop >= (sidebarTop - topOffset))
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    handleScroll() // Initial check

    return () => window.removeEventListener('scroll', handleScroll)
  }, [topOffset])

  return {
    sidebarRef,
    isSticky,
    stickyClasses: isSticky ? `fixed top-${topOffset/4} z-10` : 'relative'
  }
}
