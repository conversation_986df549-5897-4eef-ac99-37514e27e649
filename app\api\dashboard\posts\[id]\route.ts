import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { withSupabaseDashboardMiddleware, getUserFromSupabaseRequest, withSupabaseRoleAuth } from '@/lib/api/supabase-auth-middleware'
import { withErrorHandler, withLogging, withRateLimit } from '@/lib/api/middleware'
import { PostStatus } from '@/types'
import { z } from 'zod'
import { ErrorCode } from '@/types/api'

const postService = new PostApiSimpleService()

// Validation schema for post images
const postImageSchema = z.object({
  url: z.string()
    .min(1, 'Image URL is required'),
  altText: z.string()
    .max(125, 'Alt text must be less than 125 characters')
    .optional(),
  caption: z.string()
    .max(300, 'Caption must be less than 300 characters')
    .optional(),
  name: z.string()
    .min(1, 'Image name is required'),
  size: z.number()
    .min(1, 'Image size must be greater than 0')
})

// Validation schema for update post request
const updatePostSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  slug: z.string()
    .min(1, 'Slug is required')
    .max(200, 'Slug must be less than 200 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  content: z.string()
    .min(1, 'Content is required')
    .max(50000, 'Content must be less than 50,000 characters'),
  typeId: z.string()
    .min(1, 'Post type is required'),
  status: z.nativeEnum(PostStatus)
    .default(PostStatus.DRAFT),
  featured: z.boolean()
    .default(false),
  showFullContent: z.boolean()
    .default(false),
  version: z.string()
    .min(1, 'Version is required')
    .max(20, 'Version must be less than 20 characters')
    .default('1.0.0'),
  tags: z.array(z.string())
    .default([]),
  images: z.array(postImageSchema)
    .max(10, 'Maximum 10 images allowed')
    .default([])
})

async function getPostByIdHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    console.log('📖 Fetching post by ID:', resolvedParams.id)
    const { id } = resolvedParams

    if (!id) {
      return ApiResponseBuilder.validationError(
        'Post ID is required',
        'id'
      )
    }

    // Get authenticated user (optional for GET)
    const user = getUserFromSupabaseRequest(request)
    console.log('User from request:', user?.id || 'No user')

    // Get post by ID
    const post = await postService.getPostById(id)

    if (!post) {
      return ApiResponseBuilder.notFound('Post not found')
    }

    // For dashboard access, check if user can access this post
    // (either owner or admin, but for now allow all authenticated users)
    if (!user) {
      return ApiResponseBuilder.unauthorized('Authentication required')
    }

    console.log('Post found:', post.id, 'Title:', post.title)

    return ApiResponseBuilder.success(
      post,
      'Post retrieved successfully'
    )
  } catch (error) {
    console.error('Error in GET /api/dashboard/posts/[id]:', error)
    return ApiResponseBuilder.internalError('Failed to retrieve post')
  }
}

async function updatePostHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('📝 Updating post...')

    const resolvedParams = await params
    const postId = resolvedParams.id

    if (!postId) {
      return ApiResponseBuilder.validationError(
        'Post ID is required',
        'id',
        { errors: [{ field: 'id', message: 'Post ID is required' }] }
      )
    }

    // Parse request body
    const body = await request.json()
    console.log('Request body:', body)

    // Validate request data
    const validatedData = updatePostSchema.parse(body)
    console.log('Validated data:', validatedData)

    // Get authenticated user from request
    const user = getUserFromSupabaseRequest(request)
    if (!user) {
      return ApiResponseBuilder.unauthorized('Authentication required')
    }
    console.log('Authenticated user:', user.id)

    // Check if post exists and user has permission
    const existingPost = await postService.getPostById(postId)
    if (!existingPost) {
      return ApiResponseBuilder.notFound('Post not found')
    }

    // Get user role from request (for now all authenticated users are considered admins)
    // Later we can add role field to user_profiles table
    const userRole = 'admin' // This will be replaced with actual role check later

    // Allow access if user is admin or owns the post
    if (userRole !== 'admin' && existingPost.authorId !== user.id) {
      return ApiResponseBuilder.forbidden('You do not have permission to edit this post')
    }

    // Update post using service
    const postData = {
      title: validatedData.title,
      slug: validatedData.slug,
      content: validatedData.content,
      typeId: validatedData.typeId,
      status: validatedData.status,
      featured: validatedData.featured || false,
      excerpt: validatedData.showFullContent ? false : true, // Invert showFullContent to excerpt
      version: validatedData.version,
      tags: validatedData.tags || [],
      images: validatedData.images || []
    }

    console.log('Updating post with data:', {
      ...postData,
      images: postData.images.map(img => ({
        name: img.name,
        size: img.size,
        hasAltText: !!img.altText,
        hasCaption: !!img.caption
      }))
    })

    const updatedPost = await postService.updatePost(postId, postData)
    console.log('Post updated successfully:', updatedPost.id)

    return ApiResponseBuilder.success(
      updatedPost,
      `Post ${validatedData.status === 'PUBLISHED' ? 'updated and published' : 'updated as draft'} successfully`
    )
  } catch (error) {
    console.error('Error in updatePostHandler:', error)

    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(
        'Validation failed',
        error.errors[0]?.path.join('.') || 'unknown',
        { errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        })) }
      )
    }

    if (error instanceof Error) {
      // Handle specific error messages
      if (error.message.includes('Slug is already taken')) {
        return ApiResponseBuilder.validationError(
          'Slug is already taken',
          'slug',
          { errors: [{ field: 'slug', message: error.message }] }
        )
      }

      return ApiResponseBuilder.badRequest(error.message)
    }

    return ApiResponseBuilder.internalError('Failed to update post')
  }
}

// Apply dashboard middleware with authentication
export const GET = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(500, 15 * 60 * 1000) // 500 requests per 15 minutes
)(getPostByIdHandler)

export const PUT = withSupabaseDashboardMiddleware(
  withErrorHandler,
  withLogging,
  withRateLimit(100, 15 * 60 * 1000) // 100 requests per 15 minutes for updates
)(updatePostHandler)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
