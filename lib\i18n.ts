export const locales = ['en', 'id'] as const
export type Locale = typeof locales[number]
export const defaultLocale: Locale = 'en'

// Utility function to get locale display names
export function getLocaleDisplayName(locale: Locale): string {
  const displayNames: Record<Locale, string> = {
    en: 'English',
    id: 'Bahasa Indonesia'
  }
  return displayNames[locale]
}

// Utility function to get locale flags
export function getLocaleFlag(locale: Locale): string {
  const flags: Record<Locale, string> = {
    en: '🇺🇸',
    id: '🇮🇩'
  }
  return flags[locale]
}

// Get messages for a locale
export async function getMessages(locale: Locale) {
  try {
    const messages = await import(`../messages/${locale}.json`)
    return messages.default
  } catch (error) {
    console.warn(`Failed to load messages for locale: ${locale}`)
    return {}
  }
}

// Simple translation function
export function createTranslator(messages: Record<string, any>) {
  return function t(key: string, params?: Record<string, string | number>): string {
    const keys = key.split('.')
    let value = messages
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return key // Return key if translation not found
      }
    }
    
    if (typeof value !== 'string') {
      return key
    }
    
    // Simple parameter replacement
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match
      })
    }
    
    return value
  }
}
