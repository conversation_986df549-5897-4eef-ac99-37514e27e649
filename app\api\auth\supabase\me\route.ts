import { NextRequest } from 'next/server'
import { ApiResponseBuilder } from '@/lib/api/response-builder'
import { getAuthenticatedUserFromRequest } from '@/lib/supabase/auth'
import { withError<PERSON>and<PERSON>, withLogging, withRateLimit } from '@/lib/api/middleware'

async function meHandler(request: NextRequest) {
  try {
    // Get authenticated user from Supabase
    const user = await getAuthenticatedUserFromRequest(request)

    if (!user) {
      return ApiResponseBuilder.unauthorized(
        'Authentication required'
      )
    }

    return ApiResponseBuilder.success(
      {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      },
      'User information retrieved successfully'
    )
  } catch (error) {
    console.error('Get user info error:', error)
    return ApiResponseBuilder.internalError('Failed to get user information')
  }
}

// Apply middleware
export const GET = withErrorHandler(
  withLogging(
    withRateLimit(60, 15 * 60 * 1000)(meHandler) // 60 requests per 15 minutes
  )
)

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return ApiResponseBuilder.success(null, 'CORS preflight')
}
