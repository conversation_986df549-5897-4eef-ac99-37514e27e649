import {
  BookOpen,
  AlertCircle,
  Lightbulb,
  Code,
  Star,
  ImageIcon,
  Mail,
  Github,
  Instagram,
  Facebook,
  Linkedin
} from 'lucide-react'

// Post type configurations for seeding and UI
export const POST_TYPE_CONFIG: Record<string, {
  color: string
  icon: any
  iconName: string
  label: string
  slug: string
}> = {
  learning: {
    color: "#3b82f6", // blue-500
    icon: BookOpen,
    iconName: "BookOpen",
    label: "Learning",
    slug: "learning"
  },
  'error-log': {
    color: "#ef4444", // red-500
    icon: AlertCircle,
    iconName: "AlertCircle",
    label: "Error Log",
    slug: "error-log"
  },
  opinion: {
    color: "#a855f7", // purple-500
    icon: Lightbulb,
    iconName: "Lightbulb",
    label: "Opinion",
    slug: "opinion"
  },
  tip: {
    color: "#10b981", // emerald-500
    icon: Code,
    iconName: "Code",
    label: "Tip",
    slug: "tip"
  },
  showcase: {
    color: "#f59e0b", // amber-500
    icon: Star,
    iconName: "Star",
    label: "Showcase",
    slug: "showcase"
  },
  photo: {
    color: "#ec4899", // pink-500
    icon: ImageIcon,
    iconName: "ImageIcon",
    label: "Photo",
    slug: "photo"
  }
}

// Icon mapping for dynamic icon loading
export const ICON_MAP: Record<string, any> = {
  BookOpen,
  AlertCircle,
  Lightbulb,
  Code,
  Star,
  ImageIcon,
}

// Helper function to get icon by slug (fallback)
export const getPostTypeIcon = (slug: string) => {
  return POST_TYPE_CONFIG[slug]?.icon || BookOpen
}

// Helper function to get icon component by icon name from database
export const getIconByName = (iconName: string) => {
  return ICON_MAP[iconName] || BookOpen
}

// Helper function to generate CSS classes from hex color
export const getPostTypeClasses = (hexColor: string | undefined) => {
  // Fallback to default color if hexColor is undefined
  const color = hexColor || '#6b7280'

  // Convert hex to RGB for opacity calculations
  const hex = color.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  return {
    background: `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0.1), rgba(${r}, ${g}, ${b}, 0.2))`,
    color: color,
    borderColor: `rgba(${r}, ${g}, ${b}, 0.3)`,
    // For Tailwind classes (fallback)
    className: `bg-gradient-to-r border-opacity-30`
  }
}

// Social media configurations
export const SOCIAL_LINKS = [
  { icon: Github, label: 'GitHub', key: 'github' as const },
  { icon: Instagram, label: 'Instagram', key: 'instagram' as const },
  { icon: Facebook, label: 'Facebook', key: 'facebook' as const },
  { icon: Linkedin, label: 'LinkedIn', key: 'linkedin' as const },
  { icon: Mail, label: 'Email', key: 'email' as const },
]

// Navigation items
export const NAVIGATION_ITEMS = [
  { label: 'Home', href: '/' },
  { label: 'Experience', href: '/experience' },
  { label: 'Portfolio', href: '/portfolio' },
  { label: 'About', href: '/about' },
]

// Portfolio categories
export const PORTFOLIO_CATEGORIES = [
  "All",
  "Full Stack",
  "Web App",
  "Data Visualization",
  "Analytics"
]

// Post filter options
export const POST_FILTERS = [
  "all",
  "learning",
  "opinion",
  "tip",
  "photo",
  "showcase",
  "error-log"
]

// Skills data
export const SKILLS_DATA = [
  { category: "Frontend", technologies: ["React", "Next.js", "Vue.js", "TypeScript", "Tailwind CSS"] },
  { category: "Backend", technologies: ["Node.js", "Express", "Python", "PostgreSQL", "MongoDB"] },
  { category: "DevOps", technologies: ["AWS", "Docker", "CI/CD", "Kubernetes", "Terraform"] },
  { category: "Tools", technologies: ["Git", "Figma", "Jira", "Slack", "VS Code"] },
]

// Application metadata
export const APP_CONFIG = {
  name: "Rijal Solahudin",
  tagline: "Personal Portfolio",
  version: "v1.0.0",
  description: "A hybrid platform combining social posts, GitHub repositories, and blogging functionality for organizing and sharing thoughts.",
  author: "Rijal Solahudin"
}
