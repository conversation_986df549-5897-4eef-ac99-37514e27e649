{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "supabase:setup": "tsx scripts/setup-supabase.ts", "supabase:test": "tsx scripts/test-supabase-api.ts", "supabase:seed-types": "tsx scripts/seed-post-types.ts", "supabase:seed-data": "tsx scripts/seed-dummy-data.ts", "supabase:test-posts": "tsx scripts/test-posts-api.ts", "supabase:create-admin": "tsx scripts/create-supabase-admin.ts", "supabase:test-auth": "tsx scripts/test-auth-api.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@supabase/supabase-js": "^2.39.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "framer-motion": "^12.15.0", "input-otp": "latest", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "latest", "react-window": "^1.8.11", "recharts": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "dotenv": "^16.4.5", "postcss": "^8", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5"}}