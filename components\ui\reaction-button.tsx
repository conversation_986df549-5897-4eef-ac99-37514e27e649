import { Button } from "@/components/ui/button"
import { ThumbsUp, Heart, Brain } from "lucide-react"
import { buttonVariants } from "@/lib/utils/ui"
import { cn } from "@/lib/utils"

export type ReactionType = 'thumbsUp' | 'heart' | 'brain'

interface ReactionButtonProps {
  type: ReactionType
  count: number
  onClick: () => void
  size?: 'sm' | 'default'
  variant?: 'outline' | 'ghost'
  className?: string
}

const reactionIcons = {
  thumbsUp: ThumbsUp,
  heart: Heart,
  brain: Brain
}

/**
 * Reusable reaction button component with consistent styling
 */
export function ReactionButton({
  type,
  count,
  onClick,
  size = 'sm',
  variant = 'outline',
  className
}: ReactionButtonProps) {
  const IconComponent = reactionIcons[type]
  const reactionStyle = buttonVariants.reaction[type]

  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      className={cn(reactionStyle, className)}
    >
      <IconComponent className="w-4 h-4 mr-1" />
      {count}
    </Button>
  )
}

interface ReactionGroupProps {
  reactions: {
    thumbsUp: number
    heart: number
    brain: number
  }
  onReaction: (type: ReactionType) => void
  size?: 'sm' | 'default'
  variant?: 'outline' | 'ghost'
  className?: string
}

/**
 * Group of reaction buttons with consistent spacing
 */
export function ReactionGroup({
  reactions,
  onReaction,
  size = 'sm',
  variant = 'outline',
  className
}: ReactionGroupProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <ReactionButton
        type="thumbsUp"
        count={reactions.thumbsUp}
        onClick={() => onReaction('thumbsUp')}
        size={size}
        variant={variant}
      />
      <ReactionButton
        type="heart"
        count={reactions.heart}
        onClick={() => onReaction('heart')}
        size={size}
        variant={variant}
      />
      <ReactionButton
        type="brain"
        count={reactions.brain}
        onClick={() => onReaction('brain')}
        size={size}
        variant={variant}
      />
    </div>
  )
}
