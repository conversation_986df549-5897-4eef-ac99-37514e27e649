import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { AlertCircle, ChevronDown, Check } from 'lucide-react'
import { PostType } from '@/types'
import { useAuth } from '@/contexts/auth-context'

interface PostTypeSelectorProps {
  label: string
  value: string
  onChange: (typeId: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

export function PostTypeSelector({
  label,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  className
}: PostTypeSelectorProps) {
  const [postTypes, setPostTypes] = useState<PostType[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [fetchError, setFetchError] = useState<string>('')
  const hasError = !!error
  const { token, isAuthenticated } = useAuth()

  useEffect(() => {
    if (isAuthenticated && token) {
      fetchPostTypes()
    } else {
      // Fallback: try to fetch without auth for testing
      console.log('🔍 PostTypeSelector: No auth, trying fallback...')
      fetchPostTypesFallback()
    }
  }, [isAuthenticated, token])

  const fetchPostTypesFallback = async () => {
    try {
      console.log('🔍 PostTypeSelector: Using fallback endpoint...')
      setLoading(true)
      setFetchError('')

      const response = await fetch('/api/test/dashboard/post-types')
      console.log('🔍 PostTypeSelector: Fallback response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('🔍 PostTypeSelector: Fallback data received:', data)
        setPostTypes(data.data || [])
      } else {
        setFetchError('Failed to fetch post types (fallback)')
      }
    } catch (error) {
      console.error('🔍 PostTypeSelector: Fallback error:', error)
      setFetchError('Network error (fallback)')
    } finally {
      setLoading(false)
    }
  }

  const fetchPostTypes = async () => {
    try {
      console.log('🔍 PostTypeSelector: Starting fetch...')
      console.log('🔍 PostTypeSelector: isAuthenticated:', isAuthenticated)
      console.log('🔍 PostTypeSelector: token:', token ? 'Present' : 'Missing')

      setLoading(true)
      setFetchError('')

      const response = await fetch('/api/dashboard/post-types', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      console.log('🔍 PostTypeSelector: Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('🔍 PostTypeSelector: Data received:', data)
        console.log('🔍 PostTypeSelector: Post types count:', data.data?.length || 0)
        setPostTypes(data.data || [])
      } else {
        const errorData = await response.json()
        console.error('🔍 PostTypeSelector: Error response:', errorData)
        setFetchError(errorData.message || 'Failed to fetch post types')
      }
    } catch (error) {
      console.error('🔍 PostTypeSelector: Network error:', error)
      setFetchError('Network error while fetching post types')
    } finally {
      setLoading(false)
    }
  }

  const selectedType = postTypes.find(type => type.id === value)

  const handleSelect = (typeId: string) => {
    console.log('🔍 PostTypeSelector: Selecting type:', typeId)
    onChange(typeId)
    setIsOpen(false)
  }

  const handleToggleOpen = () => {
    if (!disabled) {
      console.log('🔍 PostTypeSelector: Toggling dropdown. Current isOpen:', isOpen)
      console.log('🔍 PostTypeSelector: Available post types:', postTypes.length)
      setIsOpen(!isOpen)
    }
  }

  if (loading) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <div className="h-10 bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse" />
      </div>
    )
  }

  if (fetchError) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <div className="h-10 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md flex items-center px-3">
          <AlertCircle className="h-4 w-4 text-red-500 dark:text-red-400 mr-2" />
          <span className="text-sm text-red-600 dark:text-red-400">{fetchError}</span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>

      <div className="relative">
        <button
          type="button"
          onClick={handleToggleOpen}
          disabled={disabled}
          className={cn(
            "w-full px-3 py-2 text-left bg-white dark:bg-gray-800 border rounded-md",
            "flex items-center justify-between",
            "focus:outline-none focus:ring-0 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 dark:focus-visible:ring-green-400 focus-visible:ring-offset-0",
            hasError
              ? "border-red-500 dark:border-red-400 focus-visible:ring-red-500 dark:focus-visible:ring-red-400"
              : "border-gray-300 dark:border-gray-600",
            disabled && "opacity-50 cursor-not-allowed",
            !disabled && "hover:border-green-400 dark:hover:border-green-500"
          )}
        >
          <div className="flex items-center space-x-2">
            {selectedType ? (
              <>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: selectedType.color }}
                />
                <span className="text-gray-900 dark:text-white">
                  {selectedType.name}
                </span>
              </>
            ) : (
              <span className="text-gray-500 dark:text-gray-400">
                Select post type...
              </span>
            )}
          </div>
          <ChevronDown className={cn(
            "h-4 w-4 text-gray-400 transition-transform duration-200",
            isOpen && "transform rotate-180"
          )} />
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
            <div className="py-1 max-h-60 overflow-auto">
              {postTypes.map((type) => (
                <button
                  key={type.id}
                  type="button"
                  onClick={() => handleSelect(type.id)}
                  className={cn(
                    "w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700",
                    "flex items-center justify-between",
                    "focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700",
                    value === type.id && "bg-green-50 dark:bg-green-900/20"
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: type.color }}
                    />
                    <span className="text-gray-900 dark:text-white">
                      {type.name}
                    </span>
                  </div>
                  {value === type.id && (
                    <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {hasError && (
        <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}
