"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  ExternalLink,
  Users,
  TrendingUp,
  Code,
  Award,
  Filter,
  Search,
  Calendar,
  Eye,
  Star,
  Clock,
  CheckCircle,
  Zap,
  Globe,
  ChevronRight,
  BarChart3,
  Target,
} from "lucide-react"
import Link from "next/link"
import NextImage from "next/image"
import { Input } from "@/components/ui/input"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { LanguageSwitcher } from "@/components/navigation/language-switcher"
import { PortfolioProject } from "@/types"

interface PortfolioPageClientProps {
  portfolio: PortfolioProject[]
  locale: string
  translations: {
    navigation: {
      backToHome: string
      portfolio: string
    }
    header: {
      title: string
      subtitle: string
      description: string
    }
    filters: {
      all: string
      categories: string
      search: string
      searchPlaceholder: string
    }
    project: {
      viewProject: string
      viewCode: string
      caseStudy: string
      technologies: string
      features: string
      challenges: string
      solutions: string
      metrics: string
      status: string
      duration: string
      team: string
      role: string
    }
    stats: {
      projects: string
      users: string
      rating: string
    }
    cta: {
      startProject: string
      viewExperience: string
    }
  }
}

export function PortfolioPageClient({
  portfolio,
  locale,
  translations
}: PortfolioPageClientProps) {
  const [selectedCategory, setSelectedCategory] = useState(translations.filters.all)
  const [searchTerm, setSearchTerm] = useState("")
  const [isVisible, setIsVisible] = useState(false)
  const [hoveredProject, setHoveredProject] = useState<string | null>(null)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  // Get unique categories from portfolio data
  const categories = [translations.filters.all, ...Array.from(new Set(portfolio.map(p => p.category)))]

  const filteredProjects = portfolio.filter(project => {
    const matchesCategory = selectedCategory === translations.filters.all || project.category === selectedCategory
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 theme-transition">
      {/* Enhanced Header */}
      <header className="sticky top-0 z-50 border-b border-gray-200/50 dark:border-gray-800/50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl theme-transition">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="text-gray-600 hover:text-green-500 dark:text-gray-400 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 border border-transparent hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 theme-transition"
              >
                <Link href={`/${locale}`}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {translations.navigation.backToHome}
                </Link>
              </Button>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white theme-transition">
                {translations.navigation.portfolio}
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <LanguageSwitcher />
              <ThemeSwitcher />
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 pb-32">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Enhanced Portfolio Overview */}
          <Card className={`group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-500 theme-transition ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <CardContent className="p-8 relative overflow-hidden">
              {/* Header Content */}
              <div className="text-center space-y-6 relative z-10">
                <div className="space-y-4">
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white theme-transition">
                    {translations.header.title}
                  </h1>
                  <p className="text-xl text-green-600 dark:text-green-400 font-medium">
                    {translations.header.subtitle}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed theme-transition">
                    {translations.header.description}
                  </p>
                </div>

                {/* Stats Row */}
                <div className="grid grid-cols-3 gap-4 mb-8 max-w-md mx-auto">
                  {[
                    { icon: Target, value: "50+", label: translations.stats.projects },
                    { icon: Users, value: "100K+", label: translations.stats.users },
                    { icon: Star, value: "4.9", label: translations.stats.rating }
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <stat.icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      </div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">{stat.value}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-500">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Filters */}
          <Card className={`bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm theme-transition ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`} style={{ transitionDelay: '200ms' }}>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className={`transition-all duration-300 ${
                        selectedCategory === category
                          ? "bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-green-500/25"
                          : "border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-400 hover:border-green-500/50 hover:text-green-600 dark:hover:border-green-500/70 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
                      } theme-transition`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder={translations.filters.searchPlaceholder}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64 border-gray-300 dark:border-gray-700 focus:border-green-500 dark:focus:border-green-500 theme-transition"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Projects Grid */}
          <div className="grid md:grid-cols-2 gap-8">
            {filteredProjects.map((project, index) => (
              <Card
                key={project.id}
                className={`group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-2 theme-transition ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`}
                style={{ transitionDelay: `${400 + index * 150}ms` }}
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <CardContent className="p-0 relative overflow-hidden">
                  {/* Project Image with Enhanced Overlay */}
                  <div className="relative h-48 overflow-hidden">
                    <NextImage
                      src={project.image}
                      alt={project.title}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Status Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge 
                        variant={project.status === 'Live' ? 'default' : project.status === 'In Development' ? 'secondary' : 'outline'}
                        className={`${
                          project.status === 'Live' 
                            ? 'bg-green-600 text-white' 
                            : project.status === 'In Development'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-600 text-white'
                        } shadow-lg`}
                      >
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          project.status === 'Live' ? 'bg-green-300 animate-pulse' : 
                          project.status === 'In Development' ? 'bg-blue-300 animate-pulse' : 'bg-gray-300'
                        }`} />
                        {project.status}
                      </Badge>
                    </div>

                    {/* Quick Action Buttons */}
                    <div className={`absolute top-4 right-4 flex gap-2 transition-all duration-300 ${
                      hoveredProject === project.id ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'
                    }`}>
                      {project.links.live && (
                        <Button size="sm" className="bg-white/90 hover:bg-white text-gray-900 shadow-lg hover:shadow-xl transition-all duration-300">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      )}
                      {project.links.github && (
                        <Button size="sm" variant="outline" className="bg-white/90 hover:bg-white border-white/50 text-gray-900 shadow-lg hover:shadow-xl transition-all duration-300">
                          <Code className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Project Content */}
                  <div className="p-6 space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300 theme-transition">
                          {project.title}
                        </h3>
                        <Badge variant="outline" className="text-xs border-gray-300 dark:border-gray-700 theme-transition">
                          {project.category}
                        </Badge>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed theme-transition">
                        {project.description}
                      </p>
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.slice(0, 4).map((tech) => (
                        <Badge key={tech} variant="secondary" className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-green-100 dark:hover:bg-green-900/30 hover:text-green-700 dark:hover:text-green-300 transition-colors duration-300 theme-transition">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 4 && (
                        <Badge variant="outline" className="text-xs border-gray-300 dark:border-gray-700 text-gray-500 dark:text-gray-500 theme-transition">
                          +{project.technologies.length - 4}
                        </Badge>
                      )}
                    </div>

                    {/* Project Actions */}
                    <div className="flex gap-2 pt-2">
                      {project.links.live && (
                        <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700 text-white hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-green-500/25">
                          {translations.project.viewProject}
                          <ExternalLink className="w-3 h-3 ml-2" />
                        </Button>
                      )}
                      {project.links.github && (
                        <Button size="sm" variant="outline" className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:border-green-500/50 hover:text-green-600 dark:hover:border-green-500/70 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 hover:scale-105 transition-all duration-300 theme-transition">
                          {translations.project.viewCode}
                          <Code className="w-3 h-3 ml-2" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Call to Action */}
          <Card className={`bg-gradient-to-r from-green-600 to-green-700 border-0 text-white ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`} style={{ transitionDelay: '800ms' }}>
            <CardContent className="p-8 text-center space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-3xl font-bold">
                  {locale === 'id' ? 'Siap Memulai Proyek Bersama?' : 'Ready to Start a Project Together?'}
                </h2>
                <p className="text-green-100 max-w-2xl mx-auto">
                  {locale === 'id' 
                    ? 'Mari berkolaborasi untuk mewujudkan ide Anda menjadi solusi digital yang luar biasa.'
                    : "Let's collaborate to turn your ideas into exceptional digital solutions."
                  }
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild className="bg-white text-green-600 hover:bg-gray-50 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <Link href={`/${locale}/contact`}>
                    {translations.cta.startProject}
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  asChild
                  className="border-white/30 text-white hover:bg-white/10 hover:scale-105 transition-all duration-300"
                >
                  <Link href={`/${locale}/experience`}>
                    {translations.cta.viewExperience}
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
