# Supabase Setup Guide

Panduan untuk setup dan migrasi dari Prisma ke Supabase dengan Supabase Auth.

## 🔄 **Perubahan Penting: Menggunakan Supabase Auth**

<PERSON>mi telah mengupdate schema untuk menggunakan **Supabase Auth** built-in daripada custom users table:

### **Sebelum (Custom Auth)**
- ❌ Table: `public.users` (custom)
- ❌ Manual JWT implementation
- ❌ Manual password hashing
- ❌ Manual email verification

### **Sesudah (Supabase Auth)**
- ✅ Table: `auth.users` (built-in) + `public.user_profiles` (extended)
- ✅ Automatic JWT tokens
- ✅ Built-in password hashing
- ✅ Built-in email verification
- ✅ OAuth providers support
- ✅ Row Level Security (RLS)

## 📋 Prerequisites

1. **Supabase Account**: Buat account di [supabase.com](https://supabase.com)
2. **Supabase Project**: Buat project baru di Supabase dashboard
3. **Environment Variables**: Siapkan credentials Supabase

## 🚀 Setup Steps

### 1. Buat Supabase Project

1. Login ke [Supabase Dashboard](https://app.supabase.com)
2. Klik "New Project"
3. Pilih organization dan isi detail project:
   - **Name**: `personal-web` (atau nama yang Anda inginkan)
   - **Database Password**: Buat password yang kuat
   - **Region**: Pilih region terdekat (Singapore untuk Indonesia)
4. Tunggu project selesai dibuat (~2 menit)

### 2. Dapatkan Credentials

Setelah project dibuat, buka **Settings > API**:

1. **Project URL**: Copy URL project Anda
2. **Anon Key**: Copy anon/public key
3. **Service Role Key**: Copy service role key (untuk admin operations)

### 3. Update Environment Variables

Update file `.env.local`:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

### 4. Install Dependencies

```bash
pnpm install
```

### 5. Setup Database Schema

Jalankan script setup untuk membuat tables:

```bash
pnpm setup-supabase
```

Script ini akan:
- Membuat semua tables (users, posts, post_types, post_images)
- Membuat indexes untuk performa
- Insert default post types
- Setup foreign key relationships

### 6. Verifikasi Setup

1. Buka Supabase Dashboard > Table Editor
2. Pastikan tables berikut sudah ada:
   - `user_profiles` (extends auth.users)
   - `posts`
   - `post_types`
   - `post_images`

3. Buka **Authentication > Users** untuk melihat built-in auth system

## 🔄 Migration dari Prisma (Opsional)

Jika Anda sudah punya data di Prisma/PostgreSQL:

### 1. Export Data dari Prisma

```bash
# Export users
npx prisma db execute --file scripts/export-users.sql

# Export posts
npx prisma db execute --file scripts/export-posts.sql
```

### 2. Import ke Supabase

Gunakan Supabase Dashboard atau SQL Editor untuk import data.

## 🔐 Row Level Security (RLS)

Supabase menggunakan RLS untuk security. Setup policies:

### 1. Enable RLS

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_images ENABLE ROW LEVEL SECURITY;
```

### 2. Create Policies

```sql
-- Public read access for published posts
CREATE POLICY "Public posts are viewable by everyone" ON posts
FOR SELECT USING (status = 'PUBLISHED');

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
FOR SELECT USING (auth.uid() = id::text);

-- Post types are readable by everyone
CREATE POLICY "Post types are viewable by everyone" ON post_types
FOR SELECT USING (true);
```

## 🧪 Testing

Test koneksi Supabase:

```bash
# Test basic connection
node -e "
const { createClient } = require('@supabase/supabase-js');
const client = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
client.from('post_types').select('*').then(console.log);
"
```

## 📚 Next Steps

Setelah setup selesai:

1. **Tahap 2**: Migrate API endpoints dari Prisma ke Supabase
2. **Tahap 3**: Update authentication dengan Supabase Auth
3. **Tahap 4**: Update frontend untuk menggunakan Supabase client

## 🔧 Troubleshooting

### Error: "Missing Supabase environment variables"

- Pastikan semua environment variables sudah diset
- Restart development server setelah update .env.local

### Error: "relation does not exist"

- Jalankan `pnpm setup-supabase` untuk membuat tables
- Periksa di Supabase Dashboard apakah tables sudah ada

### Error: "insufficient_privilege"

- Pastikan menggunakan service role key untuk admin operations
- Periksa RLS policies jika ada masalah akses data

## 📞 Support

Jika ada masalah, check:
1. [Supabase Documentation](https://supabase.com/docs)
2. [Supabase Discord](https://discord.supabase.com)
3. Project issues di GitHub
