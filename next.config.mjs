import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    domains: ['picsum.photos'], // Add your image domains here if needed
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  webpack: (config, { dev, isServer }) => {
    // Optimize webpack caching in development
    if (dev) {
      config.cache = {
        type: 'filesystem',
        compression: 'gzip',
        maxAge: 172800000, // 2 days
      }
    }

    // Production optimizations
    if (!dev) {
      // Enable tree shaking
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: true,
      }
    }

    return config
  },
  experimental: {
    webpackBuildWorker: true,
    optimizeCss: true, // Enable CSS optimization
    optimizePackageImports: ['@radix-ui/react-*', 'lucide-react'], // Optimize large package imports
  },
  // Enable compression
  compress: true,
  // Enable production source maps for better debugging
  productionBrowserSourceMaps: false,
}

export default nextConfig
