// Test script to create post with images
const API_BASE = 'http://localhost:3000'

// Mock image data (simulating what frontend would send)
const mockImageData = [
  {
    url: 'https://example.com/image1.jpg',
    altText: 'Test image 1 alt text',
    caption: 'Test image 1 caption',
    name: 'image1.jpg',
    size: 123456
  },
  {
    url: 'https://example.com/image2.png',
    altText: 'Test image 2 alt text',
    caption: 'Test image 2 caption',
    name: 'image2.png',
    size: 234567
  }
]

// Test post data
const testPostData = {
  title: 'Test Post with Images',
  slug: 'test-post-with-images-' + Date.now(),
  content: 'This is a test post with images. It should create records in both posts and post_images tables.',
  typeId: '3c686fa8-a62d-4cec-b4cc-59609109c55d', // Photo type ID from previous API call
  status: 'DRAFT',
  featured: false,
  showFullContent: false,
  tags: ['test', 'images'],
  images: mockImageData
}

async function testCreatePost() {
  try {
    console.log('🧪 Testing create post with images...')
    console.log('📤 Sending data:', {
      ...testPostData,
      images: testPostData.images.map(img => ({
        name: img.name,
        size: img.size,
        hasAltText: !!img.altText,
        hasCaption: !!img.caption
      }))
    })

    // First, get auth token (you'll need to replace this with actual token)
    const authToken = 'YOUR_AUTH_TOKEN_HERE' // Replace with actual token

    const response = await fetch(`${API_BASE}/api/dashboard/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      credentials: 'include',
      body: JSON.stringify(testPostData)
    })

    const result = await response.json()

    console.log('📥 Response status:', response.status)
    console.log('📥 Response data:', result)

    if (response.ok) {
      console.log('✅ Post created successfully!')
      
      // Check if images were created
      const checkImagesResponse = await fetch(`${API_BASE}/api/test/supabase/tables`)
      const tablesData = await checkImagesResponse.json()
      
      console.log('🗄️ Post images table count:', tablesData.tables.find(t => t.name === 'post_images')?.count)
      
      if (tablesData.tables.find(t => t.name === 'post_images')?.count > 0) {
        console.log('✅ Images were created in database!')
      } else {
        console.log('❌ Images were NOT created in database!')
      }
    } else {
      console.log('❌ Failed to create post:', result)
    }

  } catch (error) {
    console.error('💥 Error testing create post:', error)
  }
}

// Instructions for running this test
console.log(`
🧪 Test Create Post with Images

To run this test:

1. Get auth token:
   - Login to dashboard: http://localhost:3001/admin-access
   - Open browser console
   - Run: localStorage.getItem('auth_token')
   - Copy the token

2. Replace 'YOUR_AUTH_TOKEN_HERE' in this script with the actual token

3. Run the script:
   node scripts/test-create-post-with-images.js

4. Check the console output for results

Expected behavior:
✅ Post created successfully
✅ Images created in post_images table
✅ Console logs show image processing steps
`)

// Uncomment to run the test (after setting auth token)
// testCreatePost()
