const BASE_URL = 'http://localhost:3001'

async function testAuthAndSlug() {
  console.log('🧪 Testing Authentication and Slug Validation...\n')

  try {
    // Step 1: Login
    console.log('1️⃣ Testing login...')
    const loginResponse = await fetch(`${BASE_URL}/api/auth/supabase/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123456'
      })
    })

    const loginResult = await loginResponse.json()
    console.log('Login response status:', loginResponse.status)
    console.log('Login result:', loginResult)

    if (!loginResponse.ok || !loginResult.success) {
      console.error('❌ Login failed:', loginResult.message || 'Unknown error')
      return
    }

    const accessToken = loginResult.data?.session?.access_token
    if (!accessToken) {
      console.error('❌ No access token received')
      return
    }

    console.log('✅ Login successful!')
    console.log('🔑 Access token:', accessToken.substring(0, 20) + '...')

    // Step 2: Test slug validation with auth
    console.log('\n2️⃣ Testing slug validation with authentication...')
    
    const testCases = [
      {
        name: 'Valid new slug',
        slug: 'test-auth-slug-' + Date.now(),
        expectedAvailable: true
      },
      {
        name: 'Existing slug',
        slug: 'css-grid-layout-tip',
        expectedAvailable: false
      },
      {
        name: 'Invalid format',
        slug: 'Invalid_Slug_123!',
        expectedError: true
      }
    ]

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.name}`)
      console.log(`   Slug: "${testCase.slug}"`)

      const slugResponse = await fetch(`${BASE_URL}/api/dashboard/posts/check-slug?slug=${encodeURIComponent(testCase.slug)}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        }
      })

      const slugResult = await slugResponse.json()
      console.log(`   Status: ${slugResponse.status}`)
      console.log(`   Response:`, slugResult)

      if (testCase.expectedError) {
        if (!slugResponse.ok) {
          console.log(`   ✅ Expected error received`)
        } else {
          console.log(`   ❌ Expected error but got success`)
        }
      } else {
        if (slugResponse.ok) {
          const isAvailable = slugResult.data?.available
          if (isAvailable === testCase.expectedAvailable) {
            console.log(`   ✅ Expected availability: ${testCase.expectedAvailable}, Got: ${isAvailable}`)
          } else {
            console.log(`   ❌ Expected availability: ${testCase.expectedAvailable}, Got: ${isAvailable}`)
          }
        } else {
          console.log(`   ❌ Unexpected error: ${slugResult.message}`)
        }
      }
    }

    // Step 3: Test without auth token
    console.log('\n3️⃣ Testing slug validation without authentication...')
    const noAuthResponse = await fetch(`${BASE_URL}/api/dashboard/posts/check-slug?slug=test-no-auth`)
    const noAuthResult = await noAuthResponse.json()
    
    console.log(`Status: ${noAuthResponse.status}`)
    console.log(`Response:`, noAuthResult)
    
    if (noAuthResponse.status === 401) {
      console.log('✅ Correctly rejected request without authentication')
    } else {
      console.log('❌ Should have rejected request without authentication')
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testAuthAndSlug()
