# 🚀 Personal Website & Portfolio

A modern, responsive personal website built with Next.js 15, featuring a blog, portfolio showcase, and professional experience timeline. Designed with a clean, professional aesthetic and optimized for both light and dark themes.

![Website Preview](https://via.placeholder.com/800x400/22c55e/ffffff?text=Personal+Website+Preview)

## ✨ Features

### 🎨 **Modern Design**
- **Responsive Layout** - Optimized for all devices (mobile, tablet, desktop)
- **Dark/Light Theme** - Seamless theme switching with system preference detection
- **Green Accent Theme** - Professional color scheme with green highlights
- **Smooth Animations** - Subtle transitions and hover effects throughout

### 📝 **Content Management**
- **Blog System** - Full-featured blog with post types, categories, and tags
- **Portfolio Showcase** - Interactive project gallery with case studies
- **Experience Timeline** - Professional journey with detailed role descriptions
- **Dynamic Stats** - Real-time statistics and metrics display

### 🛠 **Technical Features**
- **Server-Side Rendering** - Fast loading with Next.js SSR
- **TypeScript** - Full type safety and better developer experience
- **Database Integration** - PostgreSQL with Prisma ORM
- **Search Functionality** - Command palette for quick navigation
- **SEO Optimized** - Meta tags, structured data, and performance optimized

## 🏗 **Tech Stack**

### **Frontend**
- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + Custom Components
- **Icons**: Lucide React
- **Theme**: next-themes

### **Backend & Database**
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **API**: Next.js API Routes (RESTful)
- **Validation**: Zod
- **Real-time**: Supabase Realtime (optional)

### **Development Tools**
- **Package Manager**: pnpm
- **Code Quality**: ESLint + Prettier
- **Git Hooks**: Husky
- **Environment**: Docker Compose

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+
- pnpm (recommended) or npm
- Supabase account and project
- Git

### **Installation**

1. **Clone the repository**
```bash
git clone https://github.com/rijalsolahudin/my-personal-web.git
cd my-personal-web
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Environment setup**
```bash
cp .env.example .env
```

4. **Configure environment variables**
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Authentication & Security
JWT_SECRET="your-secret-key"

# Optional: External APIs
GITHUB_TOKEN="your-github-token"
```

5. **Supabase setup**
```bash
# Setup Supabase schema and data
pnpm supabase:setup

# Seed post types (optional)
pnpm supabase:seed-types

# Seed dummy data (optional)
pnpm supabase:seed-data
```

6. **Start development server**
```bash
pnpm dev
```

Visit `http://localhost:3000` to see your website! 🎉

## 📁 **Project Structure**

```
├── app/                    # Next.js App Router pages
│   ├── (routes)/          # Route groups
│   ├── api/               # API endpoints
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── cards/            # Card components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   ├── navigation/       # Navigation components
│   └── ui/               # Base UI components
├── lib/                  # Utility libraries
│   ├── constants/        # App constants
│   ├── services/         # Business logic
│   └── utils/            # Helper functions
├── prisma/               # Database schema & migrations
├── public/               # Static assets
├── repositories/         # Data access layer
├── services/             # Service layer
├── types/                # TypeScript definitions
└── hooks/                # Custom React hooks
```

## 🎯 **Key Pages**

### **🏠 Homepage**
- Hero section with professional introduction
- Featured blog posts carousel
- Recent posts with filtering
- Sidebar with profile, experience, and portfolio cards

### **📝 Blog**
- Post listing with search and filtering
- Individual post pages with rich content
- Post types: Learning, Tips, Showcases, Opinions
- Tags and categories system

### **💼 Portfolio**
- Project showcase with live demos
- Detailed case studies
- Technology stack highlights
- Performance metrics

### **👨‍💻 Experience**
- Professional timeline
- Role descriptions and achievements
- Skills and technologies
- Career statistics

## 🔧 **Development**

### **Available Scripts**
```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server

# Supabase
pnpm supabase:setup   # Setup Supabase schema
pnpm supabase:test    # Test Supabase connection
pnpm supabase:seed-types    # Seed post types
pnpm supabase:seed-data     # Seed dummy data
pnpm supabase:test-posts    # Test posts API
pnpm supabase:test-auth     # Test authentication

# Code Quality
pnpm lint             # Run ESLint
```

### **Supabase Management**
```bash
# Test connection
pnpm supabase:test

# Create admin user
pnpm supabase:create-admin

# View database
# Use Supabase Dashboard at https://app.supabase.com
```

## 🎨 **Customization**

### **Theme Configuration**
Edit `tailwind.config.js` to customize colors, fonts, and spacing:

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0fdf4',
          500: '#22c55e',
          900: '#14532d',
        }
      }
    }
  }
}
```

### **Content Management**
- **Profile Data**: Update `lib/services/data-service.ts`
- **Blog Posts**: Add to database via API or Prisma Studio
- **Portfolio Projects**: Configure in `lib/services/data-service.ts`

## 🚀 **Deployment**

### **Vercel (Recommended)**
1. Push to GitHub
2. Connect repository to Vercel
3. Configure environment variables
4. Deploy automatically

### **Docker**
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 📊 **Performance**

- **Lighthouse Score**: 95+ (Performance, Accessibility, SEO)
- **Core Web Vitals**: Optimized for LCP, FID, CLS
- **Bundle Size**: Optimized with Next.js automatic code splitting
- **Images**: Optimized with Next.js Image component

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 **Author**

**Rijal Solahudin**
- Website: [rijalsolahudin.dev](https://rijalsolahudin.dev)
- GitHub: [@rijalsolahudin](https://github.com/rijalsolahudin)
- LinkedIn: [Rijal Solahudin](https://linkedin.com/in/rijalsolahudin)

## 🙏 **Acknowledgments**

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Radix UI](https://radix-ui.com/) - Unstyled UI components
- [Prisma](https://prisma.io/) - Database toolkit
- [Lucide](https://lucide.dev/) - Beautiful icons

---

⭐ **Star this repository if you found it helpful!**
