#!/usr/bin/env tsx

import { config } from 'dotenv'
import { runSupabaseMigration, checkSupabaseSchema } from '../lib/supabase/migration'

// Load environment variables
config({ path: '.env.local' })

async function setupSupabase() {
  console.log('🔧 Setting up Supabase database...')

  // Check if required environment variables are set
  const requiredEnvs = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]

  const missingEnvs = requiredEnvs.filter(env => !process.env[env])

  if (missingEnvs.length > 0) {
    console.error('❌ Missing required environment variables:')
    missingEnvs.forEach(env => console.error(`   - ${env}`))
    console.error('\nPlease check your .env.local file and make sure all Supabase credentials are set.')
    process.exit(1)
  }

  console.log('✅ Environment variables loaded successfully')
  console.log(`   - Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL}`)
  console.log(`   - Anon Key: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...`)
  console.log(`   - Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20)}...`)

  try {
    // Check current schema
    console.log('\n📋 Checking current schema...')
    const schemaStatus = await checkSupabaseSchema()

    console.log('Schema status:', schemaStatus)

    if (schemaStatus.userProfiles && schemaStatus.posts && schemaStatus.postTypes) {
      console.log('✅ All tables already exist!')
      console.log('🎉 Supabase setup is complete!')
      return
    }

    // Run migration (will output SQL for manual execution)
    console.log('🚀 Generating migration SQL...')
    const result = await runSupabaseMigration()

    if (result.success) {
      console.log('\n🔄 After executing the SQL in Supabase Dashboard, you can run:')
      console.log('   pnpm setup-supabase')
      console.log('   to verify that all tables were created successfully.')
    } else {
      console.error('❌ Setup failed:', result.error)
      process.exit(1)
    }

  } catch (error) {
    console.error('❌ Setup error:', error)
    process.exit(1)
  }
}

// Run the setup
setupSupabase()
