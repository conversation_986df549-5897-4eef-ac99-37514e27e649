#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createServerSupabaseAdminClient } from '../lib/supabase/server'

// Load environment variables
config({ path: '.env.local' })

async function testSupabaseAPI() {
  console.log('🧪 Testing Supabase API connections...')
  
  try {
    const supabase = createServerSupabaseAdminClient()
    
    // Test 1: Get post types
    console.log('\n1️⃣ Testing post_types table...')
    const { data: postTypes, error: postTypesError } = await supabase
      .from('post_types')
      .select('*')
      .limit(5)
    
    if (postTypesError) {
      console.error('❌ Post types error:', postTypesError)
    } else {
      console.log(`✅ Post types: Found ${postTypes.length} records`)
      postTypes.forEach(type => {
        console.log(`   - ${type.name} (${type.slug})`)
      })
    }
    
    // Test 2: Get user profiles
    console.log('\n2️⃣ Testing user_profiles table...')
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(5)
    
    if (profilesError) {
      console.error('❌ User profiles error:', profilesError)
    } else {
      console.log(`✅ User profiles: Found ${profiles.length} records`)
      profiles.forEach(profile => {
        console.log(`   - ${profile.name} (${profile.id})`)
      })
    }
    
    // Test 3: Get posts
    console.log('\n3️⃣ Testing posts table...')
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('*')
      .limit(5)
    
    if (postsError) {
      console.error('❌ Posts error:', postsError)
    } else {
      console.log(`✅ Posts: Found ${posts.length} records`)
      posts.forEach(post => {
        console.log(`   - ${post.title} (${post.status})`)
      })
    }
    
    console.log('\n🎉 Supabase API test completed!')
    
  } catch (error) {
    console.error('❌ Test error:', error)
    process.exit(1)
  }
}

// Run the test
testSupabaseAPI()
