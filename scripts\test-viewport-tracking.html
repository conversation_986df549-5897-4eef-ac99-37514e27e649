<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Viewport View Tracking</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .link {
            display: inline-block;
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link:hover {
            background-color: #005a87;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .feature-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>👁️ Test: Viewport View Tracking</h1>
    
    <div class="container">
        <h2>📋 Overview</h2>
        <p>Sistem view tracking otomatis yang merekam view ketika post card muncul di viewport:</p>
        
        <div class="feature-list">
            <div class="feature-item">
                <strong>👁️ Viewport Detection</strong><br>
                Intersection Observer API untuk detect visibility
            </div>
            <div class="feature-item">
                <strong>⏱️ Delayed Recording</strong><br>
                1.5 detik delay sebelum record view
            </div>
            <div class="feature-item">
                <strong>📊 60% Threshold</strong><br>
                60% dari card harus visible
            </div>
            <div class="feature-item">
                <strong>🔄 Auto Fallback</strong><br>
                Fallback ke simple system jika full system gagal
            </div>
            <div class="feature-item">
                <strong>🎯 Once Per Session</strong><br>
                Hanya trigger sekali per session
            </div>
            <div class="feature-item">
                <strong>📱 Performance Optimized</strong><br>
                Efficient dengan Intersection Observer
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Test Instructions</h2>
        
        <div class="test-case">
            <h3>Test 1: Homepage Viewport Tracking</h3>
            <ol>
                <li>Open homepage dan scroll perlahan</li>
                <li>Perhatikan console browser (F12)</li>
                <li>Lihat log "Post entered viewport" dan "View recorded"</li>
                <li>Check view count bertambah setelah 1.5 detik</li>
            </ol>
            <a href="http://localhost:3000" class="link" target="_blank">Test Homepage</a>
        </div>

        <div class="test-case">
            <h3>Test 2: Featured Posts Tracking</h3>
            <ol>
                <li>Scroll ke section Featured Posts</li>
                <li>Scroll horizontal untuk lihat featured posts</li>
                <li>Setiap post yang muncul akan auto-record view</li>
                <li>Check API untuk verify view counts</li>
            </ol>
            <a href="http://localhost:3000#featured" class="link" target="_blank">Test Featured Posts</a>
        </div>

        <div class="test-case">
            <h3>Test 3: API Verification</h3>
            <ol>
                <li>Check system status</li>
                <li>Verify view counts increasing</li>
                <li>Test fallback system working</li>
            </ol>
            <a href="http://localhost:3000/api/test/post-interactions-simple" class="link" target="_blank">System Status</a>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Technical Implementation</h2>
        
        <div class="code">
// Viewport Tracking Hook
const { ref: viewTrackingRef } = usePostViewTracking({
  postId: post.id,
  threshold: 0.6, // 60% visible
  delay: 1500, // 1.5 second delay
  enabled: true
})

// PostCard with tracking
&lt;Card ref={viewTrackingRef} className="..."&gt;
  {/* Post content */}
&lt;/Card&gt;
        </div>

        <h3>🔄 Flow Process:</h3>
        <ol>
            <li><strong>Card Renders</strong> - PostCard component mounts</li>
            <li><strong>Observer Setup</strong> - Intersection Observer attached</li>
            <li><strong>Viewport Enter</strong> - 60% of card becomes visible</li>
            <li><strong>Delay Timer</strong> - Wait 1.5 seconds</li>
            <li><strong>API Call</strong> - Try full system, fallback to simple</li>
            <li><strong>View Recorded</strong> - Count updated in database</li>
            <li><strong>Once Per Session</strong> - No more triggers for same post</li>
        </ol>
    </div>

    <div class="container">
        <h2>📊 Expected Behavior</h2>
        
        <div class="test-case">
            <h3>✅ What Should Happen:</h3>
            <ul>
                <li>View count increases when scrolling through posts</li>
                <li>Console shows "Post entered viewport" messages</li>
                <li>Console shows "View recorded successfully" messages</li>
                <li>Each post only triggers once per session</li>
                <li>API calls work (full system or fallback)</li>
                <li>No performance issues during scrolling</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>❌ What Should NOT Happen:</h3>
            <ul>
                <li>Multiple view records for same post in one session</li>
                <li>View recording on quick scroll (less than 1.5s visible)</li>
                <li>Performance lag during scrolling</li>
                <li>API errors causing crashes</li>
                <li>View recording when less than 60% visible</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Debug Information</h2>
        
        <div class="warning">
            <h3>🛠️ Debug Steps:</h3>
            <ol>
                <li><strong>Open Browser Console</strong> (F12 → Console)</li>
                <li><strong>Enable Verbose Logging</strong> - Look for view tracking logs</li>
                <li><strong>Monitor Network Tab</strong> - Check API calls</li>
                <li><strong>Test Slow Scroll</strong> - Scroll slowly to trigger properly</li>
                <li><strong>Check Database</strong> - Verify records in post_views table</li>
            </ol>
        </div>

        <div class="success">
            <h3>✅ Success Indicators:</h3>
            <ul>
                <li>Console: "📊 Recording view for post: [post-id]"</li>
                <li>Console: "✅ View recorded successfully for post: [post-id]"</li>
                <li>Network: POST requests to /api/posts/[id]/view</li>
                <li>UI: View counts increasing in real-time</li>
                <li>Database: New records in post_views table</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎯 Performance Benefits</h2>
        
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Feature</th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Old Method</th>
                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">New Method</th>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 10px;">View Trigger</td>
                <td style="border: 1px solid #ddd; padding: 10px;">❌ Manual page visit only</td>
                <td style="border: 1px solid #ddd; padding: 10px;">✅ Automatic viewport detection</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 10px;">Performance</td>
                <td style="border: 1px solid #ddd; padding: 10px;">❌ No scroll tracking</td>
                <td style="border: 1px solid #ddd; padding: 10px;">✅ Efficient Intersection Observer</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 10px;">User Experience</td>
                <td style="border: 1px solid #ddd; padding: 10px;">❌ Views only on detail page</td>
                <td style="border: 1px solid #ddd; padding: 10px;">✅ Views on list browsing</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 10px;">Analytics</td>
                <td style="border: 1px solid #ddd; padding: 10px;">❌ Limited engagement data</td>
                <td style="border: 1px solid #ddd; padding: 10px;">✅ Better engagement tracking</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>🚀 Ready to Test!</h2>
        <p>Sistem viewport tracking sudah siap ditest. Buka homepage dan scroll untuk melihat view tracking bekerja secara otomatis!</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000" class="link" target="_blank" style="font-size: 18px; padding: 15px 30px;">🏠 Start Testing Homepage</a>
        </div>
    </div>
</body>
</html>
