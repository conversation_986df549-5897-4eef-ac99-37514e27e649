"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useTheme } from "next-themes"
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>lette, <PERSON>, ThumbsUp, <PERSON> } from "lucide-react"

export function ThemeDemo() {
  const { theme } = useTheme()

  return (
    <div className="space-y-6 p-6">
      <Card className="theme-transition">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5 text-green-500" />
            Theme Showcase
          </CardTitle>
          <CardDescription>
            Current theme: <Badge variant="outline" className="ml-2">{theme || 'system'}</Badge>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Color Palette */}
          <div>
            <h3 className="text-sm font-medium mb-3">Color Palette</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="space-y-2">
                <div className="w-full h-12 bg-background border rounded-md"></div>
                <p className="text-xs text-muted-foreground">Background</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-card border rounded-md"></div>
                <p className="text-xs text-muted-foreground">Card</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-muted border rounded-md"></div>
                <p className="text-xs text-muted-foreground">Muted</p>
              </div>
              <div className="space-y-2">
                <div className="w-full h-12 bg-green-500 rounded-md"></div>
                <p className="text-xs text-muted-foreground">Green Accent</p>
              </div>
            </div>
          </div>

          {/* Typography */}
          <div>
            <h3 className="text-sm font-medium mb-3">Typography</h3>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Heading 1</h1>
              <h2 className="text-xl font-semibold">Heading 2</h2>
              <h3 className="text-lg font-medium">Heading 3</h3>
              <p className="text-base">Regular paragraph text</p>
              <p className="text-sm text-muted-foreground">Muted text</p>
            </div>
          </div>

          {/* Components */}
          <div>
            <h3 className="text-sm font-medium mb-3">Components</h3>
            <div className="flex flex-wrap gap-3">
              <Button>Primary Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost Button</Button>
              <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white">
                Green Gradient
              </Button>
            </div>
          </div>

          {/* Form Elements */}
          <div>
            <h3 className="text-sm font-medium mb-3">Form Elements</h3>
            <div className="space-y-3">
              <Input placeholder="Enter some text..." />
              <div className="flex gap-2">
                <Badge>Default</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge className="bg-green-500 hover:bg-green-600">Green</Badge>
              </div>
            </div>
          </div>

          {/* Thread-like Component */}
          <div>
            <h3 className="text-sm font-medium mb-3">Thread Example</h3>
            <Card className="theme-transition">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">AT</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">Alex Thompson</span>
                        <Badge variant="outline" className="text-xs">learning</Badge>
                      </div>
                      <h4 className="font-semibold mb-2">Building a Real-time Chat App with Next.js</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        Today I built a real-time chat application using Next.js and Socket.io. Here's what I learned about implementing WebSocket connections...
                      </p>

                      {/* Sample Image Container */}
                      <div className="mb-3">
                        <div className="relative overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 h-32 theme-transition">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-gray-500 dark:text-gray-400 text-sm">Sample Image Container</span>
                          </div>
                          {/* Sample overlay for multiple images */}
                          <div className="absolute top-2 right-2">
                            <div className="bg-gray-900/60 dark:bg-black/60 px-2 py-1 rounded text-white text-xs theme-transition">
                              +3 more
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <button className="flex items-center gap-1 hover:text-green-500 transition-colors">
                          <ThumbsUp className="w-4 h-4" />
                          <span>24</span>
                        </button>
                        <button className="flex items-center gap-1 hover:text-green-500 transition-colors">
                          <Heart className="w-4 h-4" />
                          <span>18</span>
                        </button>
                        <button className="flex items-center gap-1 hover:text-green-500 transition-colors">
                          <Brain className="w-4 h-4" />
                          <span>12</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Theme Icons */}
          <div>
            <h3 className="text-sm font-medium mb-3">Theme Icons & Switcher</h3>
            <div className="space-y-4">
              <div className="flex gap-4">
                <div className="flex items-center gap-2">
                  <Sun className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm">Light Mode</span>
                </div>
                <div className="flex items-center gap-2">
                  <Moon className="w-5 h-5 text-blue-500" />
                  <span className="text-sm">Dark Mode</span>
                </div>
                <div className="flex items-center gap-2">
                  <Monitor className="w-5 h-5 text-gray-500" />
                  <span className="text-sm">System</span>
                </div>
              </div>

              {/* Theme Switcher Demo */}
              <div className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg theme-transition">
                <span className="text-sm text-muted-foreground">Theme Switcher Button:</span>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Light Mode:</span>
                  <div className="w-9 h-9 border border-gray-300 bg-gray-100/50 rounded-md flex items-center justify-center">
                    <Palette className="w-4 h-4 text-gray-600" />
                  </div>
                  <span className="text-xs text-muted-foreground">Dark Mode:</span>
                  <div className="w-9 h-9 border border-gray-700 bg-gray-800/50 rounded-md flex items-center justify-center">
                    <Palette className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              </div>

              {/* Hover Effects Demo */}
              <div className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg theme-transition">
                <span className="text-sm text-muted-foreground">Thread Card Hover:</span>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Light:</span>
                  <div className="w-16 h-12 border border-green-500/50 bg-gray-100/50 rounded-md flex items-center justify-center">
                    <span className="text-xs text-green-500">Hover</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Dark:</span>
                  <div className="w-16 h-12 border border-green-500/70 bg-gray-800/50 rounded-md flex items-center justify-center">
                    <span className="text-xs text-green-400">Hover</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
