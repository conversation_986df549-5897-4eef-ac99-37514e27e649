import { NextRequest } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Check specific tables we care about
    const tablesToCheck = ['posts', 'post_images', 'post_types', 'user_profiles']
    const tableDetails = []

    for (const tableName of tablesToCheck) {
      try {
        // Try to query the table to see if it exists
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })

        if (error) {
          tableDetails.push({
            name: tableName,
            exists: false,
            error: error.message,
            count: 0
          })
        } else {
          tableDetails.push({
            name: tableName,
            exists: true,
            count: count || 0,
            error: null
          })
        }
      } catch (err) {
        tableDetails.push({
          name: tableName,
          exists: false,
          error: err instanceof Error ? err.message : 'Unknown error',
          count: 0
        })
      }
    }

    // Specifically check post_images table structure
    let postImagesStructure = null
    try {
      const { data: sampleData, error: sampleError } = await supabase
        .from('post_images')
        .select('*')
        .limit(1)

      if (!sampleError && sampleData) {
        postImagesStructure = {
          exists: true,
          sampleRecord: sampleData[0] || null,
          columns: sampleData[0] ? Object.keys(sampleData[0]) : []
        }
      } else {
        postImagesStructure = {
          exists: false,
          error: sampleError?.message || 'Table not found'
        }
      }
    } catch (err) {
      postImagesStructure = {
        exists: false,
        error: err instanceof Error ? err.message : 'Unknown error'
      }
    }

    return Response.json({
      success: true,
      tables: tableDetails,
      postImagesDetails: postImagesStructure,
      message: `Checked ${tablesToCheck.length} tables`
    })

  } catch (error) {
    console.error('Error checking Supabase tables:', error)
    return Response.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
