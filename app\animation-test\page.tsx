"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function AnimationTestPage() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Animation Test Page
        </h1>

        <div className="space-y-4">
          <Button onClick={() => setIsOpen(!isOpen)}>
            Toggle Test Animation
          </Button>

          {/* Test our custom animation */}
          {isOpen && (
            <div
              className="w-48 h-32 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-4"
              style={{
                animation: isOpen ? 'premium-dropdown-enter 300ms cubic-bezier(0.16, 1, 0.3, 1)' : 'premium-dropdown-exit 200ms cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <p className="text-sm text-gray-700 dark:text-gray-300">
                This should animate with our custom premium animation
              </p>
            </div>
          )}

          {/* Test with CSS classes */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              CSS Class Test
            </h2>

            <div
              className={`w-48 h-32 bg-blue-100 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md shadow-lg p-4 transition-all duration-300 ${
                isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-96'
              }`}
            >
              <p className="text-sm text-blue-700 dark:text-blue-300">
                This uses regular CSS transitions
              </p>
            </div>
          </div>

          {/* Test with data attributes */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Data Attribute Test
            </h2>

            <div
              data-state={isOpen ? "open" : "closed"}
              className="w-48 h-32 bg-green-100 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md shadow-lg p-4 animate-in"
            >
              <p className="text-sm text-green-700 dark:text-green-300">
                This should use our data-state animation override
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
