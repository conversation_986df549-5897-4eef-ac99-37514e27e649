import React from 'react'

interface FlagIconProps {
  className?: string
  size?: number
}

export const USFlag: React.FC<FlagIconProps> = ({ className = "", size = 20 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 18"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="18" fill="#B22234"/>
    <rect width="24" height="1.38" y="1.38" fill="white"/>
    <rect width="24" height="1.38" y="4.15" fill="white"/>
    <rect width="24" height="1.38" y="6.92" fill="white"/>
    <rect width="24" height="1.38" y="9.69" fill="white"/>
    <rect width="24" height="1.38" y="12.46" fill="white"/>
    <rect width="24" height="1.38" y="15.23" fill="white"/>
    <rect width="9.6" height="9.69" fill="#3C3B6E"/>
    <g fill="white">
      <circle cx="2" cy="2" r="0.3"/>
      <circle cx="4" cy="2" r="0.3"/>
      <circle cx="6" cy="2" r="0.3"/>
      <circle cx="8" cy="2" r="0.3"/>
      <circle cx="3" cy="3" r="0.3"/>
      <circle cx="5" cy="3" r="0.3"/>
      <circle cx="7" cy="3" r="0.3"/>
      <circle cx="2" cy="4" r="0.3"/>
      <circle cx="4" cy="4" r="0.3"/>
      <circle cx="6" cy="4" r="0.3"/>
      <circle cx="8" cy="4" r="0.3"/>
      <circle cx="3" cy="5" r="0.3"/>
      <circle cx="5" cy="5" r="0.3"/>
      <circle cx="7" cy="5" r="0.3"/>
      <circle cx="2" cy="6" r="0.3"/>
      <circle cx="4" cy="6" r="0.3"/>
      <circle cx="6" cy="6" r="0.3"/>
      <circle cx="8" cy="6" r="0.3"/>
      <circle cx="3" cy="7" r="0.3"/>
      <circle cx="5" cy="7" r="0.3"/>
      <circle cx="7" cy="7" r="0.3"/>
      <circle cx="2" cy="8" r="0.3"/>
      <circle cx="4" cy="8" r="0.3"/>
      <circle cx="6" cy="8" r="0.3"/>
      <circle cx="8" cy="8" r="0.3"/>
    </g>
  </svg>
)

export const IDFlag: React.FC<FlagIconProps> = ({ className = "", size = 20 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 18"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="9" fill="#FF0000"/>
    <rect width="24" height="9" y="9" fill="white"/>
  </svg>
)

interface FlagProps {
  locale: string
  className?: string
  size?: number
}

export const Flag: React.FC<FlagProps> = ({ locale, className = "", size = 20 }) => {
  switch (locale) {
    case 'en':
      return <USFlag className={className} size={size} />
    case 'id':
      return <IDFlag className={className} size={size} />
    default:
      return (
        <svg
          width={size}
          height={size}
          viewBox="0 0 24 18"
          className={className}
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="24" height="18" fill="#f0f0f0" stroke="#ccc" strokeWidth="1"/>
          <text x="12" y="12" textAnchor="middle" fontSize="8" fill="#666">?</text>
        </svg>
      )
  }
}
