"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Icon, IconName } from "@/components/ui/icon"

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="w-9 h-9 p-0 border-gray-300 bg-gray-100/50 hover:bg-gray-200/50 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50 backdrop-blur-sm theme-transition"
      >
        <Icon name="Palette" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    )
  }

  const getThemeIcon = (): IconName => {
    switch (theme) {
      case "light":
        return "Sun"
      case "dark":
        return "Moon"
      case "system":
        return "Monitor"
      default:
        return "Palette"
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="w-9 h-9 p-0 border-gray-300 bg-gray-100/50 hover:bg-gray-200/50 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50 backdrop-blur-sm theme-transition"
        >
          <Icon name={getThemeIcon()} />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Icon name="Sun" className="mr-2 h-4 w-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Icon name="Moon" className="mr-2 h-4 w-4" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <Icon name="Monitor" className="mr-2 h-4 w-4" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Alternative compact toggle button (for mobile or minimal UI)
export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="w-9 h-9 p-0 border-gray-300 bg-gray-100/50 dark:border-gray-700 dark:bg-gray-800/50 theme-transition"
      >
        <Icon name="Palette" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    )
  }

  const toggleTheme = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleTheme}
      className="w-9 h-9 p-0 border-gray-300 text-gray-600 hover:text-green-500 hover:border-green-500 bg-gray-100/50 dark:border-gray-700 dark:text-gray-400 dark:hover:text-green-400 dark:hover:border-green-500 dark:bg-gray-800/50 backdrop-blur-sm transition-all duration-200 theme-transition"
    >
      <Icon name={getThemeIcon()} />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )

  function getThemeIcon(): IconName {
    switch (theme) {
      case "light":
        return "Sun"
      case "dark":
        return "Moon"
      case "system":
        return "Monitor"
      default:
        return "Palette"
    }
  }
}
