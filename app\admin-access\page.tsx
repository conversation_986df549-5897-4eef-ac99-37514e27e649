"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LoginForm } from '@/components/auth/login-form'
import { useAuth } from '@/contexts/auth-context'
import { Loader2 } from 'lucide-react'

export default function AdminAccessPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      // Get redirect URL from query params or default to dashboard
      const urlParams = new URLSearchParams(window.location.search)
      const redirectUrl = urlParams.get('redirect') || '/dashboard'

      console.log('Already authenticated, redirecting to:', redirectUrl)

      // Use window.location for immediate redirect
      setTimeout(() => {
        window.location.href = redirectUrl
      }, 100)
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <LoginForm />

        {/* Admin notice */}
        <div className="mt-6 text-center space-y-2">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            🔒 Admin-only access
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500">
            This website does not have public registration. Only authorized administrators can access the dashboard.
          </p>
        </div>
      </div>
    </div>
  )
}
