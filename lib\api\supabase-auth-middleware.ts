import { NextRequest, NextResponse } from 'next/server'
import { ApiResponseBuilder } from './response-builder'
import { getAuthenticatedUserFromRequest, SupabaseUser } from '@/lib/supabase/auth'

/**
 * Supabase authentication middleware for dashboard API endpoints
 */
export function withSupabaseDashboardAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Get authenticated user from Supabase
      const user = await getAuthenticatedUserFromRequest(request)

      if (!user) {
        return ApiResponseBuilder.unauthorized(
          'Authentication required. Please login to access this resource.',
          {
            requiresAuth: true,
            redirectTo: '/admin-access'
          }
        )
      }

      // Add user info to request for downstream handlers
      const requestWithAuth = request.clone()

      // Store user in request context (we'll use a custom property)
      ;(requestWithAuth as any).user = user

      // Call the original handler with authenticated request
      return await handler(requestWithAuth, ...args)
    } catch (error) {
      console.error('Supabase authentication middleware error:', error)

      return ApiResponseBuilder.internalError(
        'Authentication failed. Please login again.',
        {
          requiresAuth: true,
          redirectTo: '/admin-access'
        }
      )
    }
  }
}

/**
 * Extract user information from authenticated request
 */
export function getUserFromSupabaseRequest(request: NextRequest): SupabaseUser | null {
  return (request as any).user || null
}

/**
 * Check if request has valid Supabase authentication
 */
export function isSupabaseAuthenticated(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization')
  return !!(authHeader && authHeader.startsWith('Bearer '))
}

/**
 * Middleware specifically for dashboard API routes with Supabase
 * Combines authentication with other middleware
 */
export function withSupabaseDashboardMiddleware<T extends any[]>(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: (request: NextRequest, ...args: T) => Promise<NextResponse>) => {
    // Apply Supabase authentication first, then other middlewares
    let wrappedHandler = withSupabaseDashboardAuth(handler)

    // Apply other middlewares in reverse order
    for (let i = middlewares.length - 1; i >= 0; i--) {
      // Ensure middleware receives proper parameters
      const middleware = middlewares[i]
      if (typeof middleware === 'function') {
        wrappedHandler = middleware(wrappedHandler)
      }
    }

    return wrappedHandler
  }
}

/**
 * Role-based access control middleware for Supabase
 * Note: For now, we'll treat all authenticated users as admins
 * Later we can add role management to user_profiles table
 */
export function withSupabaseRoleAuth(allowedRoles: string[] = ['admin', 'user']) {
  return function<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      const user = getUserFromSupabaseRequest(request)

      if (!user) {
        return ApiResponseBuilder.unauthorized('Authentication required')
      }

      // For now, all authenticated users are considered admins
      // Later we can add role field to user_profiles table
      const userRole = 'admin' // user.role || 'user'

      if (!allowedRoles.includes(userRole)) {
        return ApiResponseBuilder.forbidden(
          'Insufficient permissions to access this resource.',
          {
            requiredRoles: allowedRoles,
            userRole: userRole
          }
        )
      }

      return await handler(request, ...args)
    }
  }
}

/**
 * Admin-only access middleware for Supabase
 */
export function withSupabaseAdminAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return withSupabaseRoleAuth(['admin'])(handler)
}

/**
 * Optional authentication middleware - doesn't fail if no auth
 * Useful for endpoints that work for both authenticated and anonymous users
 */
export function withOptionalSupabaseAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Try to get authenticated user, but don't fail if not found
      const user = await getAuthenticatedUserFromRequest(request)

      // Add user info to request (will be null if not authenticated)
      const requestWithAuth = request.clone()
      ;(requestWithAuth as any).user = user

      return await handler(requestWithAuth, ...args)
    } catch (error) {
      console.error('Optional Supabase auth middleware error:', error)

      // Continue without authentication
      const requestWithAuth = request.clone()
      ;(requestWithAuth as any).user = null

      return await handler(requestWithAuth, ...args)
    }
  }
}
