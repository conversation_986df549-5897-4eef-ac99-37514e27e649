"use client"

import { useState, useEffect } from "react"
import { Heart, BarChart3 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FloatingReactionsProps {
  reactions: {
    heart: number
    impressions?: number
  }
  onReaction: (type: 'heart') => void
  hasLiked?: boolean
  isLiking?: boolean
  className?: string
}

export function FloatingReactions({
  reactions,
  onReaction,
  hasLiked = false,
  isLiking = false,
  className
}: FloatingReactionsProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show floating reactions when user scrolls past header
      const scrollY = window.scrollY
      setIsVisible(scrollY > 200)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleReactionClick = (type: 'heart') => {
    if (isLiking) return // Prevent multiple clicks while processing
    onReaction(type)
  }

  return (
    <div
      className={cn(
        "fixed left-8 top-1/2 -translate-y-1/2 z-50 transition-all duration-300 ease-in-out",
        isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full pointer-events-none",
        "hidden lg:block", // Only show on desktop
        className
      )}
    >
      <div className="bg-white dark:bg-gray-900 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 p-2 space-y-2 theme-transition">
        {/* Heart Reaction */}
        <div className="flex flex-col items-center relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleReactionClick('heart')}
            disabled={isLiking}
            className={cn(
              "w-12 h-12 rounded-full p-0 transition-all duration-300 hover:scale-110",
              hasLiked
                ? "bg-red-50 dark:bg-red-900/20 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30"
                : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-red-500",
              isLiking && "opacity-50 cursor-not-allowed"
            )}
          >
            <Heart
              className={cn(
                "w-5 h-5 transition-all duration-300",
                hasLiked && "fill-current",
                isLiking && "animate-pulse"
              )}
            />
          </Button>
          {reactions.heart > 0 && (
            <span className="text-xs text-gray-600 dark:text-gray-400 mt-1 font-medium">
              {reactions.heart}
            </span>
          )}
        </div>

        {/* Views/Impressions - Non-clickable */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 rounded-full p-0 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors duration-300">
            <BarChart3 className="w-5 h-5" />
          </div>
          {reactions.impressions && reactions.impressions > 0 && (
            <span className="text-xs text-gray-600 dark:text-gray-400 mt-1 font-medium">
              {reactions.impressions}
            </span>
          )}
        </div>
      </div>

      {/* Tooltip on hover */}
      <div className="absolute left-full ml-4 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-gray-900 dark:bg-gray-700 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          React to this post
        </div>
      </div>
    </div>
  )
}
