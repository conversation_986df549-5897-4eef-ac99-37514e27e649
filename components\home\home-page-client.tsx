"use client"

import { useState } from "react"
import { CommandPalette } from "@/components/navigation/command-palette"

import { PageLayout } from "@/components/layout/page-layout"
import { Head<PERSON> } from "@/components/navigation/header"
import { Sidebar } from "@/components/navigation/sidebar"
import { HeroSection } from "@/components/home/<USER>"
import { FeaturedPosts } from "@/components/home/<USER>"
import { PostsSection } from "@/components/home/<USER>"
import { Post, Profile, PortfolioProject } from "@/types"

interface HomePageClientProps {
  allPosts: Post[]
  featuredPosts: Post[]
  profile: Profile
  portfolio: PortfolioProject[]
  locale: string
  sidebarTranslations: {
    profile: {
      title: string
      status: string
      contact: string
    }
    experience: {
      title: string
      current: string
      years: string
      viewAll: string
    }
    portfolio: {
      title: string
      projects: string
      viewAll: string
    }
    stats: {
      title: string
      posts: string
      views: string
      reactions: string
    }
  }
}

export function HomePageClient({
  allPosts,
  featuredPosts,
  profile,
  portfolio,
  locale,
  sidebarTranslations
}: HomePageClientProps) {
  const [showCommandPalette, setShowCommandPalette] = useState(false)


  return (
    <PageLayout>
      <Header
        onSearchClick={() => setShowCommandPalette(true)}
      />

      <main className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          <Sidebar
            profile={profile}
            posts={allPosts}
            portfolio={portfolio}
            locale={locale}
            translations={sidebarTranslations}
          />

          <div className="lg:col-span-2 space-y-8">
            <HeroSection />
            <FeaturedPosts posts={featuredPosts} locale={locale} />
            <PostsSection posts={allPosts} locale={locale} />
          </div>
        </div>
      </main>

      <CommandPalette
        open={showCommandPalette}
        onOpenChange={setShowCommandPalette}
        posts={allPosts}
      />

    </PageLayout>
  )
}
