import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Heart,
  BarChart3,
  Clock,
  GitBranch,
  Calendar,
  ImageIcon
} from "lucide-react"
import Link from "next/link"
import NextImage from "next/image"
import { Post } from "@/types"
import { POST_TYPE_CONFIG, getPostTypeIcon, getPostTypeClasses, getIconByName } from "@/lib/constants"
import { extractContentPreview } from "@/lib/utils"
import { PostSlideshow } from "@/components/gallery/post-slideshow"
import { useUnifiedViewTracking } from "@/hooks/use-unified-view-tracking"
import { usePostInteractionsFallback } from "@/hooks/use-post-interactions-fallback"

interface PostCardProps {
  post: Post
  variant?: 'compact' | 'full' | 'mobile' | 'featured' | 'card'
  showImages?: boolean
  className?: string
  locale?: string
}

export function PostCard({
  post,
  variant = 'full',
  showImages = true,
  className = '',
  locale = 'en'
}: PostCardProps) {
  // Add unified viewport tracking (prevents duplicate views)
  const { ref: viewTrackingRef } = useUnifiedViewTracking({
    postId: post.id,
    threshold: 0.6, // 60% of card must be visible
    delay: 1500, // Wait 1.5 seconds before recording view
    enabled: true
  })

  // Add like functionality for list posts
  const {
    likeCount: currentLikeCount,
    hasLiked,
    isLiking,
    likePost,
    error: likeError
  } = usePostInteractionsFallback({
    postId: post.id,
    initialLikeCount: post.reactionHart || 0,
    initialViewCount: post.viewCount || 0,
    autoRecordView: false // Disable auto-record, use unified tracking instead
  })

  // Use current like count from hook, fallback to post data
  const displayLikeCount = currentLikeCount || post.reactionHart || 0

  const typeConfig = POST_TYPE_CONFIG[post.type.slug] || POST_TYPE_CONFIG['learning']
  // Use icon from database, fallback to config
  const IconComponent = getIconByName(post.type.icon) || getPostTypeIcon(post.type.slug)
  const typeStyles = getPostTypeClasses(post.type.color)
  // Dynamic content preview length based on image presence for better height balance
  const hasImages = post.images && post.images.length > 0
  const contentLength = variant === 'compact'
    ? (hasImages ? 120 : 300)  // Balanced content length for consistent height
    : variant === 'mobile'
    ? 150  // Shorter content for mobile
    : 200
  const contentPreview = extractContentPreview(post.content, contentLength)

  // Mobile variant - optimized for mobile screens
  if (variant === 'mobile' || variant === 'card' || variant === 'featured') {
    return (
      <Card
        ref={viewTrackingRef as React.RefObject<HTMLDivElement>}
        className={`group bg-white border-gray-200 dark:bg-gray-900 dark:border-gray-800 hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-lg theme-transition ${className}`}
      >
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col gap-3 sm:gap-4">
            {/* Header */}
            <div className="flex items-start justify-between gap-3">
              <Link href={`/${locale}/posts/${post.slug}`} className="flex-1">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 hover:text-green-500 dark:hover:text-green-400 transition-colors duration-200 cursor-pointer theme-transition">
                  {post.title}
                </h3>
              </Link>
              <Badge
                className="shrink-0 border text-xs"
                style={{
                  background: typeStyles.background,
                  color: typeStyles.color,
                  borderColor: typeStyles.borderColor
                }}
              >
                <IconComponent className="w-3 h-3 mr-1" />
                {post.type.name}
              </Badge>
            </div>

            {/* Content Preview */}
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed theme-transition">
              {contentPreview}
            </p>

            {/* Mobile Image Display */}
            {showImages && post.images && post.images.length > 0 && (
              <div className="my-2">
                {post.images.length === 1 ? (
                  <div className="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800 aspect-video">
                    <NextImage
                      src={post.images[0].url || "/placeholder.svg"}
                      alt={post.images[0].alt || "Post image"}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-2">
                    {post.images.slice(0, 4).map((image, index) => (
                      <div
                        key={image.id}
                        className="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800 aspect-video"
                      >
                        <NextImage
                          src={image.url || "/placeholder.svg"}
                          alt={image.alt || `Image ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                        {index === 3 && post.images!.length > 4 && (
                          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                              +{post.images!.length - 4}
                            </span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-3 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{post.readTime}m read</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>{post.updatedAt}</span>
              </div>
              {post.images && post.images.length > 0 && (
                <div className="flex items-center gap-1">
                  <ImageIcon className="w-3 h-3" />
                  <span>{post.images.length} image{post.images.length !== 1 ? "s" : ""}</span>
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200"
                >
                  #{tag}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-500">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-4">
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    likePost()
                  }}
                  disabled={isLiking}
                  className={`flex items-center gap-1.5 px-3 py-1.5 rounded-full transition-all duration-200 ${
                    hasLiked
                      ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                      : 'bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30'
                  } ${isLiking ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}`}
                  title={hasLiked ? 'You liked this post' : 'Like this post'}
                >
                  <Heart
                    className={`w-4 h-4 transition-all duration-200 ${
                      hasLiked ? 'fill-current text-red-500 dark:text-red-400' : ''
                    } ${isLiking ? 'animate-pulse' : ''}`}
                  />
                  <span className="text-sm font-medium">{displayLikeCount}</span>
                </button>
                <div className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-50 dark:bg-blue-900/20 rounded-full">
                  <BarChart3 className="w-4 h-4 text-blue-500 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">{post.viewCount || 0}</span>
                </div>
              </div>
              <Link
                href={`/${locale}/posts/${post.slug}`}
                className="text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors"
              >
                Read more →
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (variant === 'compact') {
    return (
      <Card
        ref={viewTrackingRef as React.RefObject<HTMLDivElement>}
        className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20 h-[320px] w-[480px] flex-shrink-0 flex flex-col theme-transition"
      >
        <CardContent className="p-3 flex flex-col h-full">
          {/* Compact Header */}
          <div className="flex items-start justify-between gap-2 mb-2">
            <Link href={`/${locale}/posts/${post.slug}`} className="flex-1">
              <h3 className="text-base font-semibold text-gray-900 dark:text-white line-clamp-2 hover:text-green-500 dark:hover:text-green-400 transition-colors duration-200 cursor-pointer theme-transition leading-tight">
                {post.title}
              </h3>
            </Link>
            <Badge
              className="shrink-0 text-xs border"
              style={{
                background: typeStyles.background,
                color: typeStyles.color,
                borderColor: typeStyles.borderColor
              }}
            >
              <IconComponent className="w-3 h-3 mr-1" />
              {post.type.name}
            </Badge>
          </div>

          {/* Compact Meta Info */}
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-600 mb-2 theme-transition">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{post.readTime}m</span>
            </div>
            {post.images && post.images.length > 0 && (
              <div className="flex items-center gap-1">
                <ImageIcon className="w-3 h-3" />
                <span>{post.images.length}</span>
              </div>
            )}
          </div>

          {/* Compact Content Preview - Flexible height */}
          <div className="flex-1 mb-2">
            <p className={`text-xs text-gray-600 dark:text-gray-400 leading-relaxed theme-transition ${
              hasImages ? 'line-clamp-3' : 'line-clamp-6'
            }`}>
              {contentPreview}
            </p>
          </div>

          {/* Compact Image Display - Horizontal Layout */}
          {showImages && post.images && post.images.length > 0 && (
            <div className="mb-2">
              {post.images.length === 1 ? (
                <div className="relative overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 w-20 h-20 theme-transition">
                  <NextImage
                    src={post.images[0].url || "/placeholder.svg"}
                    alt={post.images[0].alt || "Post image"}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="flex gap-1">
                  {post.images.slice(0, 4).map((image, index) => (
                    <div
                      key={image.id}
                      className="relative overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 w-16 h-16 theme-transition"
                    >
                      <NextImage
                        src={image.url || "/placeholder.svg"}
                        alt={image.alt || `Image ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                      {index === 3 && post.images!.length > 4 && (
                        <div className="absolute inset-0 bg-gray-900/60 dark:bg-black/60 flex items-center justify-center theme-transition">
                          <span className="text-white text-xs font-medium">
                            +{post.images!.length - 4}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Compact Tags */}
          <div className="flex flex-wrap gap-1 mb-2">
            {post.tags.slice(0, 2).map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200 px-1.5 py-0.5 theme-transition"
              >
                #{tag}
              </Badge>
            ))}
            {post.tags.length > 2 && (
              <Badge variant="outline" className="text-xs border-gray-300 text-gray-600 dark:border-gray-700 dark:text-gray-500 px-1.5 py-0.5 theme-transition">
                +{post.tags.length - 2}
              </Badge>
            )}
          </div>

          {/* Compact Stats */}
          <div className="flex items-center justify-between mt-auto pt-3 border-t border-gray-200/50 dark:border-gray-700/50 theme-transition">
            <div className="flex items-center gap-3">
              <button
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  likePost()
                }}
                disabled={isLiking}
                className={`flex items-center gap-1.5 px-2 py-1 rounded-full transition-all duration-200 ${
                  hasLiked
                    ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                    : 'bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30'
                } ${isLiking ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}`}
                title={hasLiked ? 'You liked this post' : 'Like this post'}
              >
                <Heart
                  className={`w-3 h-3 transition-all duration-200 ${
                    hasLiked ? 'fill-current text-red-500 dark:text-red-400' : ''
                  } ${isLiking ? 'animate-pulse' : ''}`}
                />
                <span className="text-xs font-medium">{displayLikeCount}</span>
              </button>
              <div className="flex items-center gap-1.5 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 rounded-full">
                <BarChart3 className="w-3 h-3 text-blue-500 dark:text-blue-400" />
                <span className="text-xs font-medium text-blue-600 dark:text-blue-400">{post.viewCount || 0}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Full variant
  return (
    <Card
      ref={viewTrackingRef as React.RefObject<HTMLDivElement>}
      className="group bg-white border-gray-200 dark:bg-gray-900/80 dark:border-gray-800/50 backdrop-blur-sm hover:border-green-500/50 dark:hover:border-green-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 dark:hover:shadow-green-500/20 theme-transition"
    >
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-start justify-between gap-3">
            <Link href={`/${locale}/posts/${post.slug}`} className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 hover:text-green-500 dark:hover:text-green-400 transition-colors duration-200 cursor-pointer theme-transition">
                {post.title}
              </h3>
            </Link>
            <Badge
              className="shrink-0 border"
              style={{
                background: typeStyles.background,
                color: typeStyles.color,
                borderColor: typeStyles.borderColor
              }}
            >
              <IconComponent className="w-3 h-3 mr-1" />
              {post.type.name}
            </Badge>
          </div>

          <p className="text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed theme-transition">
            {contentPreview}
          </p>

          {/* Slideshow for multiple images */}
          {showImages && post.images && post.images.length > 1 && (
            <div className="my-4">
              <PostSlideshow images={post.images} autoPlay={false} showThumbnails={true} />
            </div>
          )}

          {/* Single image display */}
          {showImages && post.images && post.images.length === 1 && (
            <div className="my-4">
              <div className="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-800 theme-transition">
                <NextImage
                  src={post.images[0].url || "/placeholder.svg"}
                  alt={post.images[0].alt || "Post image"}
                  width={800}
                  height={400}
                  className="w-full h-auto object-cover"
                />
              </div>
              {post.images[0].caption && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 italic theme-transition">{post.images[0].caption}</p>
              )}
            </div>
          )}

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-500 theme-transition">
            <div className="flex items-center gap-1">
              <GitBranch className="w-3 h-3" />
              <span>{post.version}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{post.readTime}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{post.updatedAt}</span>
            </div>
            {post.images && post.images.length > 0 && (
              <div className="flex items-center gap-1">
                <ImageIcon className="w-3 h-3" />
                <span>
                  {post.images.length} image{post.images.length !== 1 ? "s" : ""}
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-xs border-gray-300 text-gray-600 hover:border-green-500/50 hover:text-green-500 dark:border-gray-700 dark:text-gray-500 dark:hover:border-green-500/70 dark:hover:text-green-400 transition-colors duration-200 theme-transition"
              >
                #{tag}
              </Badge>
            ))}
          </div>

          <div className="flex items-center pt-4 border-t border-gray-200 dark:border-gray-800/50 theme-transition">
            <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-500 theme-transition">
              <button
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  likePost()
                }}
                disabled={isLiking}
                className={`flex items-center gap-1 transition-all duration-200 ${
                  hasLiked
                    ? 'text-red-500 dark:text-red-400'
                    : 'text-gray-600 dark:text-gray-500 hover:text-red-400'
                } ${isLiking ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105'}`}
                title={hasLiked ? 'You liked this post' : 'Like this post'}
              >
                <Heart
                  className={`w-4 h-4 transition-all duration-200 ${
                    hasLiked ? 'fill-current text-red-500 dark:text-red-400' : ''
                  } ${isLiking ? 'animate-pulse' : ''}`}
                />
                <span>{displayLikeCount}</span>
              </button>
              <div className="flex items-center gap-1 text-gray-600 dark:text-gray-500">
                <BarChart3 className="w-4 h-4" />
                <span>{post.viewCount || 0}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
