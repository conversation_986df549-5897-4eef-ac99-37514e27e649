import { NextRequest } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Get user profiles
    const { data: profiles, error } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(10)

    if (error) {
      return Response.json({ 
        error: 'Failed to fetch user profiles', 
        details: error 
      }, { status: 500 })
    }

    return Response.json({
      success: true,
      data: profiles,
      count: profiles?.length || 0,
      message: `Found ${profiles?.length || 0} user profiles`
    })

  } catch (error) {
    console.error('Error fetching user profiles:', error)
    return Response.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
