import { NextRequest } from 'next/server'
import { PostApiSimpleService } from '@/services/post-api-simple.service'
import { ApiResponseBuilder } from '@/lib/api/response-builder'

export async function GET(request: NextRequest) {
  try {
    console.log('🏠 Testing homepage data with Supabase SSR...')

    const postService = new PostApiSimpleService()

    // Get the same data that homepage uses - only published posts
    const [
      allPostsResult,
      featuredPosts
    ] = await Promise.all([
      postService.getPostsByStatus('PUBLISHED', { limit: 20 }),
      postService.getFeaturedPosts(6)
    ])

    console.log('📊 Homepage data results:')
    console.log('- All posts count:', allPostsResult.total)
    console.log('- Featured posts count:', featuredPosts.length)
    console.log('- First post title:', allPostsResult.posts[0]?.title || 'No posts')
    console.log('- First featured post:', featuredPosts[0]?.title || 'No featured posts')

    // Check data structure
    const samplePost = allPostsResult.posts[0]
    const sampleFeatured = featuredPosts[0]

    return ApiResponseBuilder.success(
      {
        allPosts: {
          total: allPostsResult.total,
          count: allPostsResult.posts.length,
          sample: samplePost ? {
            id: samplePost.id,
            title: samplePost.title,
            slug: samplePost.slug,
            status: samplePost.status,
            featured: samplePost.featured,
            author: {
              id: samplePost.author.id,
              name: samplePost.author.name,
              hasAvatar: !!samplePost.author.avatar_url
            },
            type: {
              id: samplePost.type.id,
              name: samplePost.type.name,
              color: samplePost.type.color,
              icon: samplePost.type.icon
            },
            hasImages: samplePost.images?.length > 0,
            tags: samplePost.tags,
            createdAt: samplePost.createdAt
          } : null
        },
        featuredPosts: {
          count: featuredPosts.length,
          sample: sampleFeatured ? {
            id: sampleFeatured.id,
            title: sampleFeatured.title,
            slug: sampleFeatured.slug,
            status: sampleFeatured.status,
            featured: sampleFeatured.featured,
            author: {
              id: sampleFeatured.author.id,
              name: sampleFeatured.author.name,
              hasAvatar: !!sampleFeatured.author.avatar_url
            },
            type: {
              id: sampleFeatured.type.id,
              name: sampleFeatured.type.name,
              color: sampleFeatured.type.color,
              icon: sampleFeatured.type.icon
            },
            hasImages: sampleFeatured.images?.length > 0,
            tags: sampleFeatured.tags,
            createdAt: sampleFeatured.createdAt
          } : null
        },
        dataQuality: {
          allPostsHaveAuthors: <AUTHORS>
          allPostsHaveTypes: allPostsResult.posts.every(p => p.type?.name),
          featuredPostsHaveAuthors: <AUTHORS>
          featuredPostsHaveTypes: featuredPosts.every(p => p.type?.name),
          allFeaturedArePublished: featuredPosts.every(p => p.status === 'PUBLISHED'),
          allFeaturedAreFeatured: featuredPosts.every(p => p.featured === true)
        }
      },
      'Homepage data retrieved successfully with Supabase SSR'
    )

  } catch (error) {
    console.error('💥 Error testing homepage data:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to get homepage data'
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { createFeaturedPost = false } = body

    if (createFeaturedPost) {
      console.log('🌟 Creating a featured post for testing...')
      
      const postService = new PostApiSimpleService()
      
      const featuredPostData = {
        title: 'Featured Test Post - ' + new Date().toISOString(),
        slug: 'featured-test-post-' + Date.now(),
        content: 'This is a featured test post created to test homepage SSR functionality. It should appear in the featured posts section.',
        authorId: '66c32a84-b0b5-4ede-a4f3-2cc919e756b6', // Valid user ID
        typeId: '3c686fa8-a62d-4cec-b4cc-59609109c55d', // Valid post type ID
        status: 'PUBLISHED',
        featured: true, // Make it featured
        excerpt: true,
        tags: ['featured', 'test', 'homepage'],
        images: []
      }

      const createdPost = await postService.createPost(featuredPostData)
      
      return ApiResponseBuilder.success(
        {
          post: createdPost,
          message: 'Featured test post created successfully'
        },
        'Featured post created for homepage testing'
      )
    }

    return ApiResponseBuilder.badRequest('Invalid request. Set createFeaturedPost: true to create a featured post.')

  } catch (error) {
    console.error('💥 Error creating featured post:', error)
    
    return ApiResponseBuilder.internalError(
      error instanceof Error ? error.message : 'Failed to create featured post'
    )
  }
}
