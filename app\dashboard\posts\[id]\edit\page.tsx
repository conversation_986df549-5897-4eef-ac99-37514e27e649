"use client"

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { PostForm } from '@/components/dashboard/forms/post-form'
import { usePostApi } from '@/hooks/use-posts-api'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { Post } from '@/types'
import { Loader2, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function EditPostPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const { isAuthenticated, token, isLoading } = useAuth()
  const postId = params.id as string

  // Fetch post data
  const { post, loading, error, refetch } = usePostApi({
    id: postId,
    autoFetch: !!token // Only fetch if we have token
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  // Redirect to login if not authenticated (but wait for auth loading to complete)
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !token) {
      router.push('/admin-access?redirect=' + encodeURIComponent(window.location.pathname))
    }
  }, [isAuthenticated, token, isLoading, router])

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true)

      // Transform images from UploadedImage format to API format
      const transformedImages = formData.images?.map((img: any) => ({
        id: img.id,
        url: img.url,
        altText: img.altText || '',
        caption: img.caption || '',
        name: img.name || '',
        size: img.size || 0,
        order: 0
      })) || []

      const apiData = {
        ...formData,
        images: transformedImages
      }

      console.log('Updating post:', postId, apiData)

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      // Add Authorization header if token is available
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/dashboard/posts/${postId}`, {
        method: 'PUT',
        headers,
        credentials: 'include',
        body: JSON.stringify(apiData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update post')
      }

      const result = await response.json()
      
      toast({
        title: "Success",
        description: `Post ${formData.status === 'PUBLISHED' ? 'updated and published' : 'updated as draft'} successfully`,
      })

      // Redirect to posts list
      router.push('/dashboard/posts')
      
    } catch (error) {
      console.error('Error updating post:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update post',
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Loading state (auth or post data)
  if (isLoading || loading) {
    return (
      <DashboardLayout
        title="Edit Post"
        description="Loading..."
      >
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-gray-600 dark:text-gray-400">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>{isLoading ? 'Checking authentication...' : 'Loading post...'}</span>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Error state
  if (error || (!loading && !post)) {
    return (
      <DashboardLayout
        title="Edit Post"
        description="Post not found"
      >
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {error?.includes('401') || error?.includes('Authentication') ? 'Authentication Required' : 'Post Not Found'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {error?.includes('401') || error?.includes('Authentication')
                ? 'Please login to access this page.'
                : error || 'The post you are looking for does not exist or you do not have permission to edit it.'
              }
            </p>
            <div className="flex gap-3">
              {error?.includes('401') || error?.includes('Authentication') ? (
                <Button asChild>
                  <Link href="/admin-access">
                    Login
                  </Link>
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => refetch()}
                  className="border-gray-300 dark:border-gray-600"
                >
                  Try Again
                </Button>
              )}
              <Button asChild variant="outline">
                <Link href="/dashboard/posts">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Posts
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Edit Post"
      description={`Editing: ${post?.title || 'Loading...'}`}
    >
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            asChild
            className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
          >
            <Link href="/dashboard/posts">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Posts
            </Link>
          </Button>
          
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Post ID: <span className="font-mono">{postId}</span>
          </div>
        </div>

        {/* Post Form */}
        {post && (
          <PostForm
            mode="edit"
            postId={postId}
            initialData={{
              title: post.title,
              slug: post.slug,
              content: post.content,
              version: post.version,
              typeId: post.type?.id || '',
              status: post.status,
              featured: post.featured || false,
              showFullContent: !post.excerpt, // Invert excerpt to showFullContent
              tags: post.tags || [],
              images: (post.images || []).map((img: any, index: number) => ({
                id: img.id || `existing-${Date.now()}-${index}`,
                url: img.url,
                name: img.name || `image-${index + 1}`,
                size: img.size || 0,
                altText: img.altText || img.alt || '',
                caption: img.caption || '',
                isExisting: true // Mark as existing image
              }))
            }}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        )}
      </div>
    </DashboardLayout>
  )
}
