"use client"

import { useState, useCallback, useEffect } from 'react'

export interface Comment {
  id: string
  postId: string
  author: string
  content: string
  createdAt: string
  updatedAt: string
  reactions: {
    thumbsUp: number
    heart: number
  }
}

export interface CreateCommentData {
  content: string
  author?: string
}

export interface UseCommentsApiOptions {
  postId: string
  autoFetch?: boolean
}

export interface UseCommentsApiReturn {
  comments: Comment[]
  loading: boolean
  error: string | null
  createComment: (data: CreateCommentData) => Promise<void>
  addReaction: (commentId: string, type: 'thumbsUp' | 'heart') => Promise<void>
  refetch: () => Promise<void>
}

/**
 * Hook for managing comments with API integration
 */
export function useCommentsApi({ 
  postId, 
  autoFetch = true 
}: UseCommentsApiOptions): UseCommentsApiReturn {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchComments = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/posts/${postId}/comments`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch comments')
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        setComments(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch comments')
      }
    } catch (err) {
      console.error('Error fetching comments:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch comments')
      
      // Fallback to mock data for development
      setComments([
        {
          id: "1",
          postId,
          author: "Sarah Chen",
          content: "Great tutorial! The implementation is really clean. I especially liked how you handled the connection management.",
          createdAt: "2024-01-16T10:00:00Z",
          updatedAt: "2024-01-16T10:00:00Z",
          reactions: { thumbsUp: 5, heart: 2 }
        },
        {
          id: "2",
          postId,
          author: "Mike Rodriguez",
          content: "This helped me solve a similar issue I was having with real-time updates. Thanks for sharing!",
          createdAt: "2024-01-17T14:30:00Z",
          updatedAt: "2024-01-17T14:30:00Z",
          reactions: { thumbsUp: 3, heart: 1 }
        }
      ])
    } finally {
      setLoading(false)
    }
  }, [postId])

  const createComment = useCallback(async (data: CreateCommentData) => {
    try {
      setError(null)
      
      const response = await fetch(`/api/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      if (!response.ok) {
        throw new Error('Failed to create comment')
      }
      
      const result = await response.json()
      
      if (result.success && result.data) {
        setComments(prev => [...prev, result.data])
      } else {
        throw new Error(result.message || 'Failed to create comment')
      }
    } catch (err) {
      console.error('Error creating comment:', err)
      setError(err instanceof Error ? err.message : 'Failed to create comment')
      
      // Fallback: Add mock comment for development
      const mockComment: Comment = {
        id: Date.now().toString(),
        postId,
        author: data.author || "Current User",
        content: data.content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        reactions: { thumbsUp: 0, heart: 0 }
      }
      setComments(prev => [...prev, mockComment])
    }
  }, [postId])

  const addReaction = useCallback(async (commentId: string, type: 'thumbsUp' | 'heart') => {
    try {
      setError(null)
      
      const response = await fetch(`/api/posts/${postId}/comments/${commentId}/reactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to add reaction')
      }
      
      const result = await response.json()
      
      if (result.success) {
        // Update local state
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, reactions: { ...comment.reactions, [type]: comment.reactions[type] + 1 } }
            : comment
        ))
      } else {
        throw new Error(result.message || 'Failed to add reaction')
      }
    } catch (err) {
      console.error('Error adding reaction:', err)
      setError(err instanceof Error ? err.message : 'Failed to add reaction')
      
      // Fallback: Update local state for development
      setComments(prev => prev.map(comment => 
        comment.id === commentId 
          ? { ...comment, reactions: { ...comment.reactions, [type]: comment.reactions[type] + 1 } }
          : comment
      ))
    }
  }, [postId])

  const refetch = useCallback(async () => {
    await fetchComments()
  }, [fetchComments])

  useEffect(() => {
    if (autoFetch) {
      fetchComments()
    }
  }, [autoFetch, fetchComments])

  return {
    comments,
    loading,
    error,
    createComment,
    addReaction,
    refetch
  }
}
